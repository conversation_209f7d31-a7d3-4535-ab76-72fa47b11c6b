
services:
  pyron-mvp:
    build: ./pyron-mvp
    container_name: pyron-mvp
    ports:
      - "3000:3000"
    env_file:
      - ./pyron-mvp/.env.auth
      - ./pyron-mvp/.env.database
      - ./pyron-mvp/.env.core
      - ./pyron-mvp/.env.blockchain
      - ./pyron-mvp/.env.wallet
    # environment:
    #   - NODE_ENV=production
    restart: unless-stopped
  pyron-webhook:
    build: ./pyron-webhook
    container_name: pyron-webhook
    ports:
      - "3004:3004"
    env_file:
      - ./pyron-webhook/.env.database
      - ./pyron-webhook/.env.admin
      - ./pyron-webhook/.env.server
      - ./pyron-webhook/.env.security
    # environment:
    #   - NODE_ENV=production
    restart: unless-stopped
  pyron-webapp:
    build: ./PyRon-webApp
    container_name: pyron-webapp
    ports:
      - "8080:8080"
    env_file:
      - ./PyRon-webApp/.env
    # environment:
    #   - NODE_ENV=production
    restart: unless-stopped

    depends_on:
      - pyron-mvp
      - pyron-webhook
