# Unit Test Plan

## Framework
- **Test Runner**: Jest
- **Mocking**: Jest mocks for external services (Drift SDK, Solana, MongoDB)
- **Environment**: Isolated test environment with mocked dependencies

## Test Structure
```
tests/
├── unit/
│   ├── controllers/
│   │   ├── agentController.test.ts
│   │   ├── authController.test.ts
│   │   ├── chatController.test.ts
│   │   ├── hypothesisController.test.ts
│   │   ├── logController.test.ts
│   │   └── tradeController.test.ts
│   ├── middleware/
│   │   └── authMiddleware.test.ts
│   └── trade/drift/
│       ├── createClient.test.ts
│       └── getCurrentPrice.test.ts
├── fixtures/mockData.ts
├── setup.ts
└── healthCheck.test.ts
```

## Controllers

### Auth Controller
- JWT token generation/validation
- Refresh token functionality
- Cookie-based token management
- Logout functionality

### Agent Controller
- Agent CRUD operations
- Trading status updates
- Hypothesis status management
- Signal management
- Prompt management

### Chat Controller
- Chat creation and retrieval
- Message management
- Chat metadata handling

### Trade Controller
- Position retrieval
- Account activity fetching
- Asset value calculation
- Drift client integration

### Log Controller
- Log creation and retrieval
- Chat-based log filtering
- Status management

### Hypothesis Controller
- Hypothesis creation
- Log-hypothesis associations
- Chat-based filtering

## Middleware

### Auth Middleware
- JWT token validation
- Wallet ownership verification
- Request authentication flow
- Error handling for invalid tokens

## Trading Functions

### Drift Client (`createClient.test.ts`)
- Client creation and configuration
- Connection error handling
- Authority and delegate setup

### Price Fetching (`getCurrentPrice.test.ts`)
- Market price retrieval
- API error handling
- Price calculation from bid/ask spread

## Mocking Strategy

### External Services
- **Drift SDK**: Mock client creation, user operations, position data
- **Solana Web3**: Mock connection, keypair generation, transactions
- **MongoDB**: Mock model operations (find, save, update, delete)
- **Axios**: Mock HTTP requests for price data

### Test Data
- Mock agents, chats, logs, hypotheses
- Valid/invalid data sets for validation testing
- Error scenarios for edge case testing

## Test Configuration

### Jest Setup
- TypeScript support via ts-jest
- Mock external dependencies
- Coverage reporting
- 10-second timeout for async operations

### Environment
- Mocked external services (no real network calls)
- Test-specific environment variables
- Isolated test database mocking

## Test Execution

### Commands
```bash
# Run all tests
npm test

# Run unit tests only
npm run test:unit

# Run specific test file
npx jest tests/unit/controllers/agentController.test.ts
npx jest tests/unit/controllers/authController.test.ts
npx jest tests/unit/controllers/chatController.test.ts
npx jest tests/unit/controllers/tradeController.test.ts
npx jest tests/unit/controllers/logController.test.ts
npx jest tests/unit/controllers/hypothesisController.test.ts
npx jest tests/unit/middleware/authMiddleware.test.ts
npx jest tests/unit/trade/drift/createClient.test.ts
npx jest tests/unit/trade/drift/getCurrentPrice.test.ts
npx jest tests/healthCheck.test.ts

# Run specific test by name pattern
npx jest --testNamePattern="should create agent"
npx jest --testNamePattern="Auth Controller"
npx jest --testNamePattern="createClient"

```

