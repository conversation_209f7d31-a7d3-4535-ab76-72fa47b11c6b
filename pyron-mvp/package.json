{"name": "pyron-mvp", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration", "start": "ts-node main.ts"}, "author": "", "license": "ISC", "dependencies": {"@crossmint/wallets-sdk": "^0.4.4", "@drift-labs/sdk": "^2.111.0-beta.3", "@lightprotocol/stateless.js": "^0.20.9", "@solana/spl-token": "^0.4.9", "@solana/web3.js": "^1.98.0", "@sqds/multisig": "^2.1.3", "@types/body-parser": "^1.19.5", "@types/cookie-parser": "^1.4.8", "@types/jsonwebtoken": "^9.0.9", "@types/mongoose": "^5.11.97", "async-mutex": "^0.5.0", "body-parser": "^1.20.3", "bs58": "^6.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "express": "^4.21.2", "jito-ts": "^4.2.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.9.2", "rpc-websockets": "7.10.0", "solana-agent-kit": "1.2.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.2", "@types/supertest": "^6.0.3", "jest": "^29.7.0", "supertest": "^7.1.0", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.6.3"}}