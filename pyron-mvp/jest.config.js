module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/tests'],
  testMatch: [
    '**/__tests__/**/*.ts',
    '**/?(*.)+(spec|test).ts'
  ],
  transform: {
    '^.+\\.ts$': 'ts-jest',
  },
  collectCoverageFrom: [
    'controller/**/*.ts',
    'middleware/**/*.ts',
    'databaseModels/**/*.ts',
    'trade/**/*.ts',
    'utils/**/*.ts',
    '!**/*.d.ts',
    '!**/node_modules/**',
    '!**/tests/**'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
  moduleFileExtensions: ['ts', 'js', 'json'],
  testTimeout: 10000,
  clearMocks: true,
  resetMocks: true,
  restoreMocks: true,
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/$1',
    '^axios$': '<rootDir>/tests/__mocks__/axios.ts',
    '^@drift-labs/sdk$': '<rootDir>/tests/__mocks__/drift-sdk.ts',
    '^@solana/web3.js$': '<rootDir>/tests/__mocks__/solana-web3.ts',
    '^mongoose$': '<rootDir>/tests/__mocks__/mongoose.ts'
    // Removed jsonwebtoken mock to allow real JWT functionality in tests
  }
};
