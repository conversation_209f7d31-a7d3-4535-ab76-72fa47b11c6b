# Trading Bot System

A Node.js REST API system for managing trading agents with Solana/Drift Protocol integration.

## Features

- **REST API**: Endpoints for managing agents, users, trades, chats, logs, and hypotheses
- **JWT Authentication**: Secure cookie-based authentication with access and refresh tokens
- **MongoDB Integration**: Data persistence for agents, users, chats, and logs
- **Drift Protocol**: Solana-based trading operations and position management
- **Chat System**: Message management and chat functionality
- **Agent Management**: CRUD operations for trading agent configurations


## Installation

```bash
npm install
```

## Testing

Tests use **Jest** with mocked external services (no database or network calls required).

```bash
npm test
```

See [unit-test-plan.md](unit-test-plan.md) for detailed test structure and commands.

## Running the Application

### Prerequisites
1. **Node.js**: Version 14 or higher
2. **Solana Wallet**: With SOL for transaction fees (for trading operations)
3. **MongoDB**: Set up a running MongoDB instance
4. **Environment Files**: Create the required environment files


### Environment Configuration
Create multiple environment files (loaded in order):

**`.env.auth`** (required):
```plaintext
JWT_SECRET=your-secret-key
```

**`.env.database`** (required):
```plaintext
MONGO_HOST=localhost
MONGO_USER=your-username
MONGO_PASSWORD=your-password
MONGO_DB=your-database
MONGO_PORT=27017
```

**`.env.core`** (optional):
```plaintext
PORT=3000
NODE_ENV=development
```

**`.env.wallet`** (optional - for trading features):
```plaintext
ADMIN_KEY=your-solana-private-key
```

**`.env.blockchain`** (optional - for trading features):
```plaintext
RPC_URL=https://api.devnet.solana.com
```

### Start the Server
```bash
npm start
```

The server starts on the configured port (default: 3000).

## Project Structure

- **/controller**: API route controllers (auth, chat, agent, trade, user, log, hypothesis)
- **/routers**: Express route definitions
- **/trade**: Trading logic with Drift Protocol integration and Jito bundling
- **/utils**: Utility functions for keypairs, market data, and Jupiter swaps
- **/databaseModels**: MongoDB schemas
- **/middleware**: JWT authentication middleware
- **/types**: TypeScript type definitions
- **/tests**: Jest unit tests with mocked dependencies

## API Endpoints

### Authentication
- `POST /api/auth/token`: Generate access token and set refresh token cookie
- `POST /api/auth/refresh`: Refresh access token using cookie-based refresh token
- `POST /api/auth/logout`: Logout user and clear refresh token cookie

### Agents
- `POST /api/agents/add-agent`: Create a new trading agent
- `GET /api/agents/get-agentById/:id`: Retrieve an agent by ID
- `GET /api/agents/get-all-agents`: Retrieve all agents
- `DELETE /api/agents/delete-agent/:id`: Delete an agent
- `PUT /api/agents/update-trading-status/:botId`: Update trading status
- `PUT /api/agents/save-or-update-agent/:botId`: Save or update an agent

### Users
- `POST /api/users/add-user`: Add a new user
- `GET /api/users/wallet/:walletAddress`: Get user profile by wallet address
- `PATCH /api/users/wallet/:walletAddress`: Update user profile

### Chats
- `POST /api/chats`: Create a new chat
- `GET /api/chats/wallet/:walletAddress`: Get all chats for a wallet
- `GET /api/chats/:id`: Get a specific chat
- `PATCH /api/chats/:id`: Update a chat
- `DELETE /api/chats/:id`: Delete a chat
- `POST /api/chats/:id/messages`: Add a message to a chat
- `GET /api/chats/:id/messages`: Get messages for a chat

### Trading
- `GET /api/trade/get-drift-position`: Get current trading position
- `GET /api/trade/get-user-activity`: Retrieve trading activity
- `GET /api/trade/get-user-assets`: Get account assets
- `GET /api/trade/get-user-positions`: Get account positions

### Logs
- `GET /api/logs/getAll`: Retrieve all trading logs
- `POST /api/logs/add-log`: Create a new log
- `PUT /api/logs/:id`: Update a log by ID
- `DELETE /api/logs/:id`: Delete a log by ID

### Hypothesis
- `POST /api/hypothesis/create`: Create a new hypothesis
- `GET /api/hypothesis/by-log-id/:logId`: Get hypotheses by log ID
- `GET /api/hypothesis/getAll`: Get all hypotheses

### Health
- `HEAD /api/health`: Health check endpoint

## Authentication System

The system uses secure, cookie-based JWT authentication:

- **Access Token**: Short-lived , sent in response body for API authorization
- **Refresh Token**: Long-lived , stored in httpOnly cookie for token renewal
- **Security**: HttpOnly cookies, CSRF protection, automatic expiration
