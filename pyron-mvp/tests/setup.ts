// Set essential environment variables for all tests
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key';
process.env.MONGO_USER = 'test';
process.env.MONGO_PASSWORD = 'test';
process.env.MONGO_HOST = 'localhost';
process.env.MONGO_DB = 'pyron_test';
process.env.MONGO_PORT = '27017';
process.env.ADMIN_KEY = 'test-admin-key-for-testing-only';
process.env.RPC_URL = 'https://api.mainnet-beta.solana.com';

// Global test setup
beforeEach(() => {
  // Clear all mocks before each test
  jest.clearAllMocks();
  jest.resetAllMocks();
});

// Global test teardown
afterEach(() => {
  // Clean up any test-specific state
  jest.restoreAllMocks();
});

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Mock timers for consistent testing
jest.useFakeTimers();

// Set up global test timeout
jest.setTimeout(10000);
