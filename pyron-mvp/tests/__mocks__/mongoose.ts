// Mock Mongoose
const mockDocument = {
  save: jest.fn().mockResolvedValue({}),
  remove: jest.fn().mockResolvedValue({}),
  deleteOne: jest.fn().mockResolvedValue({ deletedCount: 1 }),
  updateOne: jest.fn().mockResolvedValue({ modifiedCount: 1 }),
  populate: jest.fn().mockReturnThis(),
  exec: jest.fn().mockResolvedValue({}),
  lean: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  sort: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
  skip: jest.fn().mockReturnThis(),
  validateSync: jest.fn().mockReturnValue(undefined),
  toObject: jest.fn().mockReturnValue({}),
  toJSON: jest.fn().mockReturnValue({})
};

const mockModel = {
  find: jest.fn().mockReturnValue({
    ...mockDocument,
    exec: jest.fn().mockResolvedValue([])
  }),
  findOne: jest.fn().mockReturnValue({
    ...mockDocument,
    exec: jest.fn().mockResolvedValue(null)
  }),
  findById: jest.fn().mockReturnValue({
    ...mockDocument,
    exec: jest.fn().mockResolvedValue(null)
  }),
  findByIdAndUpdate: jest.fn().mockReturnValue({
    ...mockDocument,
    exec: jest.fn().mockResolvedValue(null)
  }),
  findByIdAndDelete: jest.fn().mockReturnValue({
    ...mockDocument,
    exec: jest.fn().mockResolvedValue(null)
  }),
  findOneAndUpdate: jest.fn().mockReturnValue({
    ...mockDocument,
    exec: jest.fn().mockResolvedValue(null)
  }),
  findOneAndDelete: jest.fn().mockReturnValue({
    ...mockDocument,
    exec: jest.fn().mockResolvedValue(null)
  }),
  create: jest.fn().mockResolvedValue(mockDocument),
  insertMany: jest.fn().mockResolvedValue([mockDocument]),
  updateOne: jest.fn().mockResolvedValue({ modifiedCount: 1 }),
  updateMany: jest.fn().mockResolvedValue({ modifiedCount: 1 }),
  deleteOne: jest.fn().mockResolvedValue({ deletedCount: 1 }),
  deleteMany: jest.fn().mockResolvedValue({ deletedCount: 1 }),
  countDocuments: jest.fn().mockResolvedValue(0),
  distinct: jest.fn().mockResolvedValue([]),
  aggregate: jest.fn().mockReturnValue({
    exec: jest.fn().mockResolvedValue([])
  })
};

const mockSchema = jest.fn().mockImplementation(() => ({
  add: jest.fn(),
  pre: jest.fn(),
  post: jest.fn(),
  methods: {},
  statics: {},
  virtual: jest.fn().mockReturnValue({
    get: jest.fn(),
    set: jest.fn()
  }),
  index: jest.fn(),
  plugin: jest.fn()
}));

// Add Schema.Types to the mock
(mockSchema as any).Types = {
  ObjectId: jest.fn(),
  String: jest.fn(),
  Number: jest.fn(),
  Date: jest.fn(),
  Buffer: jest.fn(),
  Boolean: jest.fn(),
  Mixed: jest.fn(),
  Array: jest.fn(),
  Decimal128: jest.fn(),
  Map: jest.fn()
};

const mockConnection = {
  readyState: 1,
  close: jest.fn().mockResolvedValue(undefined),
  dropDatabase: jest.fn().mockResolvedValue(undefined),
  on: jest.fn(),
  once: jest.fn(),
  removeListener: jest.fn()
};

const mongoose = {
  connect: jest.fn().mockResolvedValue(mockConnection),
  disconnect: jest.fn().mockResolvedValue(undefined),
  connection: mockConnection,
  model: jest.fn().mockReturnValue(mockModel),
  Schema: mockSchema,
  Types: {
    ObjectId: jest.fn().mockImplementation((id?: string) => ({
      toString: jest.fn().mockReturnValue(id || '507f1f77bcf86cd799439011'),
      toHexString: jest.fn().mockReturnValue(id || '507f1f77bcf86cd799439011'),
      equals: jest.fn().mockReturnValue(true)
    }))
  },
  isValidObjectId: jest.fn().mockReturnValue(true),
  startSession: jest.fn().mockResolvedValue({
    startTransaction: jest.fn(),
    commitTransaction: jest.fn().mockResolvedValue(undefined),
    abortTransaction: jest.fn().mockResolvedValue(undefined),
    endSession: jest.fn().mockResolvedValue(undefined)
  })
};

// Add static methods to ObjectId
(mongoose.Types.ObjectId as any).isValid = jest.fn().mockReturnValue(true);
(mongoose.Types.ObjectId as any).createFromHexString = jest.fn().mockImplementation((hex: string) =>
  new mongoose.Types.ObjectId(hex)
);

export default mongoose;
export const { connect, disconnect, connection, model, Schema, Types, isValidObjectId, startSession } = mongoose;
