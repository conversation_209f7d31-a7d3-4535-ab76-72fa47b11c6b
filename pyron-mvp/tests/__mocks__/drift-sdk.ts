// Mock Drift SDK
export const DriftClient = jest.fn().mockImplementation(() => ({
  subscribe: jest.fn().mockResolvedValue(true),
  unsubscribe: jest.fn().mockResolvedValue(true),
  getUser: jest.fn().mockReturnValue({
    getUserAccount: jest.fn().mockReturnValue({
      authority: 'mock-authority',
      subAccountId: 0,
      positions: []
    }),
    getTokenAmount: jest.fn().mockReturnValue(1000),
    getTotalAssetValue: jest.fn().mockResolvedValue(1000),
    getPerpPosition: jest.fn().mockReturnValue({
      baseAssetAmount: 100,
      quoteAssetAmount: 1000,
      unrealizedPnl: 50
    })
  }),
  placeOrder: jest.fn().mockResolvedValue({
    txSig: 'mock-transaction-signature',
    slot: 12345
  }),
  cancelOrder: jest.fn().mockResolvedValue({
    txSig: 'mock-cancel-signature',
    slot: 12346
  }),
  getMarketAccountAndSlot: jest.fn().mockReturnValue({
    data: {
      baseAssetReserve: 1000000,
      quoteAssetReserve: 1000000,
      sqrtK: 1000000
    },
    slot: 12345
  }),
  getOracleDataForPerpMarket: jest.fn().mockReturnValue({
    price: *********, // 100 USDC in micro units
    confidence: 1000,
    hasSufficientNumberOfDataPoints: true
  })
}));

export const Wallet = jest.fn().mockImplementation(() => ({
  publicKey: 'mock-public-key',
  signTransaction: jest.fn().mockResolvedValue('mock-signed-transaction'),
  signAllTransactions: jest.fn().mockResolvedValue(['mock-signed-transaction'])
}));

export const initialize = jest.fn().mockResolvedValue({
  driftClient: new DriftClient(),
  wallet: new Wallet()
});

export const getMarketsAndOraclesForSubscription = jest.fn().mockReturnValue({
  perpMarketIndexes: [0, 1, 2],
  spotMarketIndexes: [0, 1, 2],
  oracleInfos: []
});

export const convertToNumber = jest.fn().mockImplementation((value: any) => {
  if (typeof value === 'number') return value;
  if (typeof value === 'string') return parseFloat(value);
  return 0;
});

export const PRICE_PRECISION = 1000000;
export const BASE_PRECISION = 1000000;
export const QUOTE_PRECISION = 1000000;

export const MarketType = {
  PERP: 'perp',
  SPOT: 'spot'
};

export const OrderType = {
  MARKET: 'market',
  LIMIT: 'limit'
};

export const PositionDirection = {
  LONG: 'long',
  SHORT: 'short'
};

export default {
  DriftClient,
  Wallet,
  initialize,
  getMarketsAndOraclesForSubscription,
  convertToNumber,
  PRICE_PRECISION,
  BASE_PRECISION,
  QUOTE_PRECISION,
  MarketType,
  OrderType,
  PositionDirection
};
