// Mock Solana Web3.js
export const Connection = jest.fn().mockImplementation(() => ({
  getAccountInfo: jest.fn().mockResolvedValue({
    data: Buffer.from('mock-account-data'),
    executable: false,
    lamports: 1000000,
    owner: 'mock-owner',
    rentEpoch: 123
  }),
  getBalance: jest.fn().mockResolvedValue(1000000),
  getBlockHeight: jest.fn().mockResolvedValue(12345),
  getSlot: jest.fn().mockResolvedValue(12345),
  sendTransaction: jest.fn().mockResolvedValue('mock-transaction-signature'),
  confirmTransaction: jest.fn().mockResolvedValue({
    value: { err: null }
  }),
  getLatestBlockhash: jest.fn().mockResolvedValue({
    blockhash: 'mock-blockhash',
    lastValidBlockHeight: 12345
  })
}));

export const PublicKey = jest.fn().mockImplementation((key?: string) => ({
  toString: jest.fn().mockReturnValue(key || '********************************'),
  toBase58: jest.fn().mockReturnValue(key || '********************************'),
  equals: jest.fn().mockReturnValue(true)
}));

// Add static methods to PublicKey
(PublicKey as any).isOnCurve = jest.fn().mockReturnValue(true);
(PublicKey as any).createWithSeed = jest.fn().mockResolvedValue(new PublicKey());
(PublicKey as any).createProgramAddress = jest.fn().mockResolvedValue(new PublicKey());
(PublicKey as any).findProgramAddress = jest.fn().mockResolvedValue([new PublicKey(), 255]);

export const Keypair = {
  generate: jest.fn().mockReturnValue({
    publicKey: new PublicKey(),
    secretKey: new Uint8Array(64),
    _keypair: {
      publicKey: new Uint8Array(32),
      secretKey: new Uint8Array(64)
    }
  }),
  fromSecretKey: jest.fn().mockReturnValue({
    publicKey: new PublicKey(),
    secretKey: new Uint8Array(64)
  }),
  fromSeed: jest.fn().mockReturnValue({
    publicKey: new PublicKey(),
    secretKey: new Uint8Array(64)
  })
};

export const Transaction = jest.fn().mockImplementation(() => ({
  add: jest.fn().mockReturnThis(),
  sign: jest.fn(),
  serialize: jest.fn().mockReturnValue(Buffer.from('mock-serialized-transaction')),
  signatures: [],
  feePayer: null,
  recentBlockhash: null
}));

export const TransactionInstruction = jest.fn().mockImplementation(() => ({
  keys: [],
  programId: new PublicKey(),
  data: Buffer.from('mock-instruction-data')
}));

export const SystemProgram = {
  createAccount: jest.fn().mockReturnValue(new TransactionInstruction()),
  transfer: jest.fn().mockReturnValue(new TransactionInstruction()),
  assign: jest.fn().mockReturnValue(new TransactionInstruction()),
  createAccountWithSeed: jest.fn().mockReturnValue(new TransactionInstruction()),
  createNonceAccount: jest.fn().mockReturnValue(new TransactionInstruction()),
  nonceAdvance: jest.fn().mockReturnValue(new TransactionInstruction()),
  nonceWithdraw: jest.fn().mockReturnValue(new TransactionInstruction()),
  nonceAuthorize: jest.fn().mockReturnValue(new TransactionInstruction()),
  allocate: jest.fn().mockReturnValue(new TransactionInstruction()),
  allocateWithSeed: jest.fn().mockReturnValue(new TransactionInstruction()),
  programId: new PublicKey('11111111111111111111111111111111')
};

export const LAMPORTS_PER_SOL = **********;

export const clusterApiUrl = jest.fn().mockReturnValue('https://api.mainnet-beta.solana.com');

export default {
  Connection,
  PublicKey,
  Keypair,
  Transaction,
  TransactionInstruction,
  SystemProgram,
  LAMPORTS_PER_SOL,
  clusterApiUrl
};
