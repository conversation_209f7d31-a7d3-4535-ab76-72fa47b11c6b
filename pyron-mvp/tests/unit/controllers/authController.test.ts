import { Request, Response } from 'express';
import jwt from 'jsonwebtoken';

// Import controller - no need to mock JWT for basic functionality tests
import { generateAuthToken, refreshToken, logout } from '../../../controller/authController';

describe('Auth Controller', () => {
  let req: Partial<Request>;
  let res: Partial<Response>;
  let mockStatus: jest.Mock;
  let mockJson: jest.Mock;
  let mockCookie: jest.Mock;
  let mockClearCookie: jest.Mock;
  let validRefreshToken: string;
  let expiredRefreshToken: string;

  beforeEach(() => {
    // Generate real JWT tokens for testing
    validRefreshToken = jwt.sign(
      { walletAddress: 'test-wallet', type: 'refresh' },
      process.env.JWT_SECRET!,
      { expiresIn: '7d' }
    );

    expiredRefreshToken = jwt.sign(
      { walletAddress: 'test-wallet', type: 'refresh' },
      process.env.JWT_SECRET!,
      { expiresIn: '-1s' } // Already expired
    );

    req = {
      body: {},
      cookies: {}
    };

    mockJson = jest.fn();
    mockStatus = jest.fn().mockReturnValue({ json: mockJson });
    mockCookie = jest.fn();
    mockClearCookie = jest.fn();

    res = {
      status: mockStatus,
      json: mockJson,
      cookie: mockCookie,
      clearCookie: mockClearCookie
    };
  });



  describe('generateAuthToken', () => {
    it('should generate tokens for any valid wallet address', async () => {
      req.body = { walletAddress: 'test-wallet' };

      await generateAuthToken(req as Request, res as Response);

      expect(mockStatus).toHaveBeenCalledWith(200);
      expect(mockJson).toHaveBeenCalledWith({
        token: expect.any(String),
        expiresIn: 3600
      });
      expect(mockCookie).toHaveBeenCalled();
    });

    it('should return 400 if wallet address is missing', async () => {
      req.body = {};

      await generateAuthToken(req as Request, res as Response);

      expect(mockStatus).toHaveBeenCalledWith(400);
      expect(mockJson).toHaveBeenCalledWith({ message: 'Wallet address is required' });
    });

    it('should set httpOnly refresh token cookie', async () => {
      req.body = { walletAddress: 'test-wallet' };

      await generateAuthToken(req as Request, res as Response);

      expect(mockCookie).toHaveBeenCalledWith('refresh_token', expect.any(String), {
        httpOnly: true,
        secure: false, // NODE_ENV is test in setup
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000,
        path: '/'
      });
    });
  });

  describe('refreshToken', () => {
    it('should generate new access token with valid refresh token', async () => {
      req.cookies = { refresh_token: validRefreshToken };

      await refreshToken(req as Request, res as Response);

      expect(mockStatus).toHaveBeenCalledWith(200);
      expect(mockJson).toHaveBeenCalledWith({
        token: expect.any(String),
        expiresIn: 3600
      });
    });

    it('should return 401 if refresh token is missing', async () => {
      req.cookies = {};

      await refreshToken(req as Request, res as Response);

      expect(mockStatus).toHaveBeenCalledWith(401);
      expect(mockJson).toHaveBeenCalledWith({ message: 'Refresh token not found' });
    });

    it('should return 401 if token is expired', async () => {
      req.cookies = { refresh_token: expiredRefreshToken };

      await refreshToken(req as Request, res as Response);

      expect(mockStatus).toHaveBeenCalledWith(401);
      expect(mockJson).toHaveBeenCalledWith({ message: 'Refresh token expired' });
    });

    it('should return 401 if token is invalid', async () => {
      req.cookies = { refresh_token: 'invalid-token' };

      await refreshToken(req as Request, res as Response);

      expect(mockStatus).toHaveBeenCalledWith(401);
      expect(mockJson).toHaveBeenCalledWith({ message: 'Invalid refresh token' });
    });


  });

  describe('logout', () => {
    it('should clear refresh token cookie', async () => {
      req.cookies = { refresh_token: 'some-token' };

      await logout(req as Request, res as Response);

      expect(mockClearCookie).toHaveBeenCalledWith('refresh_token', {
        httpOnly: true,
        secure: false,
        sameSite: 'strict',
        path: '/'
      });
    });

    it('should return success response', async () => {
      req.cookies = { refresh_token: 'some-token' };

      await logout(req as Request, res as Response);

      expect(mockStatus).toHaveBeenCalledWith(200);
      expect(mockJson).toHaveBeenCalledWith({ success: true });
    });

    it('should handle missing refresh token gracefully', async () => {
      req.cookies = {};

      await logout(req as Request, res as Response);

      expect(mockStatus).toHaveBeenCalledWith(200);
      expect(mockJson).toHaveBeenCalledWith({ success: true });
    });
  });
});
