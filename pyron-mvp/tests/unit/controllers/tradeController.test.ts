import { Request, Response } from 'express';

// Mock external services before importing the controller
jest.mock('../../../databaseModels/agent');
jest.mock('../../../trade/drift/getClient');
jest.mock('../../../trade/drift/getPerpPrice');
jest.mock('../../../utils/getMarketId');
jest.mock('../../../utils/createKeypairFromSecretKey');
jest.mock('@solana/web3.js');
jest.mock('bn.js');
jest.mock('@drift-labs/sdk', () => ({
  AMM_RESERVE_PRECISION_EXP: { toNumber: () => 9 },
  QUOTE_PRECISION: 1000000
}));

// Import controller after mocking dependencies
import { getPosition, getAccountActivity, getAccountAssets, getAccountPositions } from '../../../controller/tradeController';
import Agent from '../../../databaseModels/agent';
import { createClient } from '../../../trade/drift/getClient';
import { getCurrentPrice } from '../../../trade/drift/getPerpPrice';
import { getMarketId } from '../../../utils/getMarketId';
import { createKeypairFromSecretKey } from '../../../utils/createKeypairFromSecretKey';


// Mock global fetch
global.fetch = jest.fn();

describe('Trade Controller', () => {
  let req: Partial<Request>;
  let res: Partial<Response>;
  let mockStatus: jest.Mock;
  let mockJson: jest.Mock;
  let mockDriftClient: any;
  let mockUser: any;
  let mockAgent: any;

  beforeEach(() => {
    // Setup request and response mocks
    req = {
      query: {},
      user: { walletAddress: '********************************' }
    };

    mockJson = jest.fn();
    mockStatus = jest.fn().mockReturnValue({ json: mockJson });
    res = {
      status: mockStatus,
      json: mockJson
    };

    // Mock agent data
    mockAgent = {
      _id: 'agent-id',
      botId: 'test-bot-id',
      pubkey: '********************************',
      assetPair: 'SOL',
      number: 0,
      agentName: 'Test Agent',
      deposit: 1000
    };

    // Mock Drift user
    mockUser = {
      getPerpPosition: jest.fn(),
      fetchAccounts: jest.fn().mockResolvedValue(undefined),
      getTotalAssetValue: jest.fn().mockReturnValue({ toNumber: () => *********0 }), // 1000 USDC
      getTotalAllTimePnl: jest.fn().mockReturnValue({ toNumber: () => ******** }), // 50 USDC
      userAccountPublicKey: 'mock-user-key'
    };

    // Mock Drift client
    mockDriftClient = {
      getUser: jest.fn().mockReturnValue(mockUser),
      subscribe: jest.fn().mockResolvedValue(true),
      unsubscribe: jest.fn().mockResolvedValue(true)
    };

    // Setup mocks
    (Agent.findOne as jest.Mock).mockResolvedValue(null);
    (Agent.find as jest.Mock).mockResolvedValue([]);
    (createClient as jest.Mock).mockResolvedValue(mockDriftClient);
    (getCurrentPrice as jest.Mock).mockResolvedValue(100);
    (getMarketId as jest.Mock).mockReturnValue(0);
    (createKeypairFromSecretKey as jest.Mock).mockReturnValue({
      publicKey: { toString: () => 'mock-public-key' }
    });
    (global.fetch as jest.Mock).mockResolvedValue({
      json: jest.fn().mockResolvedValue([])
    });


  });

  afterEach(() => {
    jest.clearAllMocks();
  });



  describe('getPosition', () => {
    it('should return 400 for missing agentId', async () => {
      req.query = {};

      await getPosition(req as Request, res as Response);

      expect(mockStatus).toHaveBeenCalledWith(400);
      expect(mockJson).toHaveBeenCalledWith({
        error: 'agentId is required and must be a string'
      });
    });

    it('should return 400 for non-string agentId', async () => {
      req.query = { agentId: 123 as any };

      await getPosition(req as Request, res as Response);

      expect(mockStatus).toHaveBeenCalledWith(400);
      expect(mockJson).toHaveBeenCalledWith({
        error: 'agentId is required and must be a string'
      });
    });

    it('should return 400 for non-existent agent', async () => {
      req.query = { agentId: 'non-existent-id' };
      (Agent.findOne as jest.Mock).mockResolvedValue(null);

      await getPosition(req as Request, res as Response);

      expect(mockStatus).toHaveBeenCalledWith(400);
      expect(mockJson).toHaveBeenCalledWith({ error: 'Agent not found' });
    });

    it('should return 403 for unauthorized user access', async () => {
      req.query = { agentId: 'test-bot-id' };
      req.user = { walletAddress: '22222222222222222222222222222223' };
      (Agent.findOne as jest.Mock).mockResolvedValue(mockAgent);

      await getPosition(req as Request, res as Response);

      expect(mockStatus).toHaveBeenCalledWith(403);
      expect(mockJson).toHaveBeenCalledWith({
        error: 'Forbidden: You do not have permission to access this agent data'
      });
    });

    it('should successfully return position data', async () => {
      req.query = { agentId: 'test-bot-id' };
      (Agent.findOne as jest.Mock).mockResolvedValue(mockAgent);

      const mockPosition = {
        baseAssetAmount: { toNumber: () => ********* },
        quoteAssetAmount: { toNumber: () => *********0 },
        unrealizedPnl: { toNumber: () => ******** },
        quoteEntryAmount: {
          abs: () => ({ toNumber: () => *********0 })
        }
      };
      mockUser.getPerpPosition.mockReturnValue(mockPosition);

      await getPosition(req as Request, res as Response);

      expect(mockStatus).toHaveBeenCalledWith(200);
      expect(mockJson).toHaveBeenCalledWith(expect.objectContaining({
        response: expect.any(Object)
      }));
    });

    it('should handle drift client creation errors', async () => {
      req.query = { agentId: 'test-bot-id' };
      (Agent.findOne as jest.Mock).mockResolvedValue(mockAgent);
      (createClient as jest.Mock).mockRejectedValue(new Error('Connection failed'));

      await getPosition(req as Request, res as Response);

      expect(mockStatus).toHaveBeenCalledWith(500);
      expect(mockJson).toHaveBeenCalledWith({ error: 'Connection failed' });
    });
  });

  describe('getAccountActivity', () => {
    it('should return 400 for missing pubkey parameter', async () => {
      req.query = {};

      await getAccountActivity(req as Request, res as Response);

      expect(mockStatus).toHaveBeenCalledWith(400);
      expect(mockJson).toHaveBeenCalledWith({ error: 'pubkey is required and must be a string' });
    });

    it('should return 403 for unauthorized user access', async () => {
      req.query = { pubkey: '********************************' };
      req.user = { walletAddress: '22222222222222222222222222222223' };

      await getAccountActivity(req as Request, res as Response);

      expect(mockStatus).toHaveBeenCalledWith(403);
      expect(mockJson).toHaveBeenCalledWith({ error: 'Forbidden: You do not have permission to access this user data' });
    });

    it('should successfully return account activity', async () => {
      req.query = { pubkey: '********************************' };
      const mockAgents = [mockAgent];
      (Agent.find as jest.Mock).mockResolvedValue(mockAgents);

      await getAccountActivity(req as Request, res as Response);

      expect(mockStatus).toHaveBeenCalledWith(200);
      expect(mockJson).toHaveBeenCalledWith(expect.any(Object));
    });

    it('should handle drift client creation errors', async () => {
      req.query = { pubkey: '********************************' };
      (createClient as jest.Mock).mockRejectedValue(new Error('Connection failed'));

      await getAccountActivity(req as Request, res as Response);

      expect(mockStatus).toHaveBeenCalledWith(500);
      expect(mockJson).toHaveBeenCalledWith({ error: 'Connection failed' });
    });
  });

  describe('getAccountAssets', () => {
    it('should return 400 for missing pubkey parameter', async () => {
      req.query = {};

      await getAccountAssets(req as Request, res as Response);

      expect(mockStatus).toHaveBeenCalledWith(400);
      expect(mockJson).toHaveBeenCalledWith({ error: 'pubkey is required and must be a string' });
    });

    it('should return 403 for unauthorized user access', async () => {
      req.query = { pubkey: '********************************' };
      req.user = { walletAddress: '22222222222222222222222222222223' };

      await getAccountAssets(req as Request, res as Response);

      expect(mockStatus).toHaveBeenCalledWith(403);
      expect(mockJson).toHaveBeenCalledWith({ error: 'Forbidden: You do not have permission to access this user data' });
    });

    it('should successfully return account assets', async () => {
      req.query = { pubkey: '********************************' };
      const mockAgents = [mockAgent];
      (Agent.find as jest.Mock).mockResolvedValue(mockAgents);

      await getAccountAssets(req as Request, res as Response);

      expect(mockStatus).toHaveBeenCalledWith(200);
      expect(mockJson).toHaveBeenCalledWith(expect.objectContaining({
        assets: expect.any(Number)
      }));
    });

    it('should handle drift client creation errors', async () => {
      req.query = { pubkey: '********************************' };
      (createClient as jest.Mock).mockRejectedValue(new Error('Connection failed'));

      await getAccountAssets(req as Request, res as Response);

      expect(mockStatus).toHaveBeenCalledWith(500);
      expect(mockJson).toHaveBeenCalledWith({ error: 'Connection failed' });
    });
  });

  describe('getAccountPositions', () => {
    it('should return 400 for missing pubkey parameter', async () => {
      req.query = {};

      await getAccountPositions(req as Request, res as Response);

      expect(mockStatus).toHaveBeenCalledWith(400);
      expect(mockJson).toHaveBeenCalledWith({ error: 'pubkey is required and must be a string' });
    });

    it('should return 403 for unauthorized user access', async () => {
      req.query = { pubkey: '********************************' };
      req.user = { walletAddress: '22222222222222222222222222222223' };

      await getAccountPositions(req as Request, res as Response);

      expect(mockStatus).toHaveBeenCalledWith(403);
      expect(mockJson).toHaveBeenCalledWith({ error: 'Forbidden: You do not have permission to access this user data' });
    });

    it('should successfully return account positions', async () => {
      req.query = { pubkey: '********************************' };
      const mockAgents = [mockAgent];
      (Agent.find as jest.Mock).mockResolvedValue(mockAgents);

      const mockPosition = {
        baseAssetAmount: { toNumber: () => ********* }
      };
      mockUser.getPerpPosition.mockReturnValue(mockPosition);

      await getAccountPositions(req as Request, res as Response);

      expect(mockStatus).toHaveBeenCalledWith(200);
      expect(mockJson).toHaveBeenCalledWith(expect.objectContaining({
        positions: expect.any(Array)
      }));
    });

    it('should handle drift client creation errors', async () => {
      req.query = { pubkey: '********************************' };
      (createClient as jest.Mock).mockRejectedValue(new Error('Connection failed'));

      await getAccountPositions(req as Request, res as Response);

      expect(mockStatus).toHaveBeenCalledWith(500);
      expect(mockJson).toHaveBeenCalledWith({ error: 'Connection failed' });
    });
  });
});
