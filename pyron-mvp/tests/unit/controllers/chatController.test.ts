import { Request, Response } from 'express';

// Mock external services before importing the controller
jest.mock('../../../databaseModels/chat');
jest.mock('../../../databaseModels/chatMessage');
jest.mock('../../../databaseModels/agent');

// Import controller after mocking dependencies
import {
  addChat,
  getChatsByWalletAddress,
  getChatsByWalletAddressWithAgents,
  getChatById,
  updateChat,
  deleteChat,
  getChatMessages,
  addChatMessage,
  createChat
} from '../../../controller/chatController';
import Chat from '../../../databaseModels/chat';
import ChatMessage from '../../../databaseModels/chatMessage';
import Agent from '../../../databaseModels/agent';
import {
  mockChat,
  mockChatArray,
  mockChatMessage,
  mockChatMessageArray,
  mockAgent,
  mockRequest,
  mockResponse,
  validChatData
} from '../../fixtures/mockData';

// Cha<PERSON>, ChatMessage, and Agent models are mocked via jest.mock() above

describe('Chat Controller', () => {
  let req: Partial<Request>;
  let res: Partial<Response>;
  let statusMock: jest.Mock;
  let jsonMock: jest.Mock;

  beforeEach(() => {
    req = mockRequest();
    res = mockResponse();
    statusMock = res.status as jest.Mock;
    jsonMock = res.json as jest.Mock;
  });

  describe('addChat', () => {
    it('should create chat with wallet address and message', async () => {
      req.body = { walletAddress: 'test-wallet', message: 'test message' };

      (Chat as any) = jest.fn().mockImplementation(() => ({
        save: jest.fn().mockResolvedValue(mockChat)
      }));

      await addChat(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(201);
      expect(jsonMock).toHaveBeenCalledWith(expect.objectContaining({
        save: expect.any(Function)
      }));
    });

    it('should return 400 for missing wallet address', async () => {
      req.body = { message: 'test message' };

      await addChat(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(400);
      expect(jsonMock).toHaveBeenCalledWith({ message: 'Wallet address and message are required' });
    });

    it('should return 400 for missing message', async () => {
      req.body = { walletAddress: 'test-wallet' };

      await addChat(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(400);
      expect(jsonMock).toHaveBeenCalledWith({ message: 'Wallet address and message are required' });
    });

    it('should handle database errors', async () => {
      req.body = { walletAddress: 'test-wallet', message: 'test message' };
      const error = new Error('Database error');

      (Chat as any) = jest.fn().mockImplementation(() => ({
        save: jest.fn().mockRejectedValue(error)
      }));

      await addChat(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(500);
      expect(jsonMock).toHaveBeenCalledWith({ error: error.message });
    });
  });

  describe('getChatsByWalletAddress', () => {
    beforeEach(() => {
      (Chat as any).find = jest.fn();
    });

    it('should return chats for valid wallet address', async () => {
      req.params = { walletAddress: 'test-wallet-address' };
      (Chat as any).find.mockResolvedValue(mockChatArray);

      await getChatsByWalletAddress(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith(mockChatArray);
    });

    it('should return empty array for wallet with no chats', async () => {
      req.params = { walletAddress: 'empty-wallet' };
      (Chat as any).find.mockResolvedValue([]);

      await getChatsByWalletAddress(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith([]);
    });

    it('should handle database errors', async () => {
      req.params = { walletAddress: 'test-wallet-address' };
      const error = new Error('Database error');
      (Chat as any).find.mockRejectedValue(error);

      await getChatsByWalletAddress(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(500);
      expect(jsonMock).toHaveBeenCalledWith({ error: error.message });
    });
  });

  describe('getChatsByWalletAddressWithAgents', () => {
    beforeEach(() => {
      (Chat as any).find = jest.fn();
      (Agent as any).findOne = jest.fn();
    });

    it('should return chats with associated agents', async () => {
      req.params = { walletAddress: 'test-wallet-address' };
      const mockLeanChats = mockChatArray.map(chat => ({ ...chat }));
      (Chat as any).find.mockReturnValue({
        lean: jest.fn().mockResolvedValue(mockLeanChats)
      });
      (Agent as any).findOne.mockReturnValue({
        lean: jest.fn().mockResolvedValue(mockAgent)
      });

      await getChatsByWalletAddressWithAgents(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            agent: expect.any(Object)
          })
        ])
      );
    });

    it('should handle chats with no associated agents', async () => {
      req.params = { walletAddress: 'test-wallet-address' };
      const mockLeanChats = [mockChat];
      (Chat as any).find.mockReturnValue({
        lean: jest.fn().mockResolvedValue(mockLeanChats)
      });
      (Agent as any).findOne.mockReturnValue({
        lean: jest.fn().mockResolvedValue(null)
      });

      await getChatsByWalletAddressWithAgents(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith([
        expect.objectContaining({
          agent: null
        })
      ]);
    });

    it('should handle database errors', async () => {
      req.params = { walletAddress: 'test-wallet-address' };
      const error = new Error('Database error');
      (Chat as any).find.mockReturnValue({
        lean: jest.fn().mockRejectedValue(error)
      });

      await getChatsByWalletAddressWithAgents(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(500);
      expect(jsonMock).toHaveBeenCalledWith({ error: error.message });
    });
  });

  describe('getChatById', () => {
    beforeEach(() => {
      (Chat as any).findById = jest.fn();
    });

    it('should return chat for valid ID', async () => {
      req.params = { chatId: mockChat._id.toString() };
      (Chat as any).findById.mockResolvedValue(mockChat);

      await getChatById(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith(mockChat);
    });

    it('should return 404 for non-existent chat', async () => {
      req.params = { chatId: 'non-existent-id' };
      (Chat as any).findById.mockResolvedValue(null);

      await getChatById(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(404);
      expect(jsonMock).toHaveBeenCalledWith({ message: 'Chat not found' });
    });

    it('should handle database errors', async () => {
      req.params = { chatId: mockChat._id.toString() };
      const error = new Error('Database error');
      (Chat as any).findById.mockRejectedValue(error);

      await getChatById(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(500);
      expect(jsonMock).toHaveBeenCalledWith({ error: error.message });
    });
  });

  describe('updateChat', () => {
    beforeEach(() => {
      (Chat as any).findByIdAndUpdate = jest.fn();
    });

    it('should update existing chat', async () => {
      req.params = { chatId: mockChat._id.toString() };
      req.body = { title: 'Updated Title' };
      const updatedChat = { ...mockChat, title: 'Updated Title' };
      (Chat as any).findByIdAndUpdate.mockResolvedValue(updatedChat);

      await updateChat(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith(updatedChat);
    });

    it('should return 404 for non-existent chat', async () => {
      req.params = { chatId: 'non-existent-id' };
      req.body = { title: 'Updated Title' };
      (Chat as any).findByIdAndUpdate.mockResolvedValue(null);

      await updateChat(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(404);
      expect(jsonMock).toHaveBeenCalledWith({ message: 'Chat not found' });
    });

    it('should handle database errors', async () => {
      req.params = { chatId: mockChat._id.toString() };
      req.body = { title: 'Updated Title' };
      const error = new Error('Database error');
      (Chat as any).findByIdAndUpdate.mockRejectedValue(error);

      await updateChat(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(500);
      expect(jsonMock).toHaveBeenCalledWith({ error: error.message });
    });
  });

  describe('deleteChat', () => {
    beforeEach(() => {
      (Chat as any).findByIdAndDelete = jest.fn();
    });

    it('should delete existing chat', async () => {
      req.params = { chatId: mockChat._id.toString() };
      (Chat as any).findByIdAndDelete.mockResolvedValue(mockChat);

      await deleteChat(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith({ message: 'Chat deleted successfully' });
    });

    it('should return 404 for non-existent chat', async () => {
      req.params = { chatId: 'non-existent-id' };
      (Chat as any).findByIdAndDelete.mockResolvedValue(null);

      await deleteChat(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(404);
      expect(jsonMock).toHaveBeenCalledWith({ message: 'Chat not found' });
    });

    it('should handle database errors', async () => {
      req.params = { chatId: mockChat._id.toString() };
      const error = new Error('Database error');
      (Chat as any).findByIdAndDelete.mockRejectedValue(error);

      await deleteChat(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(500);
      expect(jsonMock).toHaveBeenCalledWith({ error: error.message });
    });
  });

  describe('getChatMessages', () => {
    beforeEach(() => {
      (ChatMessage as any).find = jest.fn();
    });

    it('should return messages for valid chat ID', async () => {
      req.params = { chatId: mockChat._id.toString() };
      (ChatMessage as any).find.mockResolvedValue(mockChatMessageArray);

      await getChatMessages(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith(mockChatMessageArray);
    });

    it('should return empty array for chat with no messages', async () => {
      req.params = { chatId: 'empty-chat' };
      (ChatMessage as any).find.mockResolvedValue([]);

      await getChatMessages(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith([]);
    });

    it('should handle database errors', async () => {
      req.params = { chatId: mockChat._id.toString() };
      const error = new Error('Database error');
      (ChatMessage as any).find.mockRejectedValue(error);

      await getChatMessages(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(500);
      expect(jsonMock).toHaveBeenCalledWith({ error: error.message });
    });
  });

  describe('addChatMessage', () => {
    it('should add message with valid data', async () => {
      req.params = { chatId: mockChat._id.toString() };
      req.body = { type: 'user', content: 'test message' };

      (ChatMessage as any) = jest.fn().mockImplementation(() => ({
        save: jest.fn().mockResolvedValue(mockChatMessage)
      }));

      await addChatMessage(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(201);
      expect(jsonMock).toHaveBeenCalledWith(expect.objectContaining({
        save: expect.any(Function)
      }));
    });

    it('should handle database errors', async () => {
      req.params = { chatId: mockChat._id.toString() };
      req.body = { type: 'user', content: 'test message' };
      const error = new Error('Database error');

      (ChatMessage as any) = jest.fn().mockImplementation(() => ({
        save: jest.fn().mockRejectedValue(error)
      }));

      await addChatMessage(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(500);
      expect(jsonMock).toHaveBeenCalledWith({ error: error.message });
    });
  });

});