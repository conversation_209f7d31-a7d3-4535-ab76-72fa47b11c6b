import { Request, Response } from 'express';

// Mock external services before importing the controller
jest.mock('../../../databaseModels/hypothesis');
jest.mock('../../../databaseModels/log');

// Import controller after mocking dependencies
import {
  createHypothesis,
  getHypothesesByLogId,
  getAllHypotheses,
  getHypothesesByChatId
} from '../../../controller/hypothesisController';
import Hypothesis from '../../../databaseModels/hypothesis';
import Log from '../../../databaseModels/log';
import {
  mockHypothesis,
  mockHypothesisArray,
  mockLogArray,
  mockRequest,
  mockResponse,
  validHypothesisData,
  invalidHypothesisData
} from '../../fixtures/mockData';

// Hypothesis and Log models are mocked via jest.mock() above

describe('Hypothesis Controller', () => {
  let req: Partial<Request>;
  let res: Partial<Response>;
  let statusMock: jest.Mock;
  let jsonMock: jest.Mock;

  beforeEach(() => {
    req = mockRequest();
    res = mockResponse();
    statusMock = res.status as jest.Mock;
    jsonMock = res.json as jest.Mock;

    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('createHypothesis', () => {
    it('should create hypothesis with valid data', async () => {
      req.body = validHypothesisData;

      // Mock Hypothesis constructor to return an object with save method
      (Hypothesis as any) = jest.fn().mockImplementation(() => ({
        save: jest.fn().mockResolvedValue(mockHypothesis)
      }));

      await createHypothesis(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(201);
      expect(jsonMock).toHaveBeenCalledWith(mockHypothesis);
    });

    it('should return 400 for missing logId', async () => {
      req.body = invalidHypothesisData.missingLogId;

      await createHypothesis(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(400);
      expect(jsonMock).toHaveBeenCalledWith({ message: 'LogId and hypothesis are required' });
    });

    it('should return 400 for missing hypothesis text', async () => {
      req.body = invalidHypothesisData.missingHypothesis;

      await createHypothesis(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(400);
      expect(jsonMock).toHaveBeenCalledWith({ message: 'LogId and hypothesis are required' });
    });

    it('should return 400 for empty hypothesis text', async () => {
      req.body = invalidHypothesisData.emptyHypothesis;

      await createHypothesis(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(400);
      expect(jsonMock).toHaveBeenCalledWith({ message: 'LogId and hypothesis are required' });
    });

    it('should set creation date automatically', async () => {
      req.body = validHypothesisData;

      (Hypothesis as any) = jest.fn().mockImplementation(() => ({
        save: jest.fn().mockResolvedValue(mockHypothesis)
      }));

      await createHypothesis(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(201);
    });

    it('should handle database errors', async () => {
      req.body = validHypothesisData;
      const error = new Error('Database connection failed');

      (Hypothesis as any) = jest.fn().mockImplementation(() => ({
        save: jest.fn().mockRejectedValue(error)
      }));

      await createHypothesis(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(400);
      expect(jsonMock).toHaveBeenCalledWith({ message: error.message });
    });
  });

  describe('getHypothesesByLogId', () => {
    beforeEach(() => {
      (Hypothesis as any).find = jest.fn();
    });

    it('should return hypotheses for valid log ID', async () => {
      // Set up request with logId parameter - use a simple string ID
      req.params = { logId: '507f1f77bcf86cd799439015' };

      const mockSort = jest.fn().mockResolvedValue(mockHypothesisArray);
      (Hypothesis as any).find.mockReturnValue({ sort: mockSort });

      await getHypothesesByLogId(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith(mockHypothesisArray);
      expect(mockSort).toHaveBeenCalledWith({ date: -1 });
    });

    it('should sort by date descending', async () => {
      req.params = { logId: '507f1f77bcf86cd799439015' };

      const mockSort = jest.fn().mockResolvedValue(mockHypothesisArray);
      (Hypothesis as any).find.mockReturnValue({ sort: mockSort });

      await getHypothesesByLogId(req as Request, res as Response);

      expect(mockSort).toHaveBeenCalledWith({ date: -1 });
    });

    it('should return empty array for no hypotheses', async () => {
      req.params = { logId: '507f1f77bcf86cd799439015' };

      const mockSort = jest.fn().mockResolvedValue([]);
      (Hypothesis as any).find.mockReturnValue({ sort: mockSort });

      await getHypothesesByLogId(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith([]);
    });

    it('should return 400 for missing logId', async () => {
      req.params = {};

      await getHypothesesByLogId(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(400);
      expect(jsonMock).toHaveBeenCalledWith({ message: 'LogId is required' });
    });

    it('should handle database errors', async () => {
      req.params = { logId: '507f1f77bcf86cd799439015' };

      const error = new Error('Database query failed');

      // Mock find to throw an error when called
      (Hypothesis as any).find.mockImplementation(() => {
        throw error;
      });

      await getHypothesesByLogId(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(500);
      expect(jsonMock).toHaveBeenCalledWith({ message: error.message });
    });
  });

  describe('getAllHypotheses', () => {
    beforeEach(() => {
      (Hypothesis as any).find = jest.fn();
    });

    it('should return all hypotheses', async () => {
      const mockSort = jest.fn().mockResolvedValue(mockHypothesisArray);
      (Hypothesis as any).find.mockReturnValue({ sort: mockSort });

      await getAllHypotheses(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith(mockHypothesisArray);
    });

    it('should sort by date descending', async () => {
      const mockSort = jest.fn().mockResolvedValue(mockHypothesisArray);
      (Hypothesis as any).find.mockReturnValue({ sort: mockSort });

      await getAllHypotheses(req as Request, res as Response);

      expect(mockSort).toHaveBeenCalledWith({ date: -1 });
    });

    it('should handle empty collection', async () => {
      const mockSort = jest.fn().mockResolvedValue([]);
      (Hypothesis as any).find.mockReturnValue({ sort: mockSort });

      await getAllHypotheses(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith([]);
    });

    it('should handle database errors', async () => {
      const error = new Error('Database connection failed');
      (Hypothesis as any).find.mockImplementation(() => {
        throw error;
      });

      await getAllHypotheses(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(500);
      expect(jsonMock).toHaveBeenCalledWith({ message: error.message });
    });
  });

  describe('getHypothesesByChatId', () => {
    beforeEach(() => {
      (Log as any).find = jest.fn();
      (Hypothesis as any).find = jest.fn();
    });

    it('should return hypotheses for all logs in chat', async () => {
      req.params = { chatId: 'test-chat-id' };
      (Log as any).find.mockResolvedValue(mockLogArray);
      const mockSort = jest.fn().mockResolvedValue(mockHypothesisArray);
      (Hypothesis as any).find.mockReturnValue({ sort: mockSort });

      await getHypothesesByChatId(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith(mockHypothesisArray);
    });

    it('should handle chats with no logs', async () => {
      req.params = { chatId: 'test-chat-id' };
      (Log as any).find.mockResolvedValue([]);

      await getHypothesesByChatId(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith([]);
    });


    it('should maintain proper sorting', async () => {
      req.params = { chatId: 'test-chat-id' };
      (Log as any).find.mockResolvedValue(mockLogArray);
      const mockSort = jest.fn().mockResolvedValue(mockHypothesisArray);
      (Hypothesis as any).find.mockReturnValue({ sort: mockSort });

      await getHypothesesByChatId(req as Request, res as Response);

      expect(mockSort).toHaveBeenCalledWith({ date: -1 });
    });

    it('should validate chatId parameter', async () => {
      req.params = {};

      await getHypothesesByChatId(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(400);
      expect(jsonMock).toHaveBeenCalledWith({ message: 'ChatId is required' });
    });

    it('should handle database errors when fetching logs', async () => {
      req.params = { chatId: 'test-chat-id' };
      const error = new Error('Log database error');
      (Log as any).find.mockRejectedValue(error);

      await getHypothesesByChatId(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(500);
      expect(jsonMock).toHaveBeenCalledWith({ message: error.message });
    });

    it('should handle database errors when fetching hypotheses', async () => {
      req.params = { chatId: 'test-chat-id' };
      (Log as any).find.mockResolvedValue(mockLogArray);
      const error = new Error('Hypothesis database error');
      (Hypothesis as any).find.mockImplementation(() => {
        throw error;
      });

      await getHypothesesByChatId(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(500);
      expect(jsonMock).toHaveBeenCalledWith({ message: error.message });
    });
  });
});
