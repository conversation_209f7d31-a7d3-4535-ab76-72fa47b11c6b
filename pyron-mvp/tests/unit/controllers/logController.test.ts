import { Request, Response } from 'express';

// Mock external services before importing the controller
jest.mock('../../../databaseModels/log');

// Import controller after mocking dependencies
import {
  createLog,
  getLogs,
  getLog,
  updateLog,
  deleteLog,
  getLogByChatId
} from '../../../controller/logController';
import Log from '../../../databaseModels/log';
import {
  mockLog,
  mockLogArray,
  validLogData,
  invalidLogData,
  mockRequest,
  mockResponse
} from '../../fixtures/mockData';

// Log model is mocked via jest.mock() above

describe('Log Controller', () => {
  let req: Partial<Request>;
  let res: Partial<Response>;
  let statusMock: jest.Mock;
  let jsonMock: jest.Mock;

  beforeEach(() => {
    req = mockRequest();
    res = mockResponse();
    statusMock = res.status as jest.Mock;
    jsonMock = res.json as jest.Mock;
  });

  describe('createLog', () => {
    it('should create log with valid data', async () => {
      req.body = validLogData;

      // Mock Log constructor to return an object with save method
      (Log as any) = jest.fn().mockImplementation(() => ({
        save: jest.fn().mockResolvedValue(mockLog)
      }));

      await createLog(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(201);
      expect(jsonMock).toHaveBeenCalledWith(mockLog);
    });

    it('should validate required fields', async () => {
      req.body = invalidLogData.incomplete;

      (Log as any) = jest.fn().mockImplementation(() => ({
        save: jest.fn().mockRejectedValue(new Error('Validation error'))
      }));

      await createLog(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(400);
      expect(jsonMock).toHaveBeenCalledWith({ message: 'Validation error' });
    });

    it('should set timestamps correctly', async () => {
      req.body = { ...validLogData };

      (Log as any) = jest.fn().mockImplementation(() => ({
        save: jest.fn().mockResolvedValue(mockLog)
      }));

      await createLog(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(201);
    });

    it('should handle database errors', async () => {
      req.body = validLogData;

      (Log as any) = jest.fn().mockImplementation(() => ({
        save: jest.fn().mockRejectedValue(new Error('Database connection failed'))
      }));

      await createLog(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(400);
      expect(jsonMock).toHaveBeenCalledWith({ message: 'Database connection failed' });
    });
  });

  describe('getLogs', () => {
    beforeEach(() => {
      (Log as any).find = jest.fn();
    });

    it('should return all logs', async () => {
      (Log as any).find.mockResolvedValue(mockLogArray);

      await getLogs(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith(mockLogArray);
    });

    it('should handle empty log collection', async () => {
      (Log as any).find.mockResolvedValue([]);

      await getLogs(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith([]);
    });

    it('should handle database errors', async () => {
      (Log as any).find.mockRejectedValue(new Error('Database error'));

      await getLogs(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(500);
      expect(jsonMock).toHaveBeenCalledWith({ message: 'Database error' });
    });
  });

  describe('getLog', () => {
    beforeEach(() => {
      (Log as any).findById = jest.fn();
    });

    it('should return log for valid ID', async () => {
      req.params = { id: mockLog._id.toString() };
      (Log as any).findById.mockResolvedValue(mockLog);

      await getLog(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith(mockLog);
    });

    it('should return 404 for non-existent log', async () => {
      req.params = { id: '507f1f77bcf86cd799439999' };
      (Log as any).findById.mockResolvedValue(null);

      await getLog(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(404);
      expect(jsonMock).toHaveBeenCalledWith({ message: 'Log not found' });
    });

    it('should return 500 for invalid ObjectId', async () => {
      req.params = { id: 'invalid-id' };
      (Log as any).findById.mockRejectedValue(new Error('Cast to ObjectId failed'));

      await getLog(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(500);
      expect(jsonMock).toHaveBeenCalledWith({ message: 'Cast to ObjectId failed' });
    });
  });

  describe('getLogByChatId', () => {
    beforeEach(() => {
      (Log as any).find = jest.fn();
    });

    it('should return logs for valid chat ID', async () => {
      req.query = { chatId: 'test-chat-id-123' };
      const chatLogs = [mockLog];
      (Log as any).find.mockResolvedValue(chatLogs);

      await getLogByChatId(req as any, res as any);

      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith(chatLogs);
    });

    it('should return empty array for no logs', async () => {
      req.query = { chatId: 'non-existent-chat' };
      (Log as any).find.mockResolvedValue([]);

      await getLogByChatId(req as any, res as any);

      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith([]);
    });

    it('should validate chat ID format', async () => {
      req.query = { chatId: 123 as any }; // Invalid type

      await getLogByChatId(req as any, res as any);

      expect(statusMock).toHaveBeenCalledWith(400);
      expect(jsonMock).toHaveBeenCalledWith({ error: 'chatId is required and must be a string' });
    });

    it('should handle query parameter validation', async () => {
      req.query = {}; // Missing chatId

      await getLogByChatId(req as any, res as any);

      expect(statusMock).toHaveBeenCalledWith(400);
      expect(jsonMock).toHaveBeenCalledWith({ error: 'chatId is required and must be a string' });
    });

    it('should handle database errors', async () => {
      req.query = { chatId: 'test-chat-id' };
      (Log as any).find.mockRejectedValue(new Error('Database connection failed'));

      await getLogByChatId(req as any, res as any);

      expect(statusMock).toHaveBeenCalledWith(500);
      expect(jsonMock).toHaveBeenCalledWith({ error: 'Internal server error' });
    });
  });

  describe('updateLog', () => {
    beforeEach(() => {
      (Log as any).findByIdAndUpdate = jest.fn();
    });

    it('should update existing log', async () => {
      req.params = { id: mockLog._id.toString() };
      req.body = { status: 'updated' };
      const updatedLog = { ...mockLog, status: 'updated' };
      (Log as any).findByIdAndUpdate.mockResolvedValue(updatedLog);

      await updateLog(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith(updatedLog);
    });

    it('should return 404 for non-existent log', async () => {
      req.params = { id: '507f1f77bcf86cd799439999' };
      req.body = { status: 'updated' };
      (Log as any).findByIdAndUpdate.mockResolvedValue(null);

      await updateLog(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(404);
      expect(jsonMock).toHaveBeenCalledWith({ message: 'Log not found' });
    });

    it('should validate update data', async () => {
      req.params = { id: mockLog._id.toString() };
      req.body = { invalidField: 'invalid' };
      (Log as any).findByIdAndUpdate.mockRejectedValue(new Error('Validation failed'));

      await updateLog(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(400);
      expect(jsonMock).toHaveBeenCalledWith({ message: 'Validation failed' });
    });
  });

  describe('deleteLog', () => {
    beforeEach(() => {
      (Log as any).findByIdAndDelete = jest.fn();
    });

    it('should delete existing log', async () => {
      req.params = { id: mockLog._id.toString() };
      (Log as any).findByIdAndDelete.mockResolvedValue(mockLog);

      await deleteLog(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith({ message: 'Log deleted successfully' });
    });

    it('should return 404 for non-existent log', async () => {
      req.params = { id: '507f1f77bcf86cd799439999' };
      (Log as any).findByIdAndDelete.mockResolvedValue(null);

      await deleteLog(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(404);
      expect(jsonMock).toHaveBeenCalledWith({ message: 'Log not found' });
    });

    it('should handle database errors', async () => {
      req.params = { id: mockLog._id.toString() };
      (Log as any).findByIdAndDelete.mockRejectedValue(new Error('Database error'));

      await deleteLog(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(500);
      expect(jsonMock).toHaveBeenCalledWith({ message: 'Database error' });
    });
  });
});
