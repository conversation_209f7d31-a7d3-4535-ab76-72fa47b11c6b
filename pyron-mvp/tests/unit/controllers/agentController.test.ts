import { Request, Response } from 'express';

// Mock external services before importing the controller
jest.mock('../../../databaseModels/agent');
jest.mock('../../../databaseModels/chat');

// Import controller after mocking dependencies
import {
  createAgent,
  getAgentById,
  getAgentByPubkeyAndAssetPair,
  getAgentBybotId,
  getAgentBychatId,
  deleteAgent,
  updateTradingStatus,
  updateHypothesisStatus,
  saveOrUpdateAgent,
  getAllAgents,
  addPromptsToAgent,
  updateAgentName
} from '../../../controller/agentController';
import Agent from '../../../databaseModels/agent';
import Chat from '../../../databaseModels/chat';
import {
  mockAgent,
  mockAgentArray,
  mockChat,
  mockRequest,
  mockResponse,
  validAgentData,
  invalidAgentData,
  errorMessages
} from '../../fixtures/mockData';

// Agent and Chat models are mocked via jest.mock() above

describe('Agent Controller', () => {
  let req: Partial<Request>;
  let res: Partial<Response>;
  let statusMock: jest.Mock;
  let jsonMock: jest.Mock;
  let sendMock: jest.Mock;

  beforeEach(() => {
    req = mockRequest();
    res = mockResponse();
    statusMock = res.status as jest.Mock;
    jsonMock = res.json as jest.Mock;
    sendMock = res.send as jest.Mock;
  });

  describe('createAgent', () => {
    it('should create agent with valid data', async () => {
      req.body = validAgentData;

      // Mock Agent constructor to return an object with save method
      (Agent as any) = jest.fn().mockImplementation(() => ({
        save: jest.fn().mockResolvedValue(mockAgent)
      }));

      await createAgent(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(201);
      expect(jsonMock).toHaveBeenCalledWith(mockAgent);
    });

    it('should return 400 for missing required fields', async () => {
      req.body = invalidAgentData.incomplete;

      (Agent as any) = jest.fn().mockImplementation(() => ({
        save: jest.fn().mockRejectedValue(new Error('Validation error'))
      }));

      await createAgent(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(400);
    });

    it('should handle database errors', async () => {
      req.body = validAgentData;

      (Agent as any) = jest.fn().mockImplementation(() => ({
        save: jest.fn().mockRejectedValue(new Error('Database error'))
      }));

      await createAgent(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(400);
    });
  });

  describe('getAgentById', () => {
    beforeEach(() => {
      // Reset Agent to the original mock for these tests
      (Agent as any).findById = jest.fn();
      (Agent as any).findOne = jest.fn();
      (Agent as any).find = jest.fn();
      (Agent as any).findByIdAndDelete = jest.fn();
      (Agent as any).findOneAndUpdate = jest.fn();
      (Agent as any).findByIdAndUpdate = jest.fn();
    });

    it('should return agent for valid ID', async () => {
      req.params = { id: mockAgent._id.toString() };
      (Agent as any).findById.mockResolvedValue(mockAgent);

      await getAgentById(req as Request, res as Response);

      expect(jsonMock).toHaveBeenCalledWith(mockAgent);
    });

    it('should return 404 for non-existent agent', async () => {
      req.params = { id: '507f1f77bcf86cd799439999' };
      (Agent as any).findById.mockResolvedValue(null);

      await getAgentById(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(404);
      expect(jsonMock).toHaveBeenCalledWith({ message: errorMessages.AGENT_NOT_FOUND });
    });

    it('should return 500 for invalid ObjectId', async () => {
      req.params = { id: 'invalid-id' };
      (Agent as any).findById.mockRejectedValue(new Error('Cast to ObjectId failed'));

      await getAgentById(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(500);
    });
  });

  describe('getAgentByPubkeyAndAssetPair', () => {
    beforeEach(() => {
      (Agent as any).findOne = jest.fn();
    });

    it('should return agent for valid pubkey and asset pair', async () => {
      req.params = { pubkey: 'test-pubkey', assetPair: 'SOL-PERP' };
      (Agent as any).findOne.mockResolvedValue(mockAgent);

      await getAgentByPubkeyAndAssetPair(req as Request, res as Response);

      expect(jsonMock).toHaveBeenCalledWith(mockAgent);
    });

    it('should return null for non-existent agent', async () => {
      req.params = { pubkey: 'non-existent', assetPair: 'SOL-PERP' };
      (Agent as any).findOne.mockResolvedValue(null);

      await getAgentByPubkeyAndAssetPair(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith(null);
    });

    it('should handle database errors', async () => {
      req.params = { pubkey: 'test-pubkey', assetPair: 'SOL-PERP' };
      (Agent as any).findOne.mockRejectedValue(new Error('Database error'));

      await getAgentByPubkeyAndAssetPair(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(500);
    });
  });

  describe('getAgentBybotId', () => {
    beforeEach(() => {
      (Agent as any).findOne = jest.fn();
    });

    it('should return agent for valid bot ID', async () => {
      req.params = { botId: 'test-bot-id' };
      (Agent as any).findOne.mockResolvedValue(mockAgent);

      await getAgentBybotId(req as Request, res as Response);

      expect(jsonMock).toHaveBeenCalledWith(mockAgent);
    });

    it('should handle database errors', async () => {
      req.params = { botId: 'test-bot-id' };
      (Agent as any).findOne.mockRejectedValue(new Error('Database error'));

      await getAgentBybotId(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(500);
    });
  });

  describe('getAgentBychatId', () => {
    beforeEach(() => {
      (Agent as any).findOne = jest.fn();
    });

    it('should return agent for valid chat ID', async () => {
      req.params = { chatId: 'test-chat-id' };
      (Agent as any).findOne.mockResolvedValue(mockAgent);

      await getAgentBychatId(req as Request, res as Response);

      expect(jsonMock).toHaveBeenCalledWith(mockAgent);
    });

    it('should handle database errors', async () => {
      req.params = { chatId: 'test-chat-id' };
      (Agent as any).findOne.mockRejectedValue(new Error('Database error'));

      await getAgentBychatId(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(500);
    });
  });

  describe('updateTradingStatus', () => {
    beforeEach(() => {
      (Agent as any).findOne = jest.fn();
    });

    it('should update trading status to "on"', async () => {
      req.params = { botId: 'test-bot-id' };
      req.body = { tradingStatus: 'on' };
      const mockAgentWithSave = { ...mockAgent, save: jest.fn().mockResolvedValue(mockAgent) };
      (Agent as any).findOne.mockResolvedValue(mockAgentWithSave);

      await updateTradingStatus(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith(mockAgent);
      expect(mockAgentWithSave.save).toHaveBeenCalledTimes(1);
    });

    it('should return 400 for invalid status', async () => {
      req.params = { botId: 'test-bot-id' };
      req.body = { tradingStatus: 'invalid' };

      await updateTradingStatus(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(400);
      expect(jsonMock).toHaveBeenCalledWith({ message: errorMessages.INVALID_TRADING_STATUS });
    });

    it('should return 403 for non-existent agent', async () => {
      req.params = { botId: 'non-existent' };
      req.body = { tradingStatus: 'on' };
      (Agent as any).findOne.mockResolvedValue(null);

      await updateTradingStatus(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(403);
      expect(jsonMock).toHaveBeenCalledWith({ message: 'Agent not found.' });
    });
  });

  describe('updateHypothesisStatus', () => {
    beforeEach(() => {
      (Agent as any).findOne = jest.fn();
    });

    it('should update hypothesis status to "on"', async () => {
      req.params = { botId: 'test-bot-id' };
      req.body = { hypothesisStatus: 'on' };
      const mockAgentWithSave = { ...mockAgent, save: jest.fn().mockResolvedValue(mockAgent) };
      (Agent as any).findOne.mockResolvedValue(mockAgentWithSave);

      await updateHypothesisStatus(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith(mockAgent);
    });

    it('should return 400 for invalid status', async () => {
      req.params = { botId: 'test-bot-id' };
      req.body = { hypothesisStatus: 'invalid' };

      await updateHypothesisStatus(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(400);
      expect(jsonMock).toHaveBeenCalledWith({ message: 'Invalid trading status. Must be either "on" or "off".' });
    });
  });

  describe('saveOrUpdateAgent', () => {
    beforeEach(() => {
      (Agent as any).findOneAndUpdate = jest.fn();
    });

    it('should update existing agent', async () => {
      req.params = { botId: 'test-bot-id' };
      req.body = { agent: validAgentData };
      (Agent as any).findOneAndUpdate.mockResolvedValue(mockAgent);

      await saveOrUpdateAgent(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith(mockAgent);
    });

    it('should handle database errors', async () => {
      req.params = { botId: 'test-bot-id' };
      req.body = { agent: validAgentData };
      (Agent as any).findOneAndUpdate.mockRejectedValue(new Error('Database error'));

      await saveOrUpdateAgent(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(500);
    });
  });

  describe('addPromptsToAgent', () => {
    beforeEach(() => {
      (Agent as any).findOneAndUpdate = jest.fn();
    });

    it('should add buy and sell prompts', async () => {
      req.params = { botId: 'test-bot-id' };
      req.body = {
        agent: {
          buyReportPrompt: 'Buy prompt',
          sellReportPrompt: 'Sell prompt'
        }
      };
      (Agent as any).findOneAndUpdate.mockResolvedValue(mockAgent);

      await addPromptsToAgent(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(200);
      expect(jsonMock).toHaveBeenCalledWith(mockAgent);
    });

    it('should return 404 for non-existent agent', async () => {
      req.params = { botId: 'non-existent' };
      req.body = { agent: { buyReportPrompt: 'Buy prompt' } };
      (Agent as any).findOneAndUpdate.mockResolvedValue(null);

      await addPromptsToAgent(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(404);
      expect(sendMock).toHaveBeenCalledWith(errorMessages.AGENT_NOT_FOUND);
    });
  });

  describe('getAllAgents', () => {
    beforeEach(() => {
      (Agent as any).find = jest.fn();
    });

    it('should return all agents', async () => {
      (Agent as any).find.mockResolvedValue(mockAgentArray);

      await getAllAgents(req as Request, res as Response);

      expect(jsonMock).toHaveBeenCalledWith(mockAgentArray);
    });

    it('should handle database errors', async () => {
      (Agent as any).find.mockRejectedValue(new Error('Database error'));

      await getAllAgents(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(500);
    });
  });

  describe('deleteAgent', () => {
    beforeEach(() => {
      (Agent as any).findByIdAndDelete = jest.fn();
    });

    it('should delete existing agent', async () => {
      req.params = { id: mockAgent._id.toString() };
      (Agent as any).findByIdAndDelete.mockResolvedValue(mockAgent);

      await deleteAgent(req as Request, res as Response);

      expect(jsonMock).toHaveBeenCalledWith({ message: 'Agent deleted successfully' });
    });

    it('should return 404 for non-existent agent', async () => {
      req.params = { id: '507f1f77bcf86cd799439999' };
      (Agent as any).findByIdAndDelete.mockResolvedValue(null);

      await deleteAgent(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(404);
      expect(jsonMock).toHaveBeenCalledWith({ message: errorMessages.AGENT_NOT_FOUND });
    });

    it('should handle database errors', async () => {
      req.params = { id: mockAgent._id.toString() };
      (Agent as any).findByIdAndDelete.mockRejectedValue(new Error('Database error'));

      await deleteAgent(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(500);
    });
  });

  describe('updateAgentName', () => {
    beforeEach(() => {
      (Chat as any).findByIdAndUpdate = jest.fn();
      (Agent as any).findOne = jest.fn();
      (Agent as any).findByIdAndUpdate = jest.fn();
    });

    it('should update agent name and chat title', async () => {
      req.params = { id: 'test-chat-id' };
      req.body = { name: 'Updated Agent Name' };

      (Chat as any).findByIdAndUpdate.mockResolvedValue(mockChat);
      (Agent as any).findOne.mockResolvedValue(mockAgent);
      (Agent as any).findByIdAndUpdate.mockResolvedValue(mockAgent);

      await updateAgentName(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(200);
    });

    it('should return 400 for missing name', async () => {
      req.params = { id: 'test-chat-id' };
      req.body = {};

      await updateAgentName(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(400);
      expect(jsonMock).toHaveBeenCalledWith({ message: errorMessages.AGENT_NAME_REQUIRED });
    });

    it('should return 404 for non-existent chat', async () => {
      req.params = { id: 'non-existent-chat' };
      req.body = { name: 'Updated Name' };

      (Chat as any).findByIdAndUpdate.mockResolvedValue(null);

      await updateAgentName(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(404);
      expect(jsonMock).toHaveBeenCalledWith({ message: errorMessages.CHAT_NOT_FOUND });
    });

    it('should handle agent without associated chat', async () => {
      req.params = { id: 'test-chat-id' };
      req.body = { name: 'Updated Name' };

      (Chat as any).findByIdAndUpdate.mockResolvedValue(mockChat);
      (Agent as any).findOne.mockResolvedValue(null);

      await updateAgentName(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(200);
    });

    it('should handle database errors', async () => {
      req.params = { id: 'test-chat-id' };
      req.body = { name: 'Updated Name' };

      (Chat as any).findByIdAndUpdate.mockRejectedValue(new Error('Database error'));

      await updateAgentName(req as Request, res as Response);

      expect(statusMock).toHaveBeenCalledWith(500);
    });
  });
});
