// Import middleware - no need to mock JWT for basic functionality tests
import { authMiddleware, walletOwnershipMiddleware } from '../../../middleware/authMiddleware';
import jwt from 'jsonwebtoken';

describe('Auth Middleware', () => {
  let req: any;
  let res: any;
  let next: jest.Mock;
  let mockStatus: jest.Mock;
  let mockJson: jest.Mock;
  let validToken: string;

  beforeEach(() => {
    // Generate a real JWT token for testing
    validToken = jwt.sign(
      { walletAddress: 'test-wallet-address', type: 'access' },
      process.env.JWT_SECRET!,
      { expiresIn: '1h' }
    );

    req = {
      headers: {},
      params: {},
      user: undefined
    };

    mockJson = jest.fn();
    mockStatus = jest.fn().mockReturnValue({ json: mockJson });

    res = {
      status: mockStatus,
      json: mockJson
    };

    next = jest.fn();
  });

  describe('authMiddleware', () => {
    it('should authenticate valid token', () => {
      req.headers.authorization = `Bearer ${validToken}`;

      authMiddleware(req, res, next);

      expect(req.user).toEqual({ walletAddress: 'test-wallet-address' });
      expect(next).toHaveBeenCalledTimes(1);
    });

    it('should return 401 for missing token', () => {
      authMiddleware(req, res, next);

      expect(mockStatus).toHaveBeenCalledWith(401);
      expect(mockJson).toHaveBeenCalledWith({ message: 'No token provided' });
      expect(next).not.toHaveBeenCalled();
    });

    it('should return 401 for invalid token format', () => {
      req.headers.authorization = 'InvalidFormat token';

      authMiddleware(req, res, next);

      expect(mockStatus).toHaveBeenCalledWith(401);
      expect(mockJson).toHaveBeenCalledWith({ message: 'No token provided' });
      expect(next).not.toHaveBeenCalled();
    });

    it('should return 401 for invalid JWT token', () => {
      req.headers.authorization = 'Bearer invalid-token';

      authMiddleware(req, res, next);

      expect(mockStatus).toHaveBeenCalledWith(401);
      expect(mockJson).toHaveBeenCalledWith({ message: 'jwt malformed' });
      expect(next).not.toHaveBeenCalled();
    });
  });

  describe('walletOwnershipMiddleware', () => {
    it('should allow access for wallet owner', () => {
      req.params = { walletAddress: 'test-wallet' };
      req.user = { walletAddress: 'test-wallet' };

      walletOwnershipMiddleware(req, res, next);

      expect(next).toHaveBeenCalledTimes(1);
      expect(mockStatus).not.toHaveBeenCalled();
    });

    it('should deny access for non-owner', () => {
      req.params = { walletAddress: 'other-wallet' };
      req.user = { walletAddress: 'test-wallet' };

      walletOwnershipMiddleware(req, res, next);

      expect(mockStatus).toHaveBeenCalledWith(403);
      expect(mockJson).toHaveBeenCalledWith({ message: 'Access denied' });
      expect(next).not.toHaveBeenCalled();
    });

    it('should handle missing wallet address in params', () => {
      req.params = {};
      req.user = { walletAddress: 'test-wallet' };

      walletOwnershipMiddleware(req, res, next);

      expect(mockStatus).toHaveBeenCalledWith(403);
      expect(mockJson).toHaveBeenCalledWith({ message: 'Access denied' });
      expect(next).not.toHaveBeenCalled();
    });

    it('should handle missing user in request', () => {
      req.params = { walletAddress: 'test-wallet' };
      req.user = undefined;

      walletOwnershipMiddleware(req, res, next);

      expect(mockStatus).toHaveBeenCalledWith(403);
      expect(mockJson).toHaveBeenCalledWith({ message: 'Access denied' });
      expect(next).not.toHaveBeenCalled();
    });
  });
});
