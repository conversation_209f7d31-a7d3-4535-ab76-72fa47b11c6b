import axios from 'axios';
import { getCurrentPrice } from '../../../../trade/drift/getPerpPrice';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('getCurrentPrice', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch current price for valid market', async () => {
    const mockResponse = {
      data: {
        bids: [{ price: '100000000' }], // 100 USDC in micro units
        asks: [{ price: '101000000' }]  // 101 USDC in micro units
      }
    };
    mockedAxios.get.mockResolvedValue(mockResponse);

    const result = await getCurrentPrice('SOL');

    expect(result).toBe(100.5); // (100 + 101) / 2 / 10^6
    expect(mockedAxios.get).toHaveBeenCalledTimes(1);
    expect(mockedAxios.get).toHaveBeenCalledWith('https://dlob.drift.trade/l2?marketName=SOL-PERP&depth=10&includeOracle=true&includeVamm=true');
  });

  it('should calculate mean price from bid/ask spread', async () => {
    const mockResponse = {
      data: {
        bids: [{ price: '50000000' }], // 50 USDC
        asks: [{ price: '52000000' }]  // 52 USDC
      }
    };
    mockedAxios.get.mockResolvedValue(mockResponse);

    const result = await getCurrentPrice('BTC');

    expect(result).toBe(51); // (50 + 52) / 2 / 10^6
  });

  it('should handle API errors gracefully', async () => {
    mockedAxios.get.mockRejectedValue(new Error('Network error'));

    await expect(getCurrentPrice('SOL')).rejects.toThrow('Failed to fetch market price');
  });

  it('should validate market name format', async () => {
    const mockResponse = {
      data: {
        bids: [{ price: '100000000' }],
        asks: [{ price: '101000000' }]
      }
    };
    mockedAxios.get.mockResolvedValue(mockResponse);

    await getCurrentPrice('ETH');

    expect(mockedAxios.get).toHaveBeenCalledWith('https://dlob.drift.trade/l2?marketName=ETH-PERP&depth=10&includeOracle=true&includeVamm=true');
  });

  it('should handle malformed API responses', async () => {
    const mockResponse = {
      data: {
        bids: [],
        asks: []
      }
    };
    mockedAxios.get.mockResolvedValue(mockResponse);

    await expect(getCurrentPrice('SOL')).rejects.toThrow('Failed to fetch market price');
  });

  it('should handle missing bids in response', async () => {
    const mockResponse = {
      data: {
        asks: [{ price: '101000000' }]
      }
    };
    mockedAxios.get.mockResolvedValue(mockResponse);

    await expect(getCurrentPrice('SOL')).rejects.toThrow('Failed to fetch market price');
  });

  it('should handle missing asks in response', async () => {
    const mockResponse = {
      data: {
        bids: [{ price: '100000000' }]
      }
    };
    mockedAxios.get.mockResolvedValue(mockResponse);

    await expect(getCurrentPrice('SOL')).rejects.toThrow('Failed to fetch market price');
  });

  it('should handle invalid price format', async () => {
    const mockResponse = {
      data: {
        bids: [{ price: 'invalid' }],
        asks: [{ price: '101000000' }]
      }
    };
    mockedAxios.get.mockResolvedValue(mockResponse);

    const result = await getCurrentPrice('SOL');

    // parseFloat('invalid') returns NaN, so the result should be NaN
    expect(result).toBeNaN();
  });

  it('should handle empty market name', async () => {
    const mockResponse = {
      data: {
        bids: [{ price: '100000000' }],
        asks: [{ price: '101000000' }]
      }
    };
    mockedAxios.get.mockResolvedValue(mockResponse);

    await getCurrentPrice('');

    expect(mockedAxios.get).toHaveBeenCalledWith('https://dlob.drift.trade/l2?marketName=-PERP&depth=10&includeOracle=true&includeVamm=true');
  });

  it('should handle axios timeout error', async () => {
    mockedAxios.get.mockRejectedValue({ code: 'ECONNABORTED', message: 'timeout' });

    await expect(getCurrentPrice('SOL')).rejects.toThrow('Failed to fetch market price');
  });

  it('should handle HTTP error responses', async () => {
    mockedAxios.get.mockRejectedValue({ response: { status: 404, statusText: 'Not Found' } });

    await expect(getCurrentPrice('INVALID')).rejects.toThrow('Failed to fetch market price');
  });
});
