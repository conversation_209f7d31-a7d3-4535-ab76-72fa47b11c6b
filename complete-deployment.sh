#!/bin/bash

# Complete PyRon Deployment Script
# This script completes the deployment that was started with Terraform

SERVER_IP="************"
SERVER_USER="shuvo"
SSH_KEY_PATH="/home/<USER>/.ssh/id_ed25519"
REMOTE_PROJECT_DIR="/home/<USER>/pyron-project"

echo "🚀 Completing PyRon deployment on server $SERVER_IP..."

# Function to run commands on server
run_on_server() {
    ssh -i "$SSH_KEY_PATH" "$SERVER_USER@$SERVER_IP" "$1"
}

echo "📁 Checking if files are on server..."
run_on_server "cd $REMOTE_PROJECT_DIR && ls -la"

echo ""
echo "🐳 Starting Docker deployment..."

# Try to run Docker commands without sudo first
echo "Attempting to run Docker without sudo..."
if run_on_server "cd $REMOTE_PROJECT_DIR && docker --version"; then
    echo "✅ Docker is accessible without sudo"
    
    echo "🛑 Stopping existing containers..."
    run_on_server "cd $REMOTE_PROJECT_DIR && docker compose down || true"
    
    echo "🧹 Cleaning up old images..."
    run_on_server "cd $REMOTE_PROJECT_DIR && docker image prune -f || true"
    
    echo "🔨 Building and starting containers..."
    run_on_server "cd $REMOTE_PROJECT_DIR && docker compose up -d --build"
    
    echo "⏳ Waiting for containers to start..."
    sleep 15
    
    echo "📊 Checking container status..."
    run_on_server "cd $REMOTE_PROJECT_DIR && docker compose ps"
    
    echo ""
    echo "🔍 Testing service health..."
    run_on_server "curl -f http://localhost:3000 > /dev/null 2>&1 && echo '✅ Backend API (port 3000) is responding' || echo '❌ Backend API is not responding'"
    run_on_server "curl -f http://localhost:3004 > /dev/null 2>&1 && echo '✅ Webhook service (port 3004) is responding' || echo '❌ Webhook service is not responding'"
    run_on_server "curl -f http://localhost:8080 > /dev/null 2>&1 && echo '✅ Frontend (port 8080) is responding' || echo '❌ Frontend is not responding'"
    
else
    echo "❌ Docker requires sudo. Let's try with sudo..."
    echo "Note: You may need to enter the sudo password on the server"
    
    echo "🛑 Stopping existing containers..."
    run_on_server "cd $REMOTE_PROJECT_DIR && echo 'Shuvo@0011' | sudo -S docker compose down || true"
    
    echo "🧹 Cleaning up old images..."
    run_on_server "cd $REMOTE_PROJECT_DIR && echo 'Shuvo@0011' | sudo -S docker image prune -f || true"
    
    echo "🔨 Building and starting containers..."
    run_on_server "cd $REMOTE_PROJECT_DIR && echo 'Shuvo@0011' | sudo -S docker compose up -d --build"
    
    echo "⏳ Waiting for containers to start..."
    sleep 15
    
    echo "📊 Checking container status..."
    run_on_server "cd $REMOTE_PROJECT_DIR && echo 'Shuvo@0011' | sudo -S docker compose ps"
    
    echo ""
    echo "🔍 Testing service health..."
    run_on_server "curl -f http://localhost:3000 > /dev/null 2>&1 && echo '✅ Backend API (port 3000) is responding' || echo '❌ Backend API is not responding'"
    run_on_server "curl -f http://localhost:3004 > /dev/null 2>&1 && echo '✅ Webhook service (port 3004) is responding' || echo '❌ Webhook service is not responding'"
    run_on_server "curl -f http://localhost:8080 > /dev/null 2>&1 && echo '✅ Frontend (port 8080) is responding' || echo '❌ Frontend is not responding'"
fi

echo ""
echo "🎉 Deployment completed!"
echo ""
echo "📱 Your PyRon application is now running at:"
echo "   🌐 Frontend:        http://$SERVER_IP:8080"
echo "   🔧 Backend API:     http://$SERVER_IP:3000"
echo "   🪝 Webhook Service: http://$SERVER_IP:3004"
echo ""
echo "📋 To check logs:"
echo "   ssh -i $SSH_KEY_PATH $SERVER_USER@$SERVER_IP 'cd $REMOTE_PROJECT_DIR && docker compose logs -f'"
echo ""
echo "🔄 To restart services:"
echo "   ssh -i $SSH_KEY_PATH $SERVER_USER@$SERVER_IP 'cd $REMOTE_PROJECT_DIR && docker compose restart'"
