#!/bin/bash

# PyRon Server Deployment Script
# This script deploys the PyRon application to a remote server

set -e

# Configuration
SERVER_IP="************"
SERVER_USER="shuvo"
SSH_KEY_PATH="/home/<USER>/.ssh/id_rsa"
REMOTE_PROJECT_DIR="/home/<USER>/pyron-project"
LOCAL_PROJECT_DIR="/home/<USER>/Documents/Codes/Pyron/pyron-meta-repo"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if SSH key exists
    if [[ ! -f "$SSH_KEY_PATH" ]]; then
        log_error "SSH key not found at $SSH_KEY_PATH"
        exit 1
    fi
    
    # Check if local project directory exists
    if [[ ! -d "$LOCAL_PROJECT_DIR" ]]; then
        log_error "Local project directory not found at $LOCAL_PROJECT_DIR"
        exit 1
    fi
    
    # Test SSH connection
    if ! ssh -i "$SSH_KEY_PATH" -o ConnectTimeout=10 -o BatchMode=yes "$SERVER_USER@$SERVER_IP" "echo 'SSH test successful'" > /dev/null 2>&1; then
        log_error "Cannot connect to server via SSH"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Sync project files to server
sync_files() {
    log_info "Syncing project files to server..."
    
    # Create remote directory if it doesn't exist
    ssh -i "$SSH_KEY_PATH" "$SERVER_USER@$SERVER_IP" "mkdir -p $REMOTE_PROJECT_DIR"
    
    # Sync files using rsync (more efficient than copying entire directory)
    rsync -avz --delete \
        --exclude 'node_modules' \
        --exclude '.git' \
        --exclude '*.log' \
        --exclude '.env*' \
        --exclude 'terrafrom/.terraform' \
        --exclude 'terrafrom/*.tfstate*' \
        -e "ssh -i $SSH_KEY_PATH" \
        "$LOCAL_PROJECT_DIR/" \
        "$SERVER_USER@$SERVER_IP:$REMOTE_PROJECT_DIR/"
    
    log_success "Files synced successfully"
}

# Setup environment files on server
setup_environment() {
    log_info "Setting up environment files on server..."
    
    # Check if environment files exist locally
    local env_files=(
        "pyron-mvp/.env.auth"
        "pyron-mvp/.env.database"
        "pyron-mvp/.env.core"
        "pyron-mvp/.env.blockchain"
        "pyron-mvp/.env.wallet"
        "pyron-webhook/.env.admin"
        "pyron-webhook/.env.database"
        "pyron-webhook/.env.server"
        "pyron-webhook/.env.security"
        "PyRon-webApp/.env"
    )
    
    local missing_files=()
    for file in "${env_files[@]}"; do
        if [[ ! -f "$LOCAL_PROJECT_DIR/$file" ]]; then
            missing_files+=("$file")
        fi
    done
    
    if [[ ${#missing_files[@]} -gt 0 ]]; then
        log_warning "Missing environment files:"
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        log_warning "Please create these files before deployment"
        read -p "Continue anyway? (y/N): " continue_deploy
        if [[ "$continue_deploy" != "y" && "$continue_deploy" != "Y" ]]; then
            exit 1
        fi
    else
        # Copy environment files
        for file in "${env_files[@]}"; do
            if [[ -f "$LOCAL_PROJECT_DIR/$file" ]]; then
                scp -i "$SSH_KEY_PATH" "$LOCAL_PROJECT_DIR/$file" "$SERVER_USER@$SERVER_IP:$REMOTE_PROJECT_DIR/$file"
            fi
        done
        log_success "Environment files copied"
    fi
}

# Deploy application on server
deploy_application() {
    log_info "Deploying application on server..."
    
    # Execute deployment commands on server
    ssh -i "$SSH_KEY_PATH" "$SERVER_USER@$SERVER_IP" << EOF
        set -e
        cd $REMOTE_PROJECT_DIR
        
        echo "Current directory: \$(pwd)"
        echo "Files in directory:"
        ls -la
        
        # Stop existing containers
        echo "Stopping existing containers..."
        docker compose down || true
        
        # Remove old images to free space
        echo "Cleaning up old Docker images..."
        docker image prune -f || true
        
        # Build and start new containers
        echo "Building and starting containers..."
        docker compose up -d --build
        
        # Wait a moment for containers to start
        sleep 10
        
        # Check container status
        echo "Container status:"
        docker compose ps
        
        # Check if services are responding
        echo "Checking service health..."
        curl -f http://localhost:3000 > /dev/null 2>&1 && echo "✓ Backend API is responding" || echo "✗ Backend API is not responding"
        curl -f http://localhost:3004 > /dev/null 2>&1 && echo "✓ Webhook service is responding" || echo "✗ Webhook service is not responding"
        curl -f http://localhost:8080 > /dev/null 2>&1 && echo "✓ Frontend is responding" || echo "✗ Frontend is not responding"
EOF
    
    log_success "Application deployed successfully"
}

# Show deployment status
show_status() {
    log_info "Checking deployment status..."
    
    ssh -i "$SSH_KEY_PATH" "$SERVER_USER@$SERVER_IP" << EOF
        echo "=== Docker Containers ==="
        docker compose -f $REMOTE_PROJECT_DIR/docker-compose.yml ps
        
        echo ""
        echo "=== System Resources ==="
        echo "Memory usage:"
        free -h
        echo ""
        echo "Disk usage:"
        df -h
        
        echo ""
        echo "=== Service URLs ==="
        echo "Frontend: http://$SERVER_IP:8080"
        echo "Backend API: http://$SERVER_IP:3000"
        echo "Webhook Service: http://$SERVER_IP:3004"
EOF
}

# Show logs
show_logs() {
    local service="$1"
    log_info "Showing logs for ${service:-all services}..."
    
    if [[ -n "$service" ]]; then
        ssh -i "$SSH_KEY_PATH" "$SERVER_USER@$SERVER_IP" "cd $REMOTE_PROJECT_DIR && docker compose logs -f --tail=50 $service"
    else
        ssh -i "$SSH_KEY_PATH" "$SERVER_USER@$SERVER_IP" "cd $REMOTE_PROJECT_DIR && docker compose logs -f --tail=50"
    fi
}

# Main deployment function
deploy() {
    log_info "Starting deployment to $SERVER_IP..."
    
    check_prerequisites
    sync_files
    setup_environment
    deploy_application
    show_status
    
    log_success "Deployment completed successfully!"
    echo ""
    echo "Service URLs:"
    echo "  Frontend: http://$SERVER_IP:8080"
    echo "  Backend API: http://$SERVER_IP:3000"
    echo "  Webhook Service: http://$SERVER_IP:3004"
}

# Show usage
show_usage() {
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  deploy    - Deploy application to server (default)"
    echo "  status    - Show deployment status"
    echo "  logs      - Show application logs"
    echo "  logs <service> - Show logs for specific service"
    echo "  sync      - Sync files only (no deployment)"
    echo ""
    echo "Examples:"
    echo "  $0 deploy"
    echo "  $0 status"
    echo "  $0 logs pyron-webhook"
}

# Main script
main() {
    local command="${1:-deploy}"
    
    case "$command" in
        "deploy")
            deploy
            ;;
        "status")
            check_prerequisites
            show_status
            ;;
        "logs")
            check_prerequisites
            show_logs "$2"
            ;;
        "sync")
            check_prerequisites
            sync_files
            ;;
        "help"|"--help"|"-h")
            show_usage
            ;;
        *)
            log_error "Unknown command: $command"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
