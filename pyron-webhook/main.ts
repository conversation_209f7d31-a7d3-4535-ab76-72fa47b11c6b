import express, { Application } from 'express';
import bodyParser from 'body-parser';
import mongoose from 'mongoose';
import webhookRouter from './src/router/webhookRouter';
import { createWorker } from './src/queue/worker'; // Import the worker factory
import { logInfo, logError, logWarn, cleanupLoggers } from './src/utils/logger';
import { loadEnvironmentVariables } from './src/utils/loadEnv';

import cors from 'cors';

// Load environment variables from multiple files
loadEnvironmentVariables();

// Express Application Setup
const app: Application = express();
app.use(bodyParser.json());

// MongoDB URI
if (!process.env.MONGO_USER || !process.env.MONGO_PASSWORD || !process.env.MONGO_HOST || !process.env.MONGO_DB || !process.env.MONGO_PORT) {
  throw new Error("MONGO_USER, MONGO_PASSWORD, and MONGO_HOST must be set in the environment variables");
}
const username = encodeURIComponent(process.env.MONGO_USER);
const password = encodeURIComponent(process.env.MONGO_PASSWORD);
const dbName = process.env.MONGO_DB;
const host = process.env.MONGO_HOST;
const portDb = process.env.MONGO_PORT;

const mongoUri = `mongodb://${username}:${password}@${host}:${portDb}/${dbName}?authSource=admin`;

// MongoDB Connection
mongoose.connect(mongoUri, {
  connectTimeoutMS: 20000, //  connection timeout
  socketTimeoutMS: 45000,  //  socket timeout
  maxPoolSize: 50,         // Maximum number of connections
  retryWrites: true,
}).then(() => logInfo('MongoDB connected'))
  .catch(err => logError('MongoDB connection error:', err));

// Initialize worker only in non-test environment
let worker: any;
let redisConnection: any;

if (process.env.NODE_ENV !== 'test') {
  logInfo('Initializing production worker...');
  const result = createWorker(); // Create the worker with default production settings
  worker = result.worker;
  redisConnection = result.connection;

  // Handle process termination
  process.on('SIGTERM', async () => {
    logInfo('SIGTERM received, closing connections...');
    if (worker) await worker.close();
    if (redisConnection) await redisConnection.quit();
    await mongoose.connection.close();
    cleanupLoggers();
    process.exit(0);
  });

  process.on('SIGINT', async () => {
    logInfo('SIGINT received, closing connections...');
    if (worker) await worker.close();
    if (redisConnection) await redisConnection.quit();
    await mongoose.connection.close();
    cleanupLoggers();
    process.exit(0);
  });
}

const corsOptions = {
  methods: ['GET', 'POST', 'DELETE', 'PATCH', 'PUT'], // Allow these HTTP methods
  allowedHeaders: ['Content-Type', 'Authorization'],
  origin: process.env.ALLOWED_ORIGINS?.split(',')  // Specify the allowed origin
};
app.use(cors(corsOptions));

app.use('/webhook', webhookRouter);

// Only start the server if we're not in a test environment
if (process.env.NODE_ENV !== 'test') {
  const port = process.env.PORT || 3000;
  app.listen(port, () => {
    logInfo(`Server is running on http://localhost:${port}`);
  });
}

// Export the app for testing
export default app;
