import { Connection } from "@solana/web3.js";
import { placeOrder } from "../utils/drift/placeOrder";
import Log from "../databaseModels/log";
import { getMarketId } from "../utils/getMarketId";
import { PublicKey } from "@solana/web3.js";
import { cancelOrders } from "../utils/drift/cancelOrders";
import { createClient } from "../utils/drift/getClient";
import { logAgentInfo, logAgentWarn, logAgentError } from "../utils/logger";
import { loadEnvironmentVariables } from "./loadEnv";

// Load environment variables
loadEnvironmentVariables();

type SignalType =
  | "sell"
  | "buy"
  | "sellOpenBar"
  | "buyOpenBar"


export async function processAlert( alertData: any, wallet: any, agent: any, connectionRpc: Connection) {

  const authority = new PublicKey(agent.pubkey);
  const agentId = agent.botId;
  logAgentInfo(agentId, "Authority", authority.toBase58());

  try {
    // Check if trades are on
    const tradesOn = agent.tradingStatus === "on";
    if (!tradesOn) {
      logAgentInfo(agentId, `User has trades turned off. Skipping execution.`);
      return "ok";
    }

    const { action, signalName, ticker } = alertData;

    // Basic validations
    if (!ticker) {
      logAgentWarn(agentId, "No ticker in alert data. Exiting.");
      return;
    }

    if (agent.assetPair !== ticker) {
      logAgentWarn(agentId, `Ticker mismatch. Agent expects ${agent.assetPair}, got ${ticker}`);
      return;
    }

    // Make sure signals object is defined
    agent.signals = agent.signals || {};
    if (
      signalName &&
      !agent.signals[signalName as SignalType]
    ) {
      agent.signals[signalName as SignalType] = 0;
    }
    // Log only the essential signal information to reduce memory usage
    const signalSummary = {
      buy: agent.signals.buy || 0,
      sell: agent.signals.sell || 0,
      buyOpenBar: agent.signals.buyOpenBar || 0,
      sellOpenBar: agent.signals.sellOpenBar || 0
    };
    logAgentInfo(agentId, "Agent signals before processing:", signalSummary);

    // Check if market exists
    const marketIndex = getMarketId(ticker)
    if (marketIndex === undefined) {
      logAgentWarn(agentId, "marketIndex not found for ticker:", ticker);
      return;
    }


    // Helper: Close SELL side (short) positions & orders
    const closeSellPositionsAndOrders = async (closePosition: boolean) => {
      if (process.env.NODE_ENV === 'test') {
        logAgentInfo(agentId, "Test environment: Simulating closeSellPositionsAndOrders");
        return;
      }
      try {



        // If there's a short position (baseAssetAmount < 0), close it
        if (closePosition) {
          logAgentInfo(agentId, "Closing existing short position...");
          const result = await placeOrder("closeSell", 100, agent.number, ticker, wallet, agent.chatId, action, authority, agentId);

          if (!result) {
            logAgentWarn(agentId, "Failed to close short position, transaction may not have completed");
          } else {
            logAgentInfo(agentId, "Short position close transaction completed successfully");
          }
        }

        // Cancel any open short orders
        await cancelOrders(agent, marketIndex, connectionRpc, wallet, authority, "short");

      } catch (error) {
        logAgentError(agentId, "Error in closeSellPositionsAndOrders:", error);
        throw error;
      }
    };

    // Helper: Close BUY side (long) positions & orders
    const closeBuyPositionsAndOrders = async (closePosition: boolean) => {
      if (process.env.NODE_ENV === 'test') {
        logAgentInfo(agentId, "Test environment: Simulating closeBuyPositionsAndOrders");
        return;
      }


      try {

        if (closePosition) {
          logAgentInfo(agentId, "Closing existing long position...");
          const result = await placeOrder("closeBuy", 100, agent.number, ticker, wallet, agent.chatId, action, authority, agentId);

          if (!result) {
            logAgentWarn(agentId, "Failed to close long position, transaction may not have completed");
          } else {
            logAgentInfo(agentId, "Long position close transaction completed successfully");
          }
        }

        // Cancel any open long orders
        await cancelOrders(agent, marketIndex, connectionRpc, wallet, authority, "long");

      } catch (error) {
        logAgentError(agentId, "Error in closeBuyPositionsAndOrders:", error);
        throw error;
      }
    };

    // Helper: Open a new BUY position
    const openBuyPosition = async () => {
      if (process.env.NODE_ENV === 'test') {
        logAgentInfo(agentId, "Test environment: Simulating openBuyPosition");
        return;
      }
      try {

        logAgentInfo(agentId, "Opening a long position (BUY)...");
        const result = await placeOrder("buy", 100, agent.number, ticker, wallet, agent.chatId, action, authority, agentId);

        if (!result) {
          logAgentWarn(agentId, "Failed to open buy position, transaction may not have completed");
        } else {
          logAgentInfo(agentId, "Buy position open transaction completed successfully");
        }
      } catch (error) {
        logAgentError(agentId, "Error in openBuyPosition:", error);
        throw error;
      }
    };

    // Helper: Open a new SELL position
    const openSellPosition = async () => {
      if (process.env.NODE_ENV === 'test') {
        logAgentInfo(agentId, "Test environment: Simulating openSellPosition");
        return;
      }

      try {

        logAgentInfo(agentId, "Opening a short position (SELL)...");
        const result = await placeOrder("sell", 100, agent.number, ticker, wallet, agent.chatId, action, authority, agentId);

        if (!result) {
          logAgentWarn(agentId, "Failed to open sell position, transaction may not have completed");
        } else {
          logAgentInfo(agentId, "Sell position open transaction completed successfully");
        }
      } catch (error) {
        logAgentError(agentId, "Error in openSellPosition:", error);
        throw error;
      }
    };

    //------------------------------------------------
    // 1) Handle "confirmation signals" -> confirmation minute signals
    //------------------------------------------------
    if (action === "confirmationMinute" && signalName) {
      //Increase minutesignal count
      agent.signals[signalName as SignalType] = (agent.signals[signalName as SignalType] || 0) + 1;
      // Log only the essential signal information to reduce memory usage
      const signalSummary = {
        buy: agent.signals.buy || 0,
        sell: agent.signals.sell || 0,
        buyOpenBar: agent.signals.buyOpenBar || 0,
        sellOpenBar: agent.signals.sellOpenBar || 0
      };
      logAgentInfo(agentId, "Agent signals before processing:", signalSummary);

      logAgentInfo(agentId, `confirmationMinute -> incrementing ${signalName} to`, agent.signals[signalName as SignalType]);
      //Check if last override signal is the same as the current signal
      const lastOverrideSignal = await Log.findOne({
        chatId: agent.chatId,
        webhookSignal: { $in: ["overrideBuy", "overrideSell"] }
      }).sort({ timestamps: -1 }).lean(); // Use lean() to get plain JS object instead of Mongoose document

      // Log only essential information instead of the entire object
      logAgentInfo(agentId, "confirmationMinute -> lastOverrideSignal",
        lastOverrideSignal ? { signal: lastOverrideSignal.webhookSignal, timestamp: lastOverrideSignal.timestamps } : null);

      if (signalName === "buy") {
        //Check if confirmations are sufficient to close the opposit position
        if ( agent.signals.buy >= agent.requiredBuyConfirmationsClose) {
          await closeSellPositionsAndOrders(true);
        }
        //Check if confirmations are sufficient and the last override signal is the same direction as the current signal
        if (agent.signals.buy >= agent.requiredBuyConfirmationsOverride && lastOverrideSignal?.webhookSignal === "overrideBuy") {
          await closeSellPositionsAndOrders(false);
          await openBuyPosition();
          agent.signals.sell = 0;
          agent.signals.buy = 0;
          agent.signals.sellOpenBar = 0;
          agent.signals.buyOpenBar = 0;
        }
        logAgentInfo(agentId, `agent.signals.buy: ${agent.signals.buy}, agent.requiredBuyConfirmationsResetCounter: ${agent.requiredBuyConfirmationsResetCounter}`);
        //Check if confirmations are sufficient to reset the signal counter
         if (agent.signals.buy >= agent.requiredBuyConfirmationsResetCounter) {
          agent.signals.sell = 0;
          agent.signals.sellOpenBar = 0;
        }

      }

      if (signalName === "sell") {
        //Check if confirmations are sufficient to close the opposit position
        if (agent.signals.sell >= agent.requiredSellConfirmationsClose) {
          await closeBuyPositionsAndOrders(true);
        }
        //Check if confirmations are sufficient and the last override signal is the same as the current signal
        if (agent.signals.sell >= agent.requiredSellConfirmationsOverride && lastOverrideSignal?.webhookSignal === "overrideSell") {
          await closeBuyPositionsAndOrders(false);
          await openSellPosition();
          agent.signals.buy = 0;
          agent.signals.sell = 0;
          agent.signals.sellOpenBar = 0;
          agent.signals.buyOpenBar = 0;
        }
        logAgentInfo(agentId, `agent.signals.sell: ${agent.signals.sell}, agent.requiredSellConfirmationsResetCounter: ${agent.requiredSellConfirmationsResetCounter}`);

        //Check if confirmations are sufficient to reset the signal counter
        if (agent.signals.sell >= agent.requiredSellConfirmationsResetCounter) {
          agent.signals.buy = 0;
          agent.signals.buyOpenBar = 0;
        }
      }

      // Save changes
      await agent.save();
      // Log only the essential signal information to reduce memory usage
      const signalSummaryAfter = {
        buy: agent.signals.buy || 0,
        sell: agent.signals.sell || 0,
        buyOpenBar: agent.signals.buyOpenBar || 0,
        sellOpenBar: agent.signals.sellOpenBar || 0
      };
      logAgentInfo(agentId, "agent.signals after processing:", signalSummaryAfter);
    }
    //------------------------------------------------
    // 2) Handle "confirmation signals" -> confirmation open ber signals
    //------------------------------------------------
    else if (action === "confirmationOpenBar") {
      //fetch last override signal
      const lastOverrideSignal = await Log.findOne({
        chatId: agent.chatId,
        webhookSignal: { $in: ["overrideBuy", "overrideSell"] }
      }).sort({ timestamps: -1 }).lean(); // Use lean() to get plain JS object

      // Log only essential information instead of the entire object
      logAgentInfo(agentId, "confirmation openBar -> lastOverrideSignal",
        lastOverrideSignal ? { signal: lastOverrideSignal.webhookSignal, timestamp: lastOverrideSignal.timestamps } : null);

      //increment signal openBar count
      if (signalName === "buyOpenBar") {
        agent.signals.buyOpenBar = (agent.signals.buyOpenBar || 0) + 1;
        logAgentInfo(agentId, `confirmationOpenBar -> incrementing buyOpenBar to`, agent.signals.buyOpenBar);
      } else if (signalName === "sellOpenBar") {
        agent.signals.sellOpenBar = (agent.signals.sellOpenBar || 0) + 1;
        logAgentInfo(agentId, `confirmationOpenBar -> incrementing sellOpenBar to`, agent.signals.sellOpenBar);
      }

      if (signalName === "buyOpenBar") {
        //Check if the last override signal is the same direction as the current signal
        if (lastOverrideSignal?.webhookSignal === "overrideBuy") {
          await closeSellPositionsAndOrders(false);
          await openBuyPosition();
          agent.signals.sell = 0;
          agent.signals.buy = 0;
          agent.signals.sellOpenBar = 0;
          agent.signals.buyOpenBar = 0;
        }
      }
      else if (signalName === "sellOpenBar") {
        //Check if the last override signal is the same direction as the current signal
        if (lastOverrideSignal?.webhookSignal === "overrideSell") {
          await closeBuyPositionsAndOrders(false);
          await openSellPosition();
          agent.signals.buy = 0;
          agent.signals.sell = 0;
          agent.signals.sellOpenBar = 0;
          agent.signals.buyOpenBar = 0;
        }
      }
      await agent.save();
      // Log only the essential signal information to reduce memory usage
      const signalSummaryAfter = {
        buy: agent.signals.buy || 0,
        sell: agent.signals.sell || 0,
        buyOpenBar: agent.signals.buyOpenBar || 0,
        sellOpenBar: agent.signals.sellOpenBar || 0
      };
      logAgentInfo(agentId, "agent.signals after processing:", signalSummaryAfter);
    }
    //------------------------------------------------
    // 3) Handle "confirmation signals" -> confirmation close bar signals
    //------------------------------------------------
    else if (action === "confirmationCloseBar") {
      if (signalName === "buy") {
        await closeSellPositionsAndOrders(true);
        //agent.signals.sell = 0;
        await agent.save();
      }
      else if (signalName === "sell") {
        await closeBuyPositionsAndOrders(true);
        //agent.signals.buy = 0;
        await agent.save();
      }
    }
    //------------------------------------------------
    // 4) For Buy signal
    //------------------------------------------------
    else if (action === "buy") {
      // Log only the essential signal information to reduce memory usage
      const signalSummary = {
        buy: agent.signals.buy || 0,
        sell: agent.signals.sell || 0,
        buyOpenBar: agent.signals.buyOpenBar || 0,
        sellOpenBar: agent.signals.sellOpenBar || 0
      };
      logAgentInfo(agentId, "Processing BUY action with agent signals:", signalSummary);
      logAgentInfo(agentId, "Required confirmations - buyOpenBar:", agent.requiredBuyConfirmationsOpenBar);

      // Check if the buy confirmations are sufficient
      if (agent.signals.buy >= agent.requiredBuyConfirmationsOpen) {
        logAgentInfo(agentId, "Triggered BUY action and confirmations are sufficient...");
        // Close SELL side orders
        await closeSellPositionsAndOrders(false);
        // Open BUY side
        await openBuyPosition();
        // Reset ALL signals
        agent.signals.sell = 0;
        agent.signals.buy = 0;
        agent.signals.sellOpenBar = 0;
        agent.signals.buyOpenBar = 0;
        // Log only zeros since we've reset all signals
        logAgentInfo(agentId, "Reset all signals after opening position:", { buy: 0, sell: 0, buyOpenBar: 0, sellOpenBar: 0 });
      }
      else if (agent.signals.buyOpenBar >= agent.requiredBuyConfirmationsOpenBar) {
        logAgentInfo(agentId, "Triggered BUY action based on buyOpenBar confirmation...");
        logAgentInfo(agentId, `"buyOpenBar:", agent.signals.buyOpenBar, "required:", agent.requiredBuyConfirmationsOpenBar`);
        await openBuyPosition();
        // Reset ALL signals - make sure to reset sellOpenBar as well
        agent.signals.sell = 0;
        agent.signals.buy = 0;
        agent.signals.sellOpenBar = 0;
        agent.signals.buyOpenBar = 0;
        // Log only zeros since we've reset all signals
        logAgentInfo(agentId, "Reset all signals after opening position:", { buy: 0, sell: 0, buyOpenBar: 0, sellOpenBar: 0 });
      }
      else {
        logAgentInfo(agentId, `BUY signal received but not enough confirmations: buyOpenBar=${agent.signals.buyOpenBar}, buy=${agent.signals.buy}`);
      }
      // Always save the agent state after processing a buy signal
      await agent.save();
      // Log only the essential signal information to reduce memory usage
      const signalSummaryAfter = {
        buy: agent.signals.buy || 0,
        sell: agent.signals.sell || 0,
        buyOpenBar: agent.signals.buyOpenBar || 0,
        sellOpenBar: agent.signals.sellOpenBar || 0
      };
      logAgentInfo(agentId, "Agent state saved after BUY action:", signalSummaryAfter);
    }
    //------------------------------------------------
    // 5) For Sell signal
    //------------------------------------------------
    else if (action === "sell") {
      // Check if the sell confirmations are sufficient
      if (agent.signals.sell >= agent.requiredSellConfirmationsOpen) {
        // Close BUY side orders only
        await closeBuyPositionsAndOrders(false);
        logAgentInfo(agentId, "Triggered SELL action and confirmations are sufficient...");
        // Open SELL side
        await openSellPosition();
        // Reset ALL signals
        agent.signals.sell = 0;
        agent.signals.buy = 0;
        agent.signals.sellOpenBar = 0;
        agent.signals.buyOpenBar = 0;
        // Log only zeros since we've reset all signals
        logAgentInfo(agentId, "Reset all signals after opening position:", { buy: 0, sell: 0, buyOpenBar: 0, sellOpenBar: 0 });
        await agent.save();
      }
      else if (agent.signals.sellOpenBar >= agent.requiredSellConfirmationsOpenBar) {
        logAgentInfo(agentId, "Triggered SELL action based on sellOpenBar confirmation...");
        await openSellPosition();
        // Reset ALL signals - make sure to reset buyOpenBar as well
        agent.signals.sell = 0;
        agent.signals.buy = 0;
        agent.signals.sellOpenBar = 0;
        agent.signals.buyOpenBar = 0;
        // Log only zeros since we've reset all signals
        logAgentInfo(agentId, "Reset all signals after opening position:", { buy: 0, sell: 0, buyOpenBar: 0, sellOpenBar: 0 });
        await agent.save();
      }
      else {
        logAgentInfo(agentId, `SELL signal received but not enough sell confirmations: ${agent.signals.sell}`);
        // Always save the agent state after processing a sell signal
        await agent.save();
      }
    }
    //------------------------------------------------
    // 6) For overrideBuy signal
    //------------------------------------------------
    else if (action === "overrideBuy") {

      let log = new Log({
        chatId: agent.chatId,
        type: "Market",
        side: "buy",
        size: 0,
        total: 0,
        percentage: "100",
        market: ticker + "-PERP",
        status: "failed",
        shown: 1,
        webhookSignal: "overrideBuy",
        price: 0,
        timestamps: new Date().toISOString()
      })
      await log.save();
      logAgentInfo(agentId, "Created override log entry", { id: log._id, signal: log.webhookSignal });
      await agent.save();
    }
    //------------------------------------------------
    // 7) For overrideSell signal
    //------------------------------------------------
    else if (action === "overrideSell") {

      let log = new Log({
        chatId: agent.chatId,
        type: "Market",
        side: "sell",
        size: 0,
        total: 0,
        percentage: "100",
        market: ticker + "-PERP",
        status: "failed",
        shown: 1,
        webhookSignal: "overrideSell",
        price: 0,
        timestamps: new Date().toISOString()
      })
      await log.save();
      logAgentInfo(agentId, "Created override log entry", { id: log._id, signal: log.webhookSignal });
      await agent.save();
    }
    //------------------------------------------------
    // 8) For reset signal
    //------------------------------------------------
   /*  else if (action === "reset") {
      if(signalName == "buy"){
        //Check if confirmations are sufficient to reset the signal counter
        logAgentInfo(agentId, `Checking buy reset conditions:`, {
          current: agent.signals.buy,
          required: agent.requiredBuyConfirmationsResetCounter
        });
        if (agent.signals.buy >= agent.requiredBuyConfirmationsResetCounter) {
          agent.signals.buy = 0;
          agent.signals.buyOpenBar = 0;
        }
      }
      else if(signalName == "sell"){
        //Check if confirmations are sufficient to reset the signal counter
        logAgentInfo(agentId, `Checking sell reset conditions:`, {
          current: agent.signals.sell,
          required: agent.requiredSellConfirmationsResetCounter
        });
        if (agent.signals.sell >= agent.requiredSellConfirmationsResetCounter) {
          agent.signals.sell = 0;
          agent.signals.sellOpenBar = 0;
        }
      }
      await agent.save();
    }
 */
    return "ok";
  } catch (error) {
    logAgentError(agentId, "Error in processAlert:", error);
    throw error; // Re-throw the error to be handled by the worker
  }
}
