import dotenv from 'dotenv';

/**
 * Load environment variables from multiple .env files in the correct order
 * This ensures proper precedence and organization of environment variables
 */
export function loadEnvironmentVariables(): void {
  // Load database configuration first
  dotenv.config({ path: '.env.database' });
  
  // Load server configuration
  dotenv.config({ path: '.env.server' });
  
  // Load security configuration
  dotenv.config({ path: '.env.security' });
  
  // Load admin configuration (sensitive keys) last
  dotenv.config({ path: '.env.admin' });
  
  // Load main .env file last for any overrides
  dotenv.config();
}

/**
 * Load environment variables for testing
 * Uses the test-specific environment file
 */
export function loadTestEnvironmentVariables(): void {
  dotenv.config({ path: '.env.test' });
}
