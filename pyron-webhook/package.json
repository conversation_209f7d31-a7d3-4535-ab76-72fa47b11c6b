{"name": "pyron-mvp", "version": "1.0.0", "description": "", "main": "index.js", "type": "commonjs", "scripts": {"test": "jest", "test:e2e": "jest e2e/tests --config jest.e2e.config.js", "start": "ts-node main.ts"}, "author": "", "license": "ISC", "dependencies": {"@drift-labs/sdk": "^2.111.0-beta.3", "@lightprotocol/stateless.js": "^0.20.9", "@solana/spl-token": "^0.4.9", "@solana/web3.js": "^1.98.0", "@sqds/multisig": "^2.1.3", "@types/body-parser": "^1.19.5", "@types/mongoose": "^5.11.97", "async-mutex": "^0.5.0", "body-parser": "^1.20.3", "bs58": "^6.0.0", "bullmq": "^5.40.2", "chai": "^5.2.0", "cors": "^2.8.5", "express": "^4.21.2", "ioredis": "^5.5.0", "jito-ts": "^4.2.0", "mocha": "^11.1.0", "mongoose": "^8.9.2", "redlock": "^5.0.0-beta.2", "rpc-websockets": "7.10.0", "solana-agent-kit": "1.2.0", "winston": "^3.17.0"}, "devDependencies": {"@types/chai": "^5.2.1", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/mocha": "^10.0.10", "@types/mongodb-memory-server": "^1.8.0", "@types/node": "^22.10.2", "@types/supertest": "^6.0.3", "jest": "^29.7.0", "mongodb-memory-server": "^10.1.4", "supertest": "^7.1.1", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "typescript": "^5.6.3"}}