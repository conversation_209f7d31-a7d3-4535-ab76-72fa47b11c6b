import request from 'supertest';
import app from '../../main';
import { setupE2E, teardownE2E } from '../setup/e2eSetup';
import { createTestAgent, waitForJobCompletion, getAgentSignals, clearTestData, getAgentAuthority } from '../setup/testHelpers';
import { Connection, PublicKey, LAMPORTS_PER_SOL } from '@solana/web3.js';
import { createKeypairFromSecretKey } from '../../src/utils/createKeypairFromSecretKey';
import { createClient } from '../../src/utils/drift/getClient';

describe('Webhook Endpoint E2E Tests', () => {
  beforeAll(async () => {
    await setupE2E();
  });

  afterAll(async () => {
    await clearTestData();
    await teardownE2E();
  });

  beforeEach(async () => {
    await clearTestData();
  });

  describe('Environment and Wallet Validation', () => {
    it('should validate all required environment variables are present', () => {
      const requiredEnvVars = [
        'MONGO_USER',
        'MONGO_PASSWORD',
        'MONGO_HOST',
        'MONGO_PORT',
        'MONGO_DB',
        'REDIS_URL',
        'WORKER_CONCURRENCY',
        'ADMIN_KEY',
        'RPC_URL',
        'ALLOWED_ORIGINS',
      ];

      const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

      expect(missingVars).toEqual([]);
      if (missingVars.length > 0) {
        console.error('Missing environment variables:', missingVars);
      }
    });

    it('should validate ADMIN_KEY is a valid base58 private key', () => {
      const adminKey = process.env.ADMIN_KEY;
      expect(adminKey).toBeDefined();

      try {
        const wallet = createKeypairFromSecretKey(adminKey!);
        expect(wallet).toBeDefined();
        expect(wallet.publicKey).toBeInstanceOf(PublicKey);
        console.log('✓ Admin wallet public key:', wallet.publicKey.toBase58());
      } catch (error) {
        fail(`Invalid ADMIN_KEY format: ${error}`);
      }
    });

    it('should validate TEST_AGENT_PUBKEY is a valid Solana public key', () => {
      const testAgentPubkey = process.env.TEST_AGENT_PUBKEY;
      expect(testAgentPubkey).toBeDefined();

      try {
        const pubkey = new PublicKey(testAgentPubkey!);
        expect(pubkey).toBeInstanceOf(PublicKey);
        console.log('✓ Test agent public key:', pubkey.toBase58());
        console.log('ℹ This should match webhookData.address in test requests');
      } catch (error) {
        fail(`Invalid TEST_AGENT_PUBKEY format: ${error}`);
      }
    });

    it('should validate RPC connection is accessible', async () => {
      const rpcUrl = process.env.RPC_URL;
      expect(rpcUrl).toBeDefined();

      try {
        const connection = new Connection(rpcUrl!, 'confirmed');
        const version = await connection.getVersion();
        expect(version).toBeDefined();
        expect(version['solana-core']).toBeDefined();
        console.log('✓ RPC connection successful, Solana version:', version['solana-core']);
      } catch (error) {
        fail(`RPC connection failed: ${error}`);
      }
    }, 10000);

    it('should validate admin wallet has sufficient SOL balance (>0.1 SOL)', async () => {
      const rpcUrl = process.env.RPC_URL;
      const adminKey = process.env.ADMIN_KEY;

      try {
        const connection = new Connection(rpcUrl!, 'confirmed');
        const wallet = createKeypairFromSecretKey(adminKey!);

        const balance = await connection.getBalance(wallet.publicKey);
        const solBalance = balance / LAMPORTS_PER_SOL;

        console.log(`Admin wallet balance: ${solBalance} SOL`);
        expect(solBalance).toBeGreaterThan(0.01);

        if (solBalance < 1) {
          console.warn('⚠ Warning: Admin wallet has less than 1 SOL, consider funding for production use');
        }
      } catch (error) {
        fail(`Failed to check admin wallet balance: ${error}`);
      }
    }, 15000);

    it('should validate Drift client can be created with admin wallet and agent authority', async () => {
      const rpcUrl = process.env.RPC_URL;
      const adminKey = process.env.ADMIN_KEY;

      try {
        const connection = new Connection(rpcUrl!, 'confirmed');
        const wallet = createKeypairFromSecretKey(adminKey!);

        // Create a test agent to get the proper authority
        const testAgent = await createTestAgent('drift-test-agent');
        const authority = new PublicKey(testAgent.pubkey); // This should match TEST_AGENT_PUBKEY

        const driftClient = await createClient(connection, wallet, authority);
        expect(driftClient).toBeDefined();

        console.log('✓ Drift client created successfully');
        console.log('✓ Wallet (signer):', wallet.publicKey.toBase58());
        console.log('✓ Authority (agent):', authority.toBase58());

        // Clean up test agent
        await testAgent.deleteOne();
      } catch (error) {
        // This might fail in test environments, which is acceptable
        console.warn('⚠ Drift client creation failed (may be expected in test env):', error);
        expect(true).toBe(true); // Don't fail the test
      }
    }, 20000);

    it('should warn if subaccount balance is low (< $5 USD equivalent)', async () => {
      const rpcUrl = process.env.RPC_URL;
      const adminKey = process.env.ADMIN_KEY;

      try {
        const connection = new Connection(rpcUrl!, 'confirmed');
        const wallet = createKeypairFromSecretKey(adminKey!);

        // Create a test agent to get the proper authority
        const testAgent = await createTestAgent('balance-test-agent');
        const authority = new PublicKey(testAgent.pubkey); // This should match TEST_AGENT_PUBKEY

        const driftClient = await createClient(connection, wallet, authority);

        if (!driftClient) {
          console.log('⚠ Could not create Drift client, skipping subaccount balance check');
          await testAgent.deleteOne(); // Clean up
          expect(true).toBe(true);
          return;
        }

        // This is a placeholder - actual implementation would check subaccount balance
        // For now, we just validate the setup is ready for such checks
        console.log('✓ Subaccount balance check setup validated');
        console.log('✓ Wallet (signer):', wallet.publicKey.toBase58());
        console.log('✓ Authority (agent):', authority.toBase58());
        console.log('ℹ Note: Implement actual subaccount balance checking based on your Drift integration');

        // Clean up test agent
        await testAgent.deleteOne();
        expect(true).toBe(true);
      } catch (error) {
        console.warn('⚠ Subaccount balance check failed (may be expected in test env):', error);
        expect(true).toBe(true); // Don't fail the test
      }
    }, 25000);

    it('should validate test agent configuration matches webhook data structure', async () => {
      // Create a test agent
      const testAgent = await createTestAgent('webhook-config-test');

      // Validate agent pubkey matches TEST_AGENT_PUBKEY
      const expectedPubkey = process.env.TEST_AGENT_PUBKEY;
      expect(testAgent.pubkey).toBe(expectedPubkey);

      // Validate agent configuration for webhook processing
      expect(testAgent.assetPair).toBe('SOL'); // Should match ticker in webhook requests
      expect(testAgent.tradingStatus).toBe('on'); // Required for processing
      expect(testAgent.number).toBeDefined(); // Subaccount ID for Drift

      console.log('✓ Agent configuration validated:');
      console.log('  - Agent ID:', testAgent.botId);
      console.log('  - Authority (pubkey):', testAgent.pubkey);
      console.log('  - Asset Pair:', testAgent.assetPair);
      console.log('  - Trading Status:', testAgent.tradingStatus);
      console.log('  - Subaccount ID:', testAgent.number);
      console.log('ℹ webhookData.address should match:', testAgent.pubkey);

      // Clean up
      await testAgent.deleteOne();
    });
  });

  describe('POST /webhook/:agentId', () => {
    const testAgentId = 'test-agent-123';

    it('should process a valid buy confirmation webhook', async () => {
      // Create a test agent
      await createTestAgent(testAgentId);

      // Send webhook request
      const response = await request(app)
        .post(`/webhook/${testAgentId}`)
        .send({
          action: 'confirmationMinute',
          signalName: 'buy',
          ticker: 'SOL'
        });

      // Check response
      expect(response.status).toBe(200);
      expect(response.text).toBe('Webhook added to the queue for processing');

      // Wait for job completion
      const jobCompleted = await waitForJobCompletion();
      expect(jobCompleted).toBe(true);

      // Check agent signals
      const signals = await getAgentSignals(testAgentId);
      expect(signals).toBeDefined();
      expect(signals?.buy).toBe(1);
    });

    it('should process a valid sell confirmation webhook', async () => {
      // Create a test agent
      await createTestAgent(testAgentId);

      // Send webhook request
      const response = await request(app)
        .post(`/webhook/${testAgentId}`)
        .send({
          action: 'confirmationMinute',
          signalName: 'sell',
          ticker: 'SOL'
        });

      // Check response
      expect(response.status).toBe(200);
      expect(response.text).toBe('Webhook added to the queue for processing');

      // Wait for job completion
      const jobCompleted = await waitForJobCompletion();
      expect(jobCompleted).toBe(true);

      // Check agent signals
      const signals = await getAgentSignals(testAgentId);
      expect(signals).toBeDefined();
      expect(signals?.sell).toBe(1);
    });

    it('should filter webhook when buy threshold is reached', async () => {
      // Create a test agent with buy signal above threshold (3 > 2)
      await createTestAgent(testAgentId, { buy: 3, sell: 0, buyOpenBar: 0, sellOpenBar: 0 });

      // Send webhook request
      const response = await request(app)
        .post(`/webhook/${testAgentId}`)
        .send({
          action: 'confirmationMinute',
          signalName: 'buy',
          ticker: 'SOL'
        });

      // Check response
      expect(response.status).toBe(202);
      expect(response.text).toBe('Request filtered: buy signal threshold already met');

      // Verify signals haven't changed (should remain at 3)
      const signals = await getAgentSignals(testAgentId);
      expect(signals?.buy).toBe(3);
    });

    it('should filter webhook when sell threshold is reached', async () => {
      // Create a test agent with sell signal above threshold (3 > 2)
      await createTestAgent(testAgentId, { buy: 0, sell: 3, buyOpenBar: 0, sellOpenBar: 0 });

      // Send webhook request
      const response = await request(app)
        .post(`/webhook/${testAgentId}`)
        .send({
          action: 'confirmationMinute',
          signalName: 'sell',
          ticker: 'SOL'
        });

      // Check response
      expect(response.status).toBe(202);
      expect(response.text).toBe('Request filtered: sell signal threshold already met');

      // Verify signals haven't changed (should remain at 3)
      const signals = await getAgentSignals(testAgentId);
      expect(signals?.sell).toBe(3);
    });

    it('should return 400 for missing required fields', async () => {
      // Send webhook request without required fields
      const response = await request(app)
        .post(`/webhook/${testAgentId}`)
        .send({
          action: 'confirmationMinute',
          signalName: 'buy',
          // Missing signalName and ticker
        });

      // Check response
      expect(response.status).toBe(400);
      expect(response.text).toBe('Missing ticker');
    });

  });
  describe('Position Opening and Reset Operations', () => {
    const testAgentId = 'test-agent-reset-123';

    it('should open buy position and reset all signals when buy action has sufficient confirmations', async () => {
      // Create agent with buy signals at open threshold
      await createTestAgent(testAgentId, { buy: 2, sell: 1, buyOpenBar: 0, sellOpenBar: 0 });

      // Send buy action that should trigger position open and reset
      const response = await request(app)
        .post(`/webhook/${testAgentId}`)
        .send({
          action: 'buy',
          signalName: 'buy',
          ticker: 'SOL'
        });

      expect(response.status).toBe(200);

      // Wait for job completion (longer timeout for actual trading operations)
      const jobCompleted = await waitForJobCompletion(15000); // 15 seconds for trading operations
      expect(jobCompleted).toBe(true);

      // Verify all signals are reset to 0 after position open
      const signals = await getAgentSignals(testAgentId);
      expect(signals?.buy).toBe(0);
      expect(signals?.sell).toBe(0);
      expect(signals?.buyOpenBar).toBe(0);
      expect(signals?.sellOpenBar).toBe(0);
    });

    it('should open sell position and reset all signals when sell action has sufficient confirmations', async () => {
      // Create agent with sell signals at open threshold
      await createTestAgent(testAgentId, { buy: 1, sell: 2, buyOpenBar: 0, sellOpenBar: 0 });

      // Send sell action that should trigger position open and reset
      const response = await request(app)
        .post(`/webhook/${testAgentId}`)
        .send({
          action: 'sell',
          signalName: 'sell',
          ticker: 'SOL'
        });

      expect(response.status).toBe(200);

      // Wait for job completion (longer timeout for actual trading operations)
      const jobCompleted = await waitForJobCompletion(15000); // 15 seconds for trading operations
      expect(jobCompleted).toBe(true);

      // Verify all signals are reset to 0 after position open
      const signals = await getAgentSignals(testAgentId);
      expect(signals?.buy).toBe(0);
      expect(signals?.sell).toBe(0);
      expect(signals?.buyOpenBar).toBe(0);
      expect(signals?.sellOpenBar).toBe(0);
    });

    it('should not open position when buy confirmations are insufficient', async () => {
      // Create agent with buy signals below open threshold
      await createTestAgent(testAgentId, { buy: 1, sell: 0, buyOpenBar: 0, sellOpenBar: 0 });

      // Send buy action that should NOT trigger position open
      const response = await request(app)
        .post(`/webhook/${testAgentId}`)
        .send({
          action: 'buy',
          signalName: 'buy',
          ticker: 'SOL'
        });

      expect(response.status).toBe(200);

      // Wait for job completion (longer timeout for actual trading operations)
      const jobCompleted = await waitForJobCompletion(15000); // 15 seconds for trading operations
      expect(jobCompleted).toBe(true);

      // Verify signals are NOT reset (position was not opened)
      const signals = await getAgentSignals(testAgentId);
      expect(signals?.buy).toBe(1); // Should remain unchanged
    });

    it('should not open position when sell confirmations are insufficient', async () => {
      // Create agent with sell signals below open threshold
      await createTestAgent(testAgentId, { buy: 0, sell: 1, buyOpenBar: 0, sellOpenBar: 0 });

      // Send sell action that should NOT trigger position open
      const response = await request(app)
        .post(`/webhook/${testAgentId}`)
        .send({
          action: 'sell',
          signalName: 'sell',
          ticker: 'SOL'
        });

      expect(response.status).toBe(200);

      // Wait for job completion (longer timeout for actual trading operations)
      const jobCompleted = await waitForJobCompletion(15000); // 15 seconds for trading operations
      expect(jobCompleted).toBe(true);

      // Verify signals are NOT reset (position was not opened)
      const signals = await getAgentSignals(testAgentId);
      expect(signals?.sell).toBe(1); // Should remain unchanged
    });
  });
  describe('Close Position Operations', () => {
    const testAgentId = 'test-agent-close-123';

    it('should trigger close sell position when buy confirmations reach close threshold', async () => {
      // Create agent with buy signals at close threshold - 1
      await createTestAgent(testAgentId, { buy: 1, sell: 0, buyOpenBar: 0, sellOpenBar: 0 });

      // Send buy confirmation that should trigger close sell
      const response = await request(app)
        .post(`/webhook/${testAgentId}`)
        .send({
          action: 'confirmationMinute',
          signalName: 'buy',
          ticker: 'SOL'
        });

      expect(response.status).toBe(200);

      // Wait for job completion (longer timeout for trading operations)
      const jobCompleted = await waitForJobCompletion(15000); // 15 seconds for trading operations
      expect(jobCompleted).toBe(true);

      // Verify buy signal incremented to threshold (2)
      const signals = await getAgentSignals(testAgentId);
      expect(signals?.buy).toBe(2);
    });

    it('should trigger close buy position when sell confirmations reach close threshold', async () => {
      // Create agent with sell signals at close threshold - 1
      await createTestAgent(testAgentId, { buy: 0, sell: 1, buyOpenBar: 0, sellOpenBar: 0 });

      // Send sell confirmation that should trigger close buy
      const response = await request(app)
        .post(`/webhook/${testAgentId}`)
        .send({
          action: 'confirmationMinute',
          signalName: 'sell',
          ticker: 'SOL'
        });

      expect(response.status).toBe(200);

      // Wait for job completion (longer timeout for trading operations)
      const jobCompleted = await waitForJobCompletion(15000); // 15 seconds for trading operations
      expect(jobCompleted).toBe(true);

      // Verify sell signal incremented to threshold (2)
      const signals = await getAgentSignals(testAgentId);
      expect(signals?.sell).toBe(2);
    });

  });


});