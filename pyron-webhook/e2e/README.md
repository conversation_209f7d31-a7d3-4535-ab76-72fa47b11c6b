# E2E Test Setup

This guide explains how to set up and run end-to-end tests for the Pyron Webhook service.

## Prerequisites

E2E tests require MongoDB and Redis servers. The simplest setup is using Docker:

```bash
# Start MongoDB and Redis with Docker
docker run -d --name test-mongo -p 27017:27017 -e MONGO_INITDB_ROOT_USERNAME=test -e MONGO_INITDB_ROOT_PASSWORD=test mongo:latest
docker run -d --name test-redis -p 6379:6379 redis:latest
```

## Environment Configuration

Create these environment files in this `e2e/` directory:

### `e2e/.env.test.database`
```env
MONGO_USER=test
MONGO_PASSWORD=test
MONGO_HOST=localhost
MONGO_PORT=27017
MONGO_DB=pyron-test
```

### `e2e/.env.test.server`
```env
REDIS_URL=redis://localhost:6379
WORKER_CONCURRENCY=1
PORT=3001
```

### `e2e/.env.test.security`
```env
ALLOWED_ORIGINS=http://localhost:3000
ALLOWED_IPS=
RPC_URL=https://api.mainnet-beta.solana.com
```

### `e2e/.env.test.admin`
```env
ADMIN_KEY=your_test_private_key_here
TEST_AGENT_PUBKEY=your_test_agent_public_key_here
```

### `e2e/.env.test` (optional overrides)
```env
TEST_TIMEOUT=30000
```

## Important Notes

### Account Requirements (Mainnet)
- **TEST_AGENT_PUBKEY**: Must be a Solana public key of an account that has a Drift subaccount delegated to the admin account
- **ADMIN_KEY**: Private key for signing transactions (use test keys only)
- **Mainnet Testing**: E2E tests run on Solana mainnet with real accounts

### Balance Requirements
- **Admin Wallet**: Should have at least 0.01 SOL to cover transaction fees
- **Drift Subaccount**: Should have approximately $5 USD deposited for trading operations
- Use minimal amounts for testing to limit risk exposure

### Security
- These environment files are only needed for e2e tests, not unit tests
- Never commit real private keys to version control
- Use dedicated test accounts, not production wallets

## Running E2E Tests

```bash
npm run test:e2e
```

The tests will validate environment setup, wallet configuration, and webhook processing functionality.
