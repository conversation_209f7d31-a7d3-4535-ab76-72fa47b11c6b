import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';

/**
 * Load e2e test environment variables from multiple files in a specific order
 * E2E tests run in production-like environment (NODE_ENV is not forced to 'test')
 * Files are loaded in this order:
 * 1. .env.test.database (database configuration)
 * 2. .env.test.server (server configuration)
 * 3. .env.test.security (security configuration)
 * 4. .env.test.admin (admin/sensitive configuration)
 * 5. .env.test (main test configuration and overrides)
 */
export function loadTestEnvironment() {
  // CRITICAL: Override Jest's automatic NODE_ENV=test setting immediately
  process.env.NODE_ENV = 'e2e';

  const envDir = path.resolve(__dirname, '../../e2e');

  // Default test values that should always be present
  // Note: NODE_ENV is explicitly set to 'e2e' for e2e tests to ensure production-like behavior
  const defaultTestConfig = {
    NODE_ENV: 'e2e', // Explicitly set to 'e2e' to avoid test mode behavior
    MONGO_USER: 'test',
    MONGO_PASSWORD: 'test',
    MONGO_HOST: 'localhost',
    MONGO_PORT: '27017',
    MONGO_DB: 'pyron-test',
    REDIS_URL: 'redis://localhost:6379',
    WORKER_CONCURRENCY: '1',
    TEST_TIMEOUT: '30000',
    // ADMIN_KEY: 'REQUIRED - Set in .env.test.admin file for security'
    // TEST_AGENT_PUBKEY: 'REQUIRED - Set in .env.test.admin file - should match webhookData.address'
    RPC_URL: 'https://api.mainnet-beta.solana.com',
    ALLOWED_ORIGINS: 'http://localhost:3000',
    ALLOWED_IPS: '',
    PORT: '3001'
  };

  // Load files in order of precedence
  const envFiles = [
    path.join(envDir, '.env.test.database'),
    path.join(envDir, '.env.test.server'),
    path.join(envDir, '.env.test.security'),
    path.join(envDir, '.env.test.admin'),
    path.join(envDir, '.env.test')
  ];

  // Load each file if it exists
  envFiles.forEach(envFile => {
    if (fs.existsSync(envFile)) {
      dotenv.config({ path: envFile });
    }
  });

  // Apply default values if environment variables are not set
  Object.entries(defaultTestConfig).forEach(([key, value]) => {
    if (!process.env[key]) {
      process.env[key] = value;
    }
  });

  // Explicitly ensure NODE_ENV is not 'test' for e2e tests to avoid test mode behavior
  process.env.NODE_ENV = 'e2e';

  // Validate required test environment variables
  const requiredVars = {
    database: [
      'MONGO_USER',
      'MONGO_PASSWORD',
      'MONGO_HOST',
      'MONGO_PORT',
      'MONGO_DB'
    ],
    server: [
      'REDIS_URL',
      'WORKER_CONCURRENCY'
    ],
    security: [
      'ALLOWED_ORIGINS',
      // Add any required security variables here
    ],
    admin: [
      'ADMIN_KEY',
      'TEST_AGENT_PUBKEY',
      'RPC_URL'
      // Add any required admin variables here
    ]
  };

  // Check for missing required variables by category
  Object.entries(requiredVars).forEach(([category, vars]) => {
    const missingVars = vars.filter(varName => !process.env[varName]);
    if (missingVars.length > 0) {
      if (category === 'admin' && (missingVars.includes('ADMIN_KEY') || missingVars.includes('TEST_AGENT_PUBKEY'))) {
        throw new Error(`
SECURITY NOTICE: Admin credentials are required for e2e tests but must NOT be hardcoded.

Please create: e2e/.env.test.admin
Add:
ADMIN_KEY=your_test_private_key_here
TEST_AGENT_PUBKEY=your_test_agent_public_key_here

Notes:
- ADMIN_KEY: Private key for signing transactions (generate with Keypair.generate().secretKey)
- TEST_AGENT_PUBKEY: Public key that should match webhookData.address in test requests
- These should correspond to actual test accounts with Drift positions/subaccounts

NEVER commit real private keys to version control!
        `);
      }
      throw new Error(`Missing required test environment variables in ${category}: ${missingVars.join(', ')}`);
    }
  });
} 