import { alertQueue } from '../../src/queue/queue';
import Agent from '../../src/databaseModels/agent';
import { logInfo } from '../../src/utils/logger';
import { PublicKey } from '@solana/web3.js';

export async function createTestAgent(agentId: string, signals = { buy: 0, sell: 0, buyOpenBar: 0, sellOpenBar: 0 }) {
  // Get the test agent's public key - this should match webhookData.address
  const testAgentPubkey = process.env.TEST_AGENT_PUBKEY;
  if (!testAgentPubkey) {
    throw new Error(`
TEST_AGENT_PUBKEY environment variable is required for e2e tests.
Please create: e2e/.env.test.admin
Add: TEST_AGENT_PUBKEY=your_test_agent_public_key_here

This should match the address used in webhookData.address for test requests.
    `);
  }

  // Validate that it's a valid Solana public key
  try {
    new PublicKey(testAgentPubkey);
  } catch (error) {
    throw new Error(`Invalid TEST_AGENT_PUBKEY format: ${error}`);
  }

  const agent = new Agent({
    agentName: 'test-agent',
    botId: agentId,
    signals,
    assetPair: 'SOL', // Use valid ticker from PERP_MARKETS
    tradingStatus: 'on', // Required for processing
    pubkey: testAgentPubkey, // This should match webhookData.address in test requests
    chatId: 'test-chat-id',
    number: 1, // Subaccount ID for Drift
    requiredBuyConfirmationsOpen: 2,
    requiredBuyConfirmationsClose: 2,
    requiredBuyConfirmationsResetCounter: 2,
    requiredBuyConfirmationsOverride: 2,
    requiredSellConfirmationsOpen: 2,
    requiredSellConfirmationsClose: 2,
    requiredSellConfirmationsResetCounter: 2,
    requiredSellConfirmationsOverride: 2,
    deposit: 5,
    sellReportPrompt: 'test-prompt',
    buyReportPrompt: 'test-prompt',
    hypothesisStatus: 'off',
  });
  await agent.save();
  logInfo(`Created test agent with ID: ${agentId} using pubkey: ${testAgentPubkey}`);
  return agent;
}

export async function waitForJobCompletion(timeout = 5000) {
  const startTime = Date.now();
  while (Date.now() - startTime < timeout) {
    const jobCounts = await alertQueue.getJobCounts();
    if (jobCounts.active === 0 && jobCounts.waiting === 0) {
      return true;
    }
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  return false;
}

export async function getAgentSignals(agentId: string) {
  const agent = await Agent.findOne({ botId: agentId });
  return agent?.signals;
}

export async function getAgentAuthority(agentId: string) {
  const agent = await Agent.findOne({ botId: agentId });
  if (!agent) {
    throw new Error(`Agent not found: ${agentId}`);
  }
  return agent.pubkey; // This is the agent's authority public key
}

export async function clearTestData() {
  await Agent.deleteMany({});
  await alertQueue.obliterate({ force: true });
}