import { loadTestEnvironment } from './loadTestEnv';

// Load test environment variables
loadTestEnvironment();

// Increase timeout for all tests
jest.setTimeout(parseInt(process.env.TEST_TIMEOUT || '30000'));

// Global beforeAll and afterAll hooks
beforeAll(async () => {
  // E2E tests should run in production-like environment
  // Do not set NODE_ENV to 'test' as it would mock dependencies
  console.log('Starting E2E tests in production-like environment');
});

afterAll(async () => {
  // Clean up any remaining resources
  console.log('E2E tests completed');
});