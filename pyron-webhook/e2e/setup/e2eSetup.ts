import mongoose from 'mongoose';
import { alertQueue } from '../../src/queue/queue';
import { logInfo, logError } from '../../src/utils/logger';
import { loadTestEnvironment } from './loadTestEnv';

// Load test environment variables
loadTestEnvironment();

export async function setupE2E() {

  try {
    // Connect to MongoDB
    const username = encodeURIComponent(process.env.MONGO_USER || 'test_user');
    const password = encodeURIComponent(process.env.MONGO_PASSWORD || 'test_password');
    const dbName = process.env.MONGO_DB || 'webhook_test_db';
    const host = process.env.MONGO_HOST || 'localhost';
    const portDb = process.env.MONGO_PORT || '27017';
    const mongoUri = `mongodb://${username}:${password}@${host}:${portDb}/${dbName}?authSource=admin`;

    await mongoose.connect(mongoUri, {
      connectTimeoutMS: 20000,
      socketTimeoutMS: 45000,
      maxPoolSize: 50,
      retryWrites: true,
    });
    logInfo('MongoDB connected for e2e tests');

    // Clear the queue before tests
    await alertQueue.obliterate({ force: true });
    logInfo('Queue cleared for e2e tests');

    return {
      mongoose,
    };
  } catch (error) {
    logError('Error in e2e setup:', error);
    throw error;
  }
}

export async function teardownE2E() {
  try {
    // Close MongoDB connection (worker is managed by main.ts)
    await mongoose.connection.close();
    logInfo('E2E test teardown completed');
  } catch (error) {
    logError('Error in e2e teardown:', error);
    throw error;
  }
}