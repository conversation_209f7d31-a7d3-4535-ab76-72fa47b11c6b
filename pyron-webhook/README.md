# Pyron Webhook Service

A robust webhook service built with Express.js and MongoDB for handling webhook events and processing them asynchronously.

## Features

- Express.js based webhook endpoint
- MongoDB integration for data persistence
- Redis-based queue system for asynchronous processing
- IP filtering middleware for security
- CORS enabled for secure cross-origin requests
- TypeScript support
- Comprehensive test suite
- Solana blockchain integration via Drift protocol

## Prerequisites

- Node.js (v18 or higher)
- MongoDB instance
- Redis instance
- npm package manager

## Environment Variables

To run the project, create these environment files in the root directory:

**`.env.admin`** (for sensitive Solana configuration):
```env
ADMIN_KEY=your_solana_private_key_base58
```

**`.env.database`** (for MongoDB configuration):
```env
MONGO_USER=your_mongodb_username
MONGO_PASSWORD=your_mongodb_password
MONGO_HOST=your_mongodb_host
MONGO_DB=your_database_name
MONGO_PORT=27017
```

**`.env.server`** (for server and Redis configuration):
```env
PORT=3000
REDIS_URL=redis://127.0.0.1:6379
WORKER_CONCURRENCY=5
RPC_URL=https://your-solana-rpc-endpoint
```

**`.env.security`** (for security configuration):
```env
ALLOWED_ORIGINS=https://yourdomain.com,http://localhost:3000
ALLOWED_IPS=*************,************,*************,***********
NODE_ENV=production
```

**Note**: These environment files are only needed to run the actual project. Tests do not require any environment files as all dependencies are mocked.

**TradingView Webhook IPs**: `*************`, `************`, `*************`, `***********`

## Installation

```bash
git clone <repository-url>/pyron-webhook.git
cd pyron-webhook
npm install
```

## Running the Project

```bash
npm run start
```

## Running Tests

```bash
# Run unit tests
npm test

# Run e2e tests
npm run test:e2e
```

For detailed test information, see [test_plan.md](./test_plan.md).

For e2e test setup instructions, see [e2e/README.md](./e2e/README.md).

## API Endpoints

- `POST /webhook/:agentId` - Webhook endpoint for receiving events

**Request Format:**
```json
{
  "action": "confirmationMinute",
  "signalName": "buy",
  "ticker": "SOL"
}
```
