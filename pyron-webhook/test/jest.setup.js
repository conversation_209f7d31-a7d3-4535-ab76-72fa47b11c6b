// Jest setup file for unit tests

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.ADMIN_KEY = 'test_key_for_testing_only';
process.env.RPC_URL = 'https://mock-solana-rpc.test';
process.env.MONGO_HOST = 'localhost';
process.env.MONGO_USER = 'test';
process.env.MONGO_PASSWORD = 'test';
process.env.MONGO_DB = 'pyron-test';
process.env.MONGO_PORT = '27017';
process.env.PORT = '0';
process.env.REDIS_URL = 'redis://127.0.0.1:6379';
process.env.WORKER_CONCURRENCY = '3';
process.env.ALLOWED_ORIGINS = 'http://localhost:3000,http://localhost:3004';
process.env.ALLOWED_IPS = '127.0.0.1,::1';

// Mock the queue module for unit tests to avoid Redis dependency
jest.mock('../src/queue/queue', () => {
  const { mockAlertQueue } = require('./mocks/mockQueue');
  return {
    alertQueue: mockAlertQueue
  };
});

// Mock the worker module for unit tests
jest.mock('../src/queue/worker', () => ({
  createWorker: jest.fn().mockReturnValue({
    worker: {
      close: jest.fn().mockResolvedValue(true),
      on: jest.fn()
    },
    connection: {
      disconnect: jest.fn().mockResolvedValue(true),
      quit: jest.fn().mockResolvedValue(true)
    }
  })
}));

// Mock mongoose for unit tests to avoid MongoDB dependency
jest.mock('mongoose', () => {
  const mockConnection = {
    readyState: 1, // Connected state
    close: jest.fn().mockResolvedValue(true),
    on: jest.fn(),
    once: jest.fn()
  };

  // Create a mock model constructor that can be used with 'new'
  const createMockModel = () => {
    const MockModel = jest.fn().mockImplementation(function(data) {
      // Create instance with data and mock methods
      Object.assign(this, data);
      this.save = jest.fn().mockResolvedValue(this);
      this.remove = jest.fn().mockResolvedValue(this);
      this.deleteOne = jest.fn().mockResolvedValue(this);
      return this;
    });

    // Add static methods to the constructor
    MockModel.findOne = jest.fn().mockReturnValue({
      sort: jest.fn().mockReturnValue({
        lean: jest.fn().mockResolvedValue(null)
      })
    });
    MockModel.find = jest.fn().mockResolvedValue([]);
    MockModel.create = jest.fn().mockResolvedValue({});
    MockModel.updateOne = jest.fn().mockResolvedValue({ acknowledged: true });
    MockModel.deleteOne = jest.fn().mockResolvedValue({ acknowledged: true });
    MockModel.countDocuments = jest.fn().mockResolvedValue(0);

    // Add prototype methods
    MockModel.prototype.save = jest.fn().mockResolvedValue({});
    MockModel.prototype.remove = jest.fn().mockResolvedValue({});
    MockModel.prototype.deleteOne = jest.fn().mockResolvedValue({});

    return MockModel;
  };

  return {
    connect: jest.fn().mockResolvedValue(true),
    connection: mockConnection,
    model: jest.fn().mockImplementation(createMockModel),
    Schema: jest.fn().mockImplementation(() => ({})),
    Types: {
      ObjectId: jest.fn().mockImplementation((id) => id || 'mock-object-id')
    }
  };
});

// Mock specific database models
jest.mock('../src/databaseModels/log', () => {
  const MockLog = jest.fn().mockImplementation(function(data) {
    Object.assign(this, data);
    this.save = jest.fn().mockResolvedValue(this);
    return this;
  });

  MockLog.findOne = jest.fn().mockReturnValue({
    sort: jest.fn().mockReturnValue({
      lean: jest.fn().mockResolvedValue(null)
    })
  });
  MockLog.find = jest.fn().mockResolvedValue([]);
  MockLog.create = jest.fn().mockResolvedValue({});
  MockLog.updateOne = jest.fn().mockResolvedValue({ acknowledged: true });
  MockLog.deleteOne = jest.fn().mockResolvedValue({ acknowledged: true });
  MockLog.countDocuments = jest.fn().mockResolvedValue(0);

  MockLog.prototype.save = jest.fn().mockResolvedValue({});

  return MockLog;
});

jest.mock('../src/databaseModels/agent', () => {
  const MockAgent = jest.fn().mockImplementation(function(data) {
    Object.assign(this, data);
    this.save = jest.fn().mockResolvedValue(this);
    return this;
  });

  MockAgent.findOne = jest.fn().mockResolvedValue(null);
  MockAgent.find = jest.fn().mockResolvedValue([]);
  MockAgent.create = jest.fn().mockResolvedValue({});
  MockAgent.updateOne = jest.fn().mockResolvedValue({ acknowledged: true });
  MockAgent.deleteOne = jest.fn().mockResolvedValue({ acknowledged: true });
  MockAgent.countDocuments = jest.fn().mockResolvedValue(0);

  MockAgent.prototype.save = jest.fn().mockResolvedValue({});

  return MockAgent;
});

// Set Jest timeout
jest.setTimeout(30000);

console.log('🧪 Unit test environment loaded with mocked queue and database');
console.log(`📊 MongoDB: MOCKED (no real MongoDB connection for unit tests)`);
console.log(`🔴 Redis: MOCKED (no real Redis connection for unit tests)`);
