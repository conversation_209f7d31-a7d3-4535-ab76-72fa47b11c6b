import axios from 'axios';
import { getCurrentPrice } from '../../../src/utils/drift/getPerpPrice';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock the logger
jest.mock('../../../src/utils/logger', () => ({
  logInfo: jest.fn(),
  logError: jest.fn(),
  logWarn: jest.fn(),
}));

describe('getPerpPrice', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Valid price data fetching', () => {
    it('should fetch current price successfully with valid market data', async () => {
      const mockResponse = {
        data: {
          bids: [{ price: '100000000' }], // 100 * 10^6
          asks: [{ price: '102000000' }], // 102 * 10^6
        },
      };
      mockedAxios.get.mockResolvedValue(mockResponse);

      const result = await getCurrentPrice('SOL');

      expect(mockedAxios.get).toHaveBeenCalledWith(
        'https://dlob.drift.trade/l2?marketName=SOL-PERP&depth=10&includeOracle=true&includeVamm=true',
        { timeout: 10000 }
      );
      expect(result).toBe(101); // (100 + 102) / 2
    });

    it('should calculate correct mean price from bid and ask', async () => {
      const mockResponse = {
        data: {
          bids: [{ price: '50000000' }], // 50 * 10^6
          asks: [{ price: '60000000' }], // 60 * 10^6
        },
      };
      mockedAxios.get.mockResolvedValue(mockResponse);

      const result = await getCurrentPrice('BTC');

      expect(result).toBe(55); // (50 + 60) / 2
    });

    it('should handle multiple bids and asks by using first ones', async () => {
      const mockResponse = {
        data: {
          bids: [
            { price: '100000000' },
            { price: '99000000' },
          ],
          asks: [
            { price: '102000000' },
            { price: '103000000' },
          ],
        },
      };
      mockedAxios.get.mockResolvedValue(mockResponse);

      const result = await getCurrentPrice('ETH');

      expect(result).toBe(101); // Uses first bid (100) and first ask (102)
    });
  });

  describe('Market not found scenarios', () => {
    it('should throw error when no bids are available', async () => {
      const mockResponse = {
        data: {
          bids: [],
          asks: [{ price: '100000000' }],
        },
      };
      mockedAxios.get.mockResolvedValue(mockResponse);

      await expect(getCurrentPrice('INVALID')).rejects.toThrow(
        'Failed to fetch market price for INVALID'
      );
    });

    it('should throw error when no asks are available', async () => {
      const mockResponse = {
        data: {
          bids: [{ price: '100000000' }],
          asks: [],
        },
      };
      mockedAxios.get.mockResolvedValue(mockResponse);

      await expect(getCurrentPrice('INVALID')).rejects.toThrow(
        'Failed to fetch market price for INVALID'
      );
    });
  });

  describe('Price data validation', () => {
    it('should handle string prices correctly', async () => {
      const mockResponse = {
        data: {
          bids: [{ price: '123456789' }],
          asks: [{ price: '234567890' }],
        },
      };
      mockedAxios.get.mockResolvedValue(mockResponse);

      const result = await getCurrentPrice('SOL');

      expect(result).toBeCloseTo(179.012339, 5); // (123.456789 + 234.56789) / 2
    });

    it('should handle numeric prices correctly', async () => {
      const mockResponse = {
        data: {
          bids: [{ price: 100000000 }],
          asks: [{ price: 200000000 }],
        },
      };
      mockedAxios.get.mockResolvedValue(mockResponse);

      const result = await getCurrentPrice('SOL');

      expect(result).toBe(150); // (100 + 200) / 2
    });

    it('should handle zero prices', async () => {
      const mockResponse = {
        data: {
          bids: [{ price: '0' }],
          asks: [{ price: '100000000' }],
        },
      };
      mockedAxios.get.mockResolvedValue(mockResponse);

      const result = await getCurrentPrice('SOL');

      expect(result).toBe(50); // (0 + 100) / 2
    });
  });

  describe('Network failure recovery', () => {
    it('should handle network timeout errors', async () => {
      mockedAxios.get.mockRejectedValue(new Error('timeout of 10000ms exceeded'));

      await expect(getCurrentPrice('SOL')).rejects.toThrow(
        'Failed to fetch market price for SOL'
      );
    });

    it('should handle network connection errors', async () => {
      mockedAxios.get.mockRejectedValue(new Error('Network Error'));

      await expect(getCurrentPrice('SOL')).rejects.toThrow(
        'Failed to fetch market price for SOL'
      );
    });


    it('should log appropriate error messages on failure', async () => {
      const { logError } = require('../../../src/utils/logger');
      const error = new Error('Network timeout');
      mockedAxios.get.mockRejectedValue(error);

      await expect(getCurrentPrice('SOL')).rejects.toThrow();

      expect(logError).toHaveBeenCalledWith(
        'Failed to fetch market price for SOL:',
        error
      );
    });

    it('should log warning when no market data is available', async () => {
      const { logWarn } = require('../../../src/utils/logger');
      const mockResponse = {
        data: {
          bids: [],
          asks: [],
        },
      };
      mockedAxios.get.mockResolvedValue(mockResponse);

      await expect(getCurrentPrice('SOL')).rejects.toThrow();

      expect(logWarn).toHaveBeenCalledWith('No bids or asks found for SOL');
    });
  });
});
