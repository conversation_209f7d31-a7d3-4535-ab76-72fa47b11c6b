// Mock BN first to avoid constructor issues
jest.mock('bn.js', () => {
  const mockBN = jest.fn().mockImplementation((value) => ({
    toNumber: () => Number(value),
    toString: () => String(value),
    add: jest.fn().mockReturnThis(),
    sub: jest.fn().mockReturnThis(),
    mul: jest.fn().mockReturnThis(),
    div: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnValue(true),
    gt: jest.fn().mockReturnValue(false),
    lt: jest.fn().mockReturnValue(false),
    gte: jest.fn().mockReturnValue(true),
    lte: jest.fn().mockReturnValue(true),
  }));
  
  return {
    BN: mockBN,
    default: mockBN,
  };
});

// Mock Solana Web3.js completely
jest.mock('@solana/web3.js', () => ({
  PublicKey: jest.fn().mockImplementation((key) => ({
    toBase58: () => typeof key === 'string' ? key : '********************************',
    toString: () => typeof key === 'string' ? key : '********************************',
    equals: jest.fn().mockReturnValue(true),
  })),
  Keypair: {
    generate: jest.fn().mockReturnValue({
      publicKey: {
        toBase58: () => '********************************',
        toString: () => '********************************',
      },
      secretKey: new Uint8Array(64),
    }),
  },
  Connection: jest.fn(),
  Transaction: jest.fn().mockImplementation(() => ({
    add: jest.fn().mockReturnThis(),
    sign: jest.fn(),
    lastValidBlockHeight: undefined,
    recentBlockhash: undefined,
    feePayer: undefined,
  })),
  TransactionMessage: jest.fn().mockImplementation(() => ({
    compileToV0Message: jest.fn().mockReturnValue({}),
  })),
  VersionedTransaction: jest.fn().mockImplementation(() => ({
    sign: jest.fn(),
  })),
  ComputeBudgetProgram: {
    setComputeUnitLimit: jest.fn().mockReturnValue({}),
    setComputeUnitPrice: jest.fn().mockReturnValue({}),
  },
  SystemProgram: {
    transfer: jest.fn(),
  },
  LAMPORTS_PER_SOL: 1000000000,
}));

// Mock the external dependencies
jest.mock('@drift-labs/sdk', () => ({
  DriftClient: jest.fn(),
  Wallet: jest.fn(),
  DRIFT_PROGRAM_ID: 'DRiFtupJYLTosbwoN8koMbEYSx54aFAVLddWsbksjwg7',
  OrderType: {
    MARKET: 'market',
    LIMIT: 'limit',
  },
  PositionDirection: {
    LONG: 'long',
    SHORT: 'short',
  },
  AMM_RESERVE_PRECISION_EXP: {
    toNumber: () => 6,
  },
}));

// Mock Light Protocol
jest.mock('@lightprotocol/stateless.js', () => ({
  Rpc: jest.fn().mockImplementation(() => ({
    getLatestBlockhash: jest.fn().mockResolvedValue({
      blockhash: 'mock-blockhash',
      lastValidBlockHeight: 12345,
    }),
  })),
  sendAndConfirmTx: jest.fn(),
}));

// Mock other dependencies
jest.mock('../../../src/utils/drift/getPerpPrice');
jest.mock('../../../src/utils/drift/getClient');
jest.mock('../../../src/utils/getMarketId');
jest.mock('../../../src/utils/getPriorityFees');
jest.mock('../../../src/databaseModels/log');
jest.mock('../../../constant');

// Mock the logger
jest.mock('../../../src/utils/logger', () => ({
  logAgentInfo: jest.fn(),
  logAgentWarn: jest.fn(),
  logAgentError: jest.fn(),
}));

import { PublicKey } from '@solana/web3.js';
import { placeOrder } from '../../../src/utils/drift/placeOrder';
import { getCurrentPrice } from '../../../src/utils/drift/getPerpPrice';
import { createClient } from '../../../src/utils/drift/getClient';
import { getMarketId } from '../../../src/utils/getMarketId';
import { getPriorityFee } from '../../../src/utils/getPriorityFees';
import Log from '../../../src/databaseModels/log';
import { PERP_MARKETS } from '../../../constant';
import { sendAndConfirmTx } from '@lightprotocol/stateless.js';

const mockedGetCurrentPrice = getCurrentPrice as jest.MockedFunction<typeof getCurrentPrice>;
const mockedCreateClient = createClient as jest.MockedFunction<typeof createClient>;
const mockedGetMarketId = getMarketId as jest.MockedFunction<typeof getMarketId>;
const mockedGetPriorityFee = getPriorityFee as jest.MockedFunction<typeof getPriorityFee>;
const mockedSendAndConfirmTx = sendAndConfirmTx as jest.MockedFunction<typeof sendAndConfirmTx>;
const MockedLog = Log as jest.MockedClass<typeof Log>;

// Mock PERP_MARKETS constant
(PERP_MARKETS as any) = {
  BTC: { minAmount: 0.0001 }, // Very small minimum
  SOL: { minAmount: 0.01 },
  ETH: { minAmount: 0.001 },
};

describe('placeOrder', () => {
  let mockDriftClient: any;
  let mockUser: any;
  let mockSigner: any;
  let mockAuthority: PublicKey;
  let mockLog: any;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();

    // Set up environment
    process.env.RPC_URL = 'https://mock-rpc-url.com';

    // Create mock objects
    mockSigner = {
      publicKey: {
        toBase58: () => '********************************',
      },
    };
    mockAuthority = new (PublicKey as any)('22222222222222222222222222222222');

    // Mock user with position data
    let positionCallCount = 0;
    mockUser = {
      getPerpPosition: jest.fn().mockImplementation(() => {
        // Simulate position change after first call to break the waiting loop
        const baseAmount = positionCallCount < 2 ? 1000000 : 2000000;
        positionCallCount++;
        return {
          baseAssetAmount: {
            toNumber: () => baseAmount,
          },
        };
      }),
      getTotalCollateral: jest.fn().mockReturnValue({
        toNumber: () => ***********, // 50,000 USDC in precision (6 decimals)
      }),
      fetchAccounts: jest.fn().mockResolvedValue(undefined),
    };
    
    // Mock drift client
    mockDriftClient = {
      subscribe: jest.fn().mockResolvedValue(undefined),
      unsubscribe: jest.fn().mockResolvedValue(undefined),
      getUser: jest.fn().mockReturnValue(mockUser),
      convertToPerpPrecision: jest.fn().mockImplementation((value) => ({ toNumber: () => value * 1000000 })),
      convertToPricePrecision: jest.fn().mockImplementation((value) => ({ toNumber: () => value * 1000000 })),
      getPlacePerpOrderIx: jest.fn().mockResolvedValue({}),
    };
    
    // Mock log
    mockLog = {
      _id: 'mock-log-id',
      side: 'LONG',
      market: 'BTC-PERP',
      status: 'success',
      save: jest.fn().mockResolvedValue(undefined),
    };
    
    // Set up default mocks
    mockedCreateClient.mockResolvedValue(mockDriftClient);
    mockedGetCurrentPrice.mockResolvedValue(50000);
    mockedGetMarketId.mockReturnValue(0);
    mockedGetPriorityFee.mockResolvedValue(100000);
    mockedSendAndConfirmTx.mockResolvedValue('mock-transaction-signature');
    MockedLog.mockImplementation(() => mockLog);
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('Core functionality tests', () => {
    it('should return undefined when RPC_URL is missing', async () => {
      // Test missing RPC_URL
      delete process.env.RPC_URL;
      const result = await placeOrder('buy', 50, 0, 'BTC', mockSigner, 'test-chat-id', 'buy-signal', mockAuthority, 'test-bot-id');
      expect(result).toBeUndefined();
    });

    it('should return undefined when market is invalid', async () => {
      // Test invalid market
      mockedGetMarketId.mockReturnValue(undefined);
      const result = await placeOrder('buy', 50, 0, 'INVALID', mockSigner, 'test-chat-id', 'buy-signal', mockAuthority, 'test-bot-id');
      expect(result).toBeUndefined();
    });

    it('should call required functions in correct order', async () => {
      // Mock sendAndConfirmTx to return immediately
      mockedSendAndConfirmTx.mockResolvedValue(undefined as any);

      await placeOrder('buy', 50, 0, 'BTC', mockSigner, 'test-chat-id', 'buy-signal', mockAuthority, 'test-bot-id');

      // Verify function calls
      expect(mockedCreateClient).toHaveBeenCalledWith(expect.any(Object), mockSigner, mockAuthority);
      expect(mockDriftClient.subscribe).toHaveBeenCalled();
      expect(mockDriftClient.getUser).toHaveBeenCalledWith(0, mockAuthority);
      expect(mockedGetCurrentPrice).toHaveBeenCalledWith('BTC');
      expect(mockedGetMarketId).toHaveBeenCalledWith('BTC');
      expect(mockDriftClient.getPlacePerpOrderIx).toHaveBeenCalled();
      expect(mockedSendAndConfirmTx).toHaveBeenCalled();
      expect(mockLog.save).toHaveBeenCalled();
      expect(mockDriftClient.unsubscribe).toHaveBeenCalled();
    });

    it('should handle different order directions based on position logic', async () => {
      mockedSendAndConfirmTx.mockResolvedValue(undefined as any);

      // Current test setup:
      // - totalCollateral = *********** / 50000 / 10^6 = 1 BTC equivalent
      // - positionSize = 1000000 / 10^6 = 1 BTC (LONG position)
      // - For buy 50%: newOrderSize = 0.5, orderSize = 0.5 - 1 = -0.5 → SHORT direction
      // - For sell 50%: direction starts as SHORT, orderSize = 0.5 + 1 = 1.5 → SHORT direction

      // Test buy order with existing long position
      await placeOrder('buy', 50, 0, 'BTC', mockSigner, 'test-chat-id', 'buy-signal', mockAuthority, 'test-bot-id');
      expect(mockDriftClient.getPlacePerpOrderIx).toHaveBeenLastCalledWith(
        expect.objectContaining({ direction: 'short' }), // Correct: reduces long position
        0
      );

      // Test sell order with existing long position
      await placeOrder('sell', 50, 0, 'BTC', mockSigner, 'test-chat-id', 'sell-signal', mockAuthority, 'test-bot-id');
      expect(mockDriftClient.getPlacePerpOrderIx).toHaveBeenLastCalledWith(
        expect.objectContaining({ direction: 'short' }), // Correct: adds to short direction
        0
      );
    });

    it('should handle client creation failure', async () => {
      // Test client creation failure
      mockedCreateClient.mockResolvedValue(null);
      const result = await placeOrder('buy', 50, 0, 'BTC', mockSigner, 'test-chat-id', 'buy-signal', mockAuthority, 'test-bot-id');
      expect(result).toBeUndefined();
    });

    it('should handle price fetch failure', async () => {
      // Test price fetch failure
      mockedGetCurrentPrice.mockRejectedValue(new Error('Price fetch failed'));
      const result = await placeOrder('buy', 50, 0, 'BTC', mockSigner, 'test-chat-id', 'buy-signal', mockAuthority, 'test-bot-id');
      expect(result).toBeUndefined();
      expect(mockDriftClient.unsubscribe).toHaveBeenCalled();
    });
  });

});
