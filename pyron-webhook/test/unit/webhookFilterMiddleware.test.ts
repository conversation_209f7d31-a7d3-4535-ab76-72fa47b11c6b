import { Request, Response, NextFunction } from 'express';
import { webhookFilterMiddleware } from '../../src/middleware/webhookFilterMiddleware';
import Agent from '../../src/databaseModels/agent';

// Mock the Agent model
jest.mock('../../src/databaseModels/agent');

// Mock the logger
jest.mock('../../src/utils/logger', () => ({
  logInfo: jest.fn(),
  logWarn: jest.fn(),
  logError: jest.fn(),
}));

describe('Webhook Filter Middleware', () => {
  let req: Partial<Request>;
  let res: Partial<Response>;
  let next: NextFunction;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Setup request, response, and next function
    req = {
      params: { agentId: 'test-agent-id' },
      body: {
        action: 'confirmationMinute',
        signalName: 'buy',
        ticker: 'BTC'
      }
    };

    res = {
      status: jest.fn().mockReturnThis(),
      send: jest.fn()
    };

    next = jest.fn();
  });

  test('should return 400 if agentId is missing', async () => {
    req.params = {}; // No agentId

    await webhookFilterMiddleware(req as Request, res as Response, next);

    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.send).toHaveBeenCalledWith('Missing agentId');
    expect(next).not.toHaveBeenCalled();
  });

  test('should return 400 if action is missing', async () => {
    req.body.action = undefined; // No action

    await webhookFilterMiddleware(req as Request, res as Response, next);

    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.send).toHaveBeenCalledWith('Missing action');
    expect(next).not.toHaveBeenCalled();
  });

  test('should return 400 if signalName is missing for confirmationMinute action', async () => {
    req.body.signalName = undefined; // No signalName

    await webhookFilterMiddleware(req as Request, res as Response, next);

    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.send).toHaveBeenCalledWith('Missing signalName for confirmationMinute action');
    expect(next).not.toHaveBeenCalled();
  });

  test('should return 400 if ticker is missing', async () => {
    req.body.ticker = undefined; // No ticker

    await webhookFilterMiddleware(req as Request, res as Response, next);

    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.send).toHaveBeenCalledWith('Missing ticker');
    expect(next).not.toHaveBeenCalled();
  });

  test('should call next() if action is not confirmationMinute', async () => {
    req.body.action = 'someOtherAction';

    await webhookFilterMiddleware(req as Request, res as Response, next);

    expect(next).toHaveBeenCalled();
    expect(res.status).not.toHaveBeenCalled();
  });

  test('should call next() if signalName is not buy or sell', async () => {
    req.body.signalName = 'someOtherSignal';

    await webhookFilterMiddleware(req as Request, res as Response, next);

    expect(next).toHaveBeenCalled();
    expect(res.status).not.toHaveBeenCalled();
  });

  test('should filter buy signal if signals.buy > max of all buy thresholds', async () => {
    // Mock Agent.findOne to return an agent with signals.buy > max of all thresholds
    const mockLean = jest.fn().mockResolvedValue({
      signals: { buy: 25 },
      requiredBuyConfirmationsOpen: 20,
      requiredBuyConfirmationsClose: 15,
      requiredBuyConfirmationsResetCounter: 4,
      requiredBuyConfirmationsOverride: 10
    });

    (Agent.findOne as jest.Mock).mockReturnValue({
      lean: mockLean
    });

    await webhookFilterMiddleware(req as Request, res as Response, next);

    expect(Agent.findOne).toHaveBeenCalledWith(
      { botId: 'test-agent-id' },
      {
        'signals.buy': 1,
        'requiredBuyConfirmationsOpen': 1,
        'requiredBuyConfirmationsClose': 1,
        'requiredBuyConfirmationsResetCounter': 1,
        'requiredBuyConfirmationsOverride': 1
      }
    );
    expect(res.status).toHaveBeenCalledWith(202);
    expect(res.send).toHaveBeenCalledWith('Request filtered: buy signal threshold already met');
    expect(next).not.toHaveBeenCalled();
  });

  test('should filter sell signal if signals.sell > max of all sell thresholds', async () => {
    req.body.signalName = 'sell';

    // Mock Agent.findOne to return an agent with signals.sell > max of all thresholds
    const mockLean = jest.fn().mockResolvedValue({
      signals: { sell: 25 },
      requiredSellConfirmationsOpen: 20,
      requiredSellConfirmationsClose: 15,
      requiredSellConfirmationsResetCounter: 4,
      requiredSellConfirmationsOverride: 10
    });

    (Agent.findOne as jest.Mock).mockReturnValue({
      lean: mockLean
    });

    await webhookFilterMiddleware(req as Request, res as Response, next);

    expect(Agent.findOne).toHaveBeenCalledWith(
      { botId: 'test-agent-id' },
      {
        'signals.sell': 1,
        'requiredSellConfirmationsOpen': 1,
        'requiredSellConfirmationsClose': 1,
        'requiredSellConfirmationsResetCounter': 1,
        'requiredSellConfirmationsOverride': 1
      }
    );
    expect(res.status).toHaveBeenCalledWith(202);
    expect(res.send).toHaveBeenCalledWith('Request filtered: sell signal threshold already met');
    expect(next).not.toHaveBeenCalled();
  });

  test('should call next() if signals.buy <= max of all buy thresholds', async () => {
    // Mock Agent.findOne to return an agent with signals.buy <= max of all thresholds
    const mockLean = jest.fn().mockResolvedValue({
      signals: { buy: 20 },
      requiredBuyConfirmationsOpen: 20,
      requiredBuyConfirmationsClose: 15,
      requiredBuyConfirmationsResetCounter: 4,
      requiredBuyConfirmationsOverride: 10
    });

    (Agent.findOne as jest.Mock).mockReturnValue({
      lean: mockLean
    });

    await webhookFilterMiddleware(req as Request, res as Response, next);

    expect(next).toHaveBeenCalled();
    expect(res.status).not.toHaveBeenCalled();
  });
});
