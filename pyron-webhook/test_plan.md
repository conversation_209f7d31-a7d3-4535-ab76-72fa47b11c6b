# Unit Test Plan

## Framework
- **Jest** - Primary testing framework
- **Supertest** - HTTP endpoint testing

## Mocked Dependencies
- **MongoDB** - Database operations mocked via jest.mock()
- **Redis/Queue** - Queue operations mocked via jest.mock()
- **Worker** - Worker processes mocked via jest.mock()
- **External APIs** - Solana RPC and Drift SDK mocked

## Existing Tests (All Passing ✅)

### ipFilterMiddleware.test.ts (14 tests)
- should allow requests from allowed IP addresses
- should allow requests from allowed IP addresses (second IP in list)
- should reject requests from disallowed IP addresses
- should handle IP addresses with whitespace in environment variable
- should allow all requests when ALLOWED_IPS is not set
- should allow all requests when ALLOWED_IPS is empty string
- should extract IP from x-forwarded-for header
- should extract IP from x-real-ip header
- should fall back to req.ip when headers are not present
- should fall back to connection.remoteAddress when req.ip is not available
- should handle missing IP address gracefully
- should handle malformed ALLOWED_IPS gracefully
- should allow IPv6 addresses
- should reject disallowed IPv6 addresses

### webhookFilterMiddleware.test.ts (64 tests)
- should return 400 if agentId is missing
- should return 400 if action is missing
- should return 400 if signal<PERSON><PERSON> is missing for confirmationMinute action
- should return 400 if ticker is missing
- should call next() if action is not confirmationMinute
- should call next() if signalName is not buy or sell
- should filter buy signal if signals.buy > max of all buy thresholds
- should filter sell signal if signals.sell > max of all sell thresholds
- should call next() if signals.buy <= max of all buy thresholds

### drift/getClient.test.ts (8 tests)
- should create a drift client successfully with valid parameters
- should log successful client creation
- should handle undefined connection gracefully
- should handle invalid program ID
- should accept valid keypair
- should handle keypair with valid public key
- should handle undefined keypair
- should handle keypair without public key

### drift/placeOrder.test.ts (6 tests)
- should return undefined when RPC_URL is missing
- should return undefined when market is invalid
- should call required functions in correct order
- should handle different order directions based on position logic
- should handle client creation failure
- should handle price fetch failure

### drift/cancelOrders.test.ts (21 tests)
- should cancel all orders when side is not specified
- should cancel only long orders when side is "long"
- should cancel only short orders when side is "short"
- should filter orders by market index correctly
- should handle empty order list gracefully
- should handle no matching orders for market
- should handle no matching orders for side
- should not cancel filled orders
- should not cancel cancelled orders
- should only cancel open orders among mixed statuses
- should handle drift client creation failure
- should handle user fetch failure
- should handle order cancellation errors and continue
- should handle subscription errors
- should handle unsubscribe errors gracefully
- should only cancel perp orders, not spot orders
- should handle mixed market types correctly

### drift/getPerpPrice.test.ts (12 tests)
- should fetch current price successfully with valid market data
- should calculate correct mean price from bid and ask
- should handle multiple bids and asks by using first ones
- should throw error when no bids are available
- should throw error when no asks are available
- should handle string prices correctly
- should handle numeric prices correctly
- should handle zero prices
- should handle network timeout errors
- should handle network connection errors
- should log appropriate error messages on failure
- should log warning when no market data is available


## Test Scripts

### Run All Unit Tests
```bash
npm test
```

### Run by Test File
```bash
npx jest test/unit/ipFilterMiddleware.test.ts
npx jest test/unit/webhookFilterMiddleware.test.ts
npx jest test/unit/processAlert.test.ts
npx jest test/unit/drift/getClient.test.ts
npx jest test/unit/drift/placeOrder.test.ts
npx jest test/unit/drift/cancelOrders.test.ts
npx jest test/unit/drift/getPerpPrice.test.ts
```

### Run Individual Test
```bash
npx jest -t "test name"
```

