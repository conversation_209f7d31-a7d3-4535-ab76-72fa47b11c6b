/// <reference types="vitest" />
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    // Ensure tests don't load .env files - all env vars should be mocked in setup
    env: {
      // Override any potential .env loading with test values
      VITE_RPC_URL: 'https://mock-rpc-url.com',
      VITE_BASE_PYRON_URL: 'https://mock-api.test.com',
      VITE_WEBHOOK_URL: 'https://mock-webhook.test.com',
      VITE_ADMIN_KEY: 'mock-admin-key-for-testing',
      VITE_ENVIRONMENT: 'test',
      NODE_ENV: 'test',
    },
    // Isolate tests from external dependencies
    pool: 'forks',
    poolOptions: {
      forks: {
        singleFork: true,
      },
    },
    // Ensure clean environment for each test
    clearMocks: true,
    restoreMocks: true,
    // Timeout for tests that might hang on real network calls
    testTimeout: 10000,
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  // Ensure no real network calls during testing
  define: {
    'process.env.NODE_ENV': '"test"',
    'import.meta.env.VITE_RPC_URL': '"https://mock-rpc-url.com"',
    'import.meta.env.VITE_BASE_PYRON_URL': '"https://mock-api.test.com"',
    'import.meta.env.VITE_WEBHOOK_URL': '"https://mock-webhook.test.com"',
    'import.meta.env.VITE_ADMIN_KEY': '"mock-admin-key-for-testing"',
    'import.meta.env.VITE_ENVIRONMENT': '"test"',
  },
})
