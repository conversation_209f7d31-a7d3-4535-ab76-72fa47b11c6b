# Use official Node.js image as the base
FROM node:20-alpine

# Install build dependencies for native modules
RUN apk add --no-cache python3 make g++ linux-headers udev eudev-dev

# Set working directory
WORKDIR /app

# Copy package files first for better caching
COPY package*.json ./

# Install dependencies
RUN npm install --frozen-lockfile || npm install

# Copy the rest of the application code
COPY . .

# Build the app
RUN npm run build

# Install serve to serve the build directory
RUN npm install -g serve

# Expose port 3000 (default for serve)
EXPOSE 8080

# Start the app using serve
CMD ["serve", "-s", "dist", "-l", "8080"]
