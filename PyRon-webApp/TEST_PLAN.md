# PyRon-webApp Test Plan

## Overview
This test plan covers existing features for the PyRon-webApp, a Solana-based trading application with AI chat interface, wallet integration, and automated trading capabilities.

## Testing Framework
- **Framework**: Vitest with React Testing Library
- **Environment**: jsdom
- **Test Commands**:
  - `npm test` - Run tests in watch mode
  - `npm run test:run` - Run tests once
  - `npm run test:ui` - Run tests with UI
  - `npm run test:security` - Run security tests only
  - `npm test -- --run src/test/integration/` - Run integration tests only

## Test Coverage

### 1. Component Tests
#### 1.1 Trading Components
**Location**: `src/test/components/trading/`
- ✅ **DepositDialog.test.tsx** - Deposit dialog UI and functionality
- ✅ **ManageFundsDialog.test.tsx** - Withdraw dialog and fund management

#### 1.2 Wallet Components
**Location**: `src/test/components/wallet/`
- ✅ **PhantomPrompt.test.tsx** - Phantom wallet connection prompt
- ✅ **WalletConnect.test.tsx** - Wallet connection component
- ✅ **WalletConnect.simple.test.tsx** - Simplified wallet connection tests

#### 1.3 Auto-Trading Components
**Location**: `src/test/components/`
- ✅ **AutoTradeDialog.test.tsx** - Auto-trading configuration dialog

#### 1.4 Chart Components
**Location**: `src/components/__tests__/`
- ✅ **StockChart.test.tsx** - Main chart component with tab system
- ✅ **TradingViewWidget.test.tsx** - TradingView widget integration
- ✅ **ChartsTabTradingView.test.tsx** - Charts tab with TradingView

#### 1.5 Interface Components
**Location**: `src/components/__tests__/`
- ✅ **ChatInterface.test.tsx** - Chat interface functionality
- ✅ **Layout.test.tsx** - Main application layout
- ✅ **ScrollBehavior.test.tsx** - Scroll behavior testing

### 2. Business Logic Tests

#### 2.1 Trading Logic
**Location**: `src/test/lib/`
- ✅ **drift.test.ts** - Drift SDK integration, deposit/withdraw functions, position management

#### 2.2 Wallet Utilities
**Location**: `src/test/utils/`
- ✅ **phantomWallet.simple.test.ts** - Phantom wallet utility functions

### 3. State Management Tests

#### 3.1 Store Tests
**Location**: `src/test/store/`
- ✅ **walletStore.test.ts** - Wallet state management with Zustand

### 4. Security Tests

#### 4.1 Wallet Security
**Location**: `src/test/security/`
- ✅ **walletSecurity.test.ts** - Private key handling, transaction signing, API key storage security (16 tests)

### 5. Service Tests

#### 5.1 AI Integration
**Location**: `src/test/services/`
- ✅ **openaiWebSearch.test.ts** - OpenAI web search functionality

### 6. Test Utilities

#### 6.1 Mock Utilities
**Location**: `src/test/utils/`
- ✅ **mockWallet.ts** - Wallet mocking utilities
- ✅ **securityTestUtils.ts** - Security testing utilities

#### 6.2 Test Setup
**Location**: `src/test/`
- ✅ **setup.ts** - Global test configuration and mocks

### 7. Integration Tests
**Location**: `src/test/integration/`
- ✅ **TradingFlow.test.tsx** - End-to-end trading flow from wallet connection to auto-trading
- 🔄 **ChatTrading.test.tsx** - Interaction between chat interface and trading components
- 🔄 **WalletOperations.test.tsx** - Wallet operations across multiple components

### Main Test Directory: `src/test/`
```
src/test/
├── setup.ts                           # Global test setup and mocks
├── components/                        # Component tests
├── integration/                       # Integration tests
│   ├── TradingFlow.test.tsx           ✅
│   ├── ChatTrading.test.tsx           🔄
│   └── WalletOperations.test.tsx      🔄
├── lib/                               # Business logic tests
├── security/                          # Security tests
├── services/                          # Service tests
├── store/                             # Store tests
└── utils/                             # Test utilities
```

### Alternative Test Location: `src/components/__tests__/`
```
src/components/__tests__/
├── ChartsTabTradingView.test.tsx       ✅
├── ChatInterface.test.tsx              ✅
├── Layout.test.tsx                     ✅
├── ScrollBehavior.test.tsx             ✅
├── StockChart.test.tsx                 ✅
└── TradingViewWidget.test.tsx          ✅
```

## Mock Strategy

### External Dependencies
- **Phantom Wallet**: Mock wallet adapter and connection
- **Solana Web3**: Mock connection and transaction handling
- **Drift SDK**: Mock DriftClient and trading functions
- **OpenAI API**: Mock API responses
- **Environment Variables**: All mocked in vitest.config.ts

### Test Environment
- All tests run in isolated environment with mocked external dependencies
- No real wallet connections or transactions
- No actual API calls to external services

## Running Tests

### All Tests
```bash
npm test                    # Watch mode
npm run test:run           # Single run
npm run test:ui            # UI interface
```

### Specific Test Categories
```bash
npm run test:security      # Security tests only
npm test -- --run src/test/components/  # Component tests
npm test -- --run src/test/lib/         # Business logic tests
```

### Test Coverage
```bash
npm run test:run -- --coverage
```

## Key Features

### Security-First Testing
- All wallet operations tested for security vulnerabilities
- Private key exposure prevention
- Transaction signing validation
- API key storage security

### Comprehensive Mocking
- External dependencies fully mocked
- No real blockchain interactions
- Isolated test environment

### TDD-Ready Structure
- Tests organized by feature area
- Clear separation of concerns
- Easy to extend for new features

## Notes
- Backend API tests removed from frontend (tested on backend)
- All external dependencies properly mocked
- Environment variables configured in vitest.config.ts
- Tests focus on UI components and client-side logic only
