# PyRon Web App

A Solana-based trading application with AI chat interface, wallet integration, and automated trading capabilities.

## Quick Start

```sh
# Clone and setup
git clone <YOUR_GIT_URL>
cd PyRon-webApp
npm install

# Configure environment
cp .env.example .env

# Start development
npm run dev
```

## Scripts

```sh
npm run dev          # Development server
npm test             # Run tests
npm run test:security # Security tests
```

## Technology Stack

**Frontend:** React 18, TypeScript, Vite, Tailwind CSS, shadcn/ui
**Blockchain:** Solana Web3.js, Drift SDK, Phantom Wallet
**State:** Zustand, TanStack Query
**AI:** OpenAI API with web search
**Charts:** TradingView Widget, Lightweight Charts
**Testing:** Vitest, React Testing Library

## Features

🔐 **Wallet Integration** - Phantom wallet with secure Solana blockchain connection
💰 **Trading** - Automated trading, deposit/withdraw via Drift protocol
🤖 **AI Chat** - OpenAI integration with web search and trading analysis
📊 **Charts** - TradingView widgets with backtesting capabilities

## Environment Variables

```env
VITE_RPC_URL=your_solana_rpc_url
VITE_WEBHOOK_URL=your_webhook_url
VITE_BASE_PYRON_URL=your_backend_api_url
VITE_ADMIN_KEY=your_admin_public_key
```

## Project Structure

```
src/
├── components/     # React components (chat, chart, trade, ui)
├── lib/           # Core business logic and Drift SDK
├── store/         # Zustand state management
├── services/      # API integrations
├── test/          # Test files and utilities
└── utils/         # Utility functions
```

## Testing

Comprehensive testing with unit tests, security tests, and mock utilities for external dependencies. Run with `npm test` or `npm run test:security`.


## Contributing

1. Fork and create feature branch
2. Make changes and run tests (`npm test`)
3. Commit and push changes
4. Open Pull Request

