{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "start": "vite preview --port 3000", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:security": "vitest run src/test/security/"}, "dependencies": {"@drift-labs/sdk": "^2.38.0", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@reach/visually-hidden": "^0.18.0", "@solana/wallet-adapter-wallets": "^0.19.32", "@tanstack/react-query": "^5.80.10", "@types/uuid": "^10.0.0", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "crypto-browserify": "^3.12.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "input-otp": "^1.2.4", "lightweight-charts": "^5.0.6", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.30.0", "readable-stream": "^3.6.0", "recharts": "^2.12.7", "rpc-websockets": "7.10.0", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.3", "zod": "^3.23.8", "zustand": "^5.0.3"}, "devDependencies": {"@esbuild-plugins/node-globals-polyfill": "^0.2.3", "@esbuild-plugins/node-modules-polyfill": "^0.2.2", "@eslint/js": "^9.9.0", "@rollup/plugin-inject": "^5.0.5", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bs58": "^4.0.4", "@types/node": "^22.5.5", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/ui": "^3.1.4", "autoprefixer": "^10.4.20", "buffer": "^6.0.3", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "jsdom": "^25.0.1", "lovable-tagger": "^1.1.7", "postcss": "^8.4.47", "process": "^0.11.10", "rollup-plugin-node-polyfills": "^0.2.1", "stream-browserify": "^3.0.0", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1", "vite-plugin-node-polyfills": "^0.23.0", "vitest": "^3.1.4"}}