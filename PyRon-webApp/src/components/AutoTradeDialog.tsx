import React, { useState, useEffect } from 'react';
import { Co<PERSON>, X } from 'lucide-react';
import { Dialog, DialogContent, DialogClose, DialogTitle } from '@/components/ui/dialog';
import { Switch } from '@/components/ui/switch';
import { createNewChat, getUserChats } from '@/lib/chat';
import { saveOrUpdateAgent, getUserAgentByChatId } from '@/lib/agents';
import { useChatContext } from '@/context/ChatContext';
import { useWalletStore } from '@/store/walletStore';
import DepositDialog from './DepositDialog';

interface AutoTradeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}



const AutoTradeDialog = ({
  open,
  onOpenChange
}: AutoTradeDialogProps) => {
  const { currentChatId, setCurrentChatId, agent, setAgent, chats, setChats } = useChatContext();
  const { walletAddress} = useWalletStore.getState();
  const [agentName, setAgentName] = useState('');
  const [assetPair, setAssetPair] = useState('');
  const [botId, setBotId] = useState(generateId());
  const [webhookUrl, setWebhookUrl] = useState(`${import.meta.env.VITE_WEBHOOK_URL}/${botId}`);
  const [isAutoTradeOn, setIsAutoTradeOn] = useState(agent?.tradingStatus === 'on');
  const [isDepositDialogOpen, setIsDepositDialogOpen] = useState(false);
  const [ruleConfigs, setRuleConfigs] = useState([
    { type: 'Long', confirms: 1, openBar: true },
    { type: 'Short', confirms: 1, openBar: true },
    { type: 'Close Long', confirms: 1, openBar: false },
    { type: 'Close Short', confirms: 1, openBar: false },
    { type: 'BuyReset', confirms: 1, openBar: false },
    { type: 'SellReset', confirms: 1, openBar: false },
    { type: 'Override Buy', confirms: 1, openBar: false },
    { type: 'Override Sell', confirms: 1, openBar: false },
  ]);

  const confirmOptions = Array.from({ length: 60 }, (_, i) => i + 1);
  // Builds the full rule sentence once, so we can reuse it both for <option>
// text *and* for the text copied to clipboard.
const makeRuleSentence = (
  rule: (typeof ruleConfigs)[number],
  n: number = rule.confirms
) => {
  const isBuy = rule.type === "Long" || rule.type === "Close Short" || rule.type === "overrideBuy";
  const confirmLabel = isBuy ? "BuyConfirm" : "SellConfirm";
  const openBarLabel = isBuy ? "Buy" : "Sell";
  let thenAction = rule.type.toUpperCase();

  if (rule.type === "BuyReset") {
    thenAction = "RESET BUY CONFIRM SIGNALS";
  } else if (rule.type === "SellReset") {
    thenAction = "RESET SELL CONFIRM SIGNALS";
  } else if (rule.type === "overrideBuy") {
    thenAction = "LONG";
  } else if (rule.type === "overrideSell") {
    thenAction = "SHORT";
  }

  // Special handling for override rules
  if (rule.type === "overrideBuy") {
    return `If ${n} buy confirm & lookback = buy(close bar) then ${thenAction}`;
  } else if (rule.type === "overrideSell") {
    return `If ${n} sell confirm & lookback = sell(close bar) then ${thenAction}`;
  }

  return rule.openBar
    ? `IF ${n} ${confirmLabel} & 1 ${openBarLabel} (Open Bar) THEN ${thenAction}`
    : `IF ${n} ${confirmLabel} THEN ${thenAction}`;
};


  useEffect(() => {
    setWebhookUrl(`${import.meta.env.VITE_WEBHOOK_URL}/${botId}`);
  }, [botId]);

  useEffect(() => {
    if (agent) {
      setIsAutoTradeOn(agent.tradingStatus === 'on');
      setAgentName(agent.agentName || '');
      setAssetPair(agent.assetPair || '');
      setBotId(agent.botId || generateId());
      // 👇  map DB → UI
    setRuleConfigs([
      { type: 'Long',        confirms: agent.requiredBuyConfirmationsOpen  ?? 1, openBar: true  },
      { type: 'Short',       confirms: agent.requiredSellConfirmationsOpen ?? 1, openBar: true  },
      { type: 'Close Long',  confirms: agent.requiredSellConfirmationsClose?? 1, openBar: false },
      { type: 'Close Short', confirms: agent.requiredBuyConfirmationsClose ?? 1, openBar: false },
      { type: 'BuyReset',    confirms: agent.requiredSellConfirmationsResetCounter ?? 1, openBar: false },
      { type: 'SellReset',   confirms: agent.requiredBuyConfirmationsResetCounter ?? 1, openBar: false },
      { type: 'overrideBuy', confirms: agent.requiredBuyConfirmationsOverride ?? 1, openBar: false },
      { type: 'overrideSell', confirms: agent.requiredSellConfirmationsOverride ?? 1, openBar: false },
    ]);
  } else {
    // Reset to defaults
    setAgentName('');
    setAssetPair('');
    setBotId(generateId());
    setRuleConfigs([
      { type: 'Long',        confirms: 1, openBar: true  },
      { type: 'Short',       confirms: 1, openBar: true  },
      { type: 'Close Long',  confirms: 1, openBar: false },
      { type: 'Close Short', confirms: 1, openBar: false },
      { type: 'BuyReset',    confirms: 1, openBar: false },
      { type: 'SellReset',   confirms: 1, openBar: false },
      { type: 'overrideBuy', confirms: 1, openBar: false },
      { type: 'overrideSell', confirms: 1, openBar: false },
    ]);
      setIsAutoTradeOn(false);
    }
  }, [agent]);

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text);
    console.log('Copied to clipboard:', text);
  };

  const handleSubmit = async () => {
    console.log("submit");

    if (!assetPair || assetPair === "" || agentName === "" || !agentName) {
      alert("Please select an asset pair and enter a name for your agent");
      return;
    }

    try {
      const requiredBuyConfirmationsOpen = ruleConfigs.find(r => r.openBar && r.type === 'Long')?.confirms ?? 1;
      const requiredSellConfirmationsOpen = ruleConfigs.find(r => r.openBar && r.type === 'Short')?.confirms ?? 1;
      const requiredSellConfirmationsClose = ruleConfigs.find(r => !r.openBar && r.type === 'Close Long')?.confirms ?? 1;
      const requiredBuyConfirmationsClose = ruleConfigs.find(r => !r.openBar && r.type === 'Close Short')?.confirms ?? 1;
      const requiredSellConfirmationsResetCounter = ruleConfigs.find(r => r.type === 'BuyReset')?.confirms ?? 1;
      const requiredBuyConfirmationsResetCounter = ruleConfigs.find(r => r.type === 'SellReset')?.confirms ?? 1;
      const requiredBuyConfirmationsOverride = ruleConfigs.find(r => r.type === 'overrideBuy')?.confirms ?? 1;
      const requiredSellConfirmationsOverride = ruleConfigs.find(r => r.type === 'overrideSell')?.confirms ?? 1;

      let chatId = currentChatId;
      console.log("chatId", chatId);

      // If no chat ID is set or it's an empty string, create a new chat
      if (!chatId || chatId === "") {
        chatId = await createNewChat(agentName);
        setCurrentChatId(chatId);
        // Fetch updated chats and set the new chat ID
        const updatedChats = await getUserChats(walletAddress);
        setChats(updatedChats);
      }
      console.log("chatId after createNewChat", chatId);
      // Ensure chatId is not empty before proceeding
      if (!chatId) {
        console.error("Failed to create or retrieve a chat ID");
        return;
      }

      // Fetch existing agent details
      const existingAgent = await getUserAgentByChatId(chatId);

      // Create a new agent object that preserves user inputs
      let agent = {
        // Start with existing agent properties if available
        ...(existingAgent || {}),
        // Override with current user inputs
        botId,
        agentName,
        assetPair,
        requiredBuyConfirmationsOpen,
        requiredSellConfirmationsOpen,
        requiredBuyConfirmationsClose,
        requiredSellConfirmationsClose,
        requiredBuyConfirmationsResetCounter,
        requiredSellConfirmationsResetCounter,
        requiredBuyConfirmationsOverride,
        requiredSellConfirmationsOverride,
        chatId,
        pubkey: walletAddress,
      };

      if (agent.number) {
      // Determine the new trading status
      let newTradingStatus = existingAgent?.tradingStatus === 'on' ? 'off' : 'on';
      agent.tradingStatus = newTradingStatus;
      setIsAutoTradeOn(newTradingStatus === 'on');
      }else{
        agent.tradingStatus = 'off';
        setIsAutoTradeOn(false);
      }
      // Save or update the agent
      let updatedAgent = await saveOrUpdateAgent(agent, botId);
      console.log("Agent saved or updated successfully", updatedAgent);
      if (updatedAgent) {
        setAgent(updatedAgent);
      }
      // Check if agent.number is empty and open DepositDialog if true
      if (!agent.number) {
        setIsDepositDialogOpen(true);
        return;
      }

    } catch (error) {
      console.error("Error in handleSubmit:", error);
    }
  };

  const generateSignalMessage = (signal: string, isConfirmation: boolean) => {
    let action = isConfirmation ? "confirmationMinute" : "buy";
    let signal_name = "buy";

    if (signal === "SELL Open Bar" || signal === "Short") {
      action = isConfirmation ? "confirmationMinute" : "sell";
      signal_name = "sell";
    } else if (signal === "Close Position") {
      action = isConfirmation ? "confirmationMinute" : "close";
      signal_name = "close";
    } else if (signal === "BUY Confirmation") {
      action = "confirmationMinute";
      signal_name = "buy";
    } else if (signal === "SELL Confirmation") {
      action = "confirmationMinute";
      signal_name = "sell";
    } else if (signal === "BuyReset") {
      action = isConfirmation ? "confirmationMinute" : "reset";
      signal_name = "sell";
    } else if (signal === "SellReset") {
      action = isConfirmation ? "confirmationMinute" : "reset";
      signal_name = "buy";
    } else if (signal === "Close Long") {
      action = "confirmationMinute";
      signal_name = "sell";
    } else if (signal === "overrideBuy") {
      action = isConfirmation ? "confirmationMinute" : "overrideBuy";
      signal_name = "buy";
    } else if (signal === "overrideSell") {
      action = isConfirmation ? "confirmationMinute" : "overrideSell";
      signal_name = "sell";
    }

    return JSON.stringify({
      bot_id: botId,
      ticker: assetPair,
      action: action,
      order_size: "100%",
      address: walletAddress,
      timestamp: "{{time}}",
      signalName: signal_name,
    }, null, 2);
  };

  const copyToClipboard = (signal: string) => {
    navigator.clipboard.writeText(signal)
      .then(() => {
        console.log("Copied to clipboard");
      })
      .catch((err) => {
        console.error("Copy failed", err);
      });
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent hideCloseButton={true} className="max-w-[670px] w-[90vw] bg-[#1a1a1a] text-white border border-gray-800/30 p-0 py-0">
          <DialogTitle className="sr-only">Auto Trade Configuration</DialogTitle>
          {/* Custom close button positioned properly */}
          <div className="absolute right-5 top-5 z-10">
            <DialogClose className="rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none">
              <X className="h-7 w-7 text-white py-0" />
              <span className="sr-only">Close</span>
            </DialogClose>
          </div>

          <div className="space-y-2 p-5 py-[40px] px-[25px]">
            <input type="text" value={agentName} onChange={e => setAgentName(e.target.value)} placeholder="Your Agent's Name" className="w-full px-5 py-2 bg-[#222222] border border-[#333333] rounded-md text-white text-lg" />

            <div className="relative">
              <select
                value={assetPair}
                onChange={e => setAssetPair(e.target.value)}
                className="w-full appearance-none px-5 py-2 bg-[#222222] border border-[#333333] rounded-md text-white text-lg"
              >
                <option value="">-- Select the asset to auto-trade--</option>
                <option value="SOL">SOL/USD</option>
                <option value="BTC">BTC/USD</option>
                <option value="ETH">ETH/USD</option>
                <option value="SUI">SUI/USD</option>
                <option value="JTO">JTO/USD</option>
                <option value="RAY">RAY/USD</option>
                <option value="KMNO">KMNO/USD</option>
                <option value="RENDER">RENDER/USD</option>
                <option value="JUP">JUP/USD</option>
                <option value="DOGE">DOGE/USD</option>
                <option value="TAO">TAO/USD</option>
                <option value="AI16Z">AI16Z/USD</option>
                <option value="GOAT">GOAT/USD</option>
                <option value="PAXG">PAXG/USD</option>
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                <svg className="h-6 w-6 text-gray-400" viewBox="0 0 20 20" fill="none" stroke="currentColor">
                  <path d="M7 7l3 3 3-3" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
              </div>
            </div>

            <div className="text-center py-1">
              <h3 className="text-base font-medium">Copy/Paste the following into the TradingView alert you wish to auto-trade with - <span className="text-[#FFD433] cursor-pointer">Click Here</span> to learn more.</h3>
            </div>

            <div className="relative">
              <input type="text" value={webhookUrl} onChange={e => setWebhookUrl(e.target.value)} className="w-full px-5 py-2 bg-[#222222] border border-[#333333] rounded-md text-white text-lg" readOnly />
              <button onClick={() => handleCopy(webhookUrl)} className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white" aria-label="Copy webhook URL">
                <Copy size={20} />
              </button>
            </div>

            <div className="grid grid-cols-1 gap-1 mt-1">
              {/* Dynamic Rules */}
              <div className="grid grid-cols-[0.6fr,2.8fr] text-sm font-semibold text-[#ddd] rounded-md overflow-hidden border-b-0 border-r"
              style={{marginBottom: '5px'}}>
  {/* 1️⃣ OrderType – add explicit border-r */}
  <div className="bg-[#222] p-2 border border-[#333] border-b-0 border-r">
    OrderType
  </div>

  {/* 2️⃣ Ruleset – remove border-l so it lines up with the above border-r */}
  <div className="bg-[#222] p-2 border-y border-[#333] border-b-0">
    Ruleset
  </div>

</div>

{ruleConfigs.map((rule, index) => (
  <div
    key={index}
    className="grid grid-cols-[0.6fr,2.5fr,auto,auto] border border-[#333] rounded-md overflow-hidden"
    data-testid={`rule-row-${rule.type}-${index}`}
    role="row"
  >
    <div className="bg-[#222] p-2 text-white text-sm border-r border-[#333]" data-testid={`rule-type-${rule.type}`}>
      {rule.type === 'overrideBuy' ? 'Override Buy' : rule.type === 'overrideSell' ? 'Override Sell' : rule.type}
    </div>

    <select
      value={rule.confirms}
      onChange={(e) => {
        const newRules = [...ruleConfigs];
        newRules[index].confirms = parseInt(e.target.value);
        setRuleConfigs(newRules);
      }}
      className="
        w-full h-full
        appearance-none pl-3 pr-8 py-1
        text-white text-sm cursor-pointer outline-none focus:ring-0
        bg-[#222]
      "
      data-testid={`rule-select-${rule.type}`}
    >
      {confirmOptions.map((num) => (
        <option key={num} value={num} className="bg-[#222]">
          {makeRuleSentence(rule, num)}
        </option>
      ))}
    </select>

    {/* Copy confirmation message */}
    <button
      onClick={() => copyToClipboard(generateSignalMessage(rule.type, true))}
      className="flex items-center justify-center w-11 bg-[#222] border-l border-[#333] text-gray-400 hover:text-white"
      aria-label="Copy confirmation"
      data-testid={`copy-confirmation-${rule.type}`}
    >
      <Copy size={16} />
    </button>

    {/* Copy action message, disabled for "Close Long" and "Close Short" */}
    <button
      onClick={() => copyToClipboard(generateSignalMessage(rule.type, false))}
      className={`flex items-center justify-center w-11 border-l border-[#333] ${
        rule.type === "Close Long" || rule.type === "Close Short" || rule.type === "BuyReset" || rule.type === "SellReset"
          ? "bg-gray-600 text-gray-500 cursor-not-allowed"
          : "bg-[#222] text-gray-400 hover:text-white"
      }`}
      aria-label="Copy action"
      data-testid={`copy-action-${rule.type}`}
      disabled={rule.type === "Close Long" || rule.type === "Close Short" || rule.type === "BuyReset" || rule.type === "SellReset"}
    >
      <Copy size={16} />
    </button>
  </div>
))}



            </div>

            <div className="flex justify-center pt-1.5 pb-1">
              <button
                onClick={handleSubmit}
                className="px-7 py-2 rounded-full bg-transparent border border-[#FFD433] text-white flex items-center gap-3 hover:bg-[#FFD433]/10 transition-colors"
              >
                <span className="text-base">Auto-Trade</span>
                <div className={`w-14 h-6 rounded-full flex items-center justify-center text-base font-bold ${isAutoTradeOn ? 'bg-[#FFD433] text-black' : 'bg-gray-600 text-white'}`}>
                  {isAutoTradeOn ? 'ON' : 'OFF'}
                </div>
              </button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
      <DepositDialog
        open={isDepositDialogOpen}
        onOpenChange={setIsDepositDialogOpen}
        agent={agent || { agentName: 'Default Name' }}
        onDepositSuccess={(updatedAgent) => {
          setAgent(updatedAgent);
          setIsDepositDialogOpen(false);
          setIsAutoTradeOn(true);
        }}
      />
    </>
  );
};

// Helper function to generate a unique ID
function generateId() {
  return (
    Math.random().toString(36).substring(2, 15) +
    Math.random().toString(36).substring(2, 15)
  );
}

export default AutoTradeDialog;
