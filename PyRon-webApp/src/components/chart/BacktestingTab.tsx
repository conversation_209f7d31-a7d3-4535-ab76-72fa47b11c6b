import React, { useEffect, useState } from 'react';
import { getBotLogs } from '@/lib/logs';
import { getAllHypotheses, createHypothesis, getHypothesesByChatId } from '@/lib/hypothesis';
import { searchOpenAI } from '@/utils/openai';
import { Trade, Hypothesis } from './types';
import { format } from 'date-fns';
import { useChatContext } from '@/context/ChatContext';

interface BacktestingTabProps {
  chatId: string;
  isWalletConnected: boolean;
}

const BacktestingTab: React.FC<BacktestingTabProps> = ({ chatId, isWalletConnected }) => {
  const [trades, setTrades] = useState<Trade[]>([]);
  const [hypotheses, setHypotheses] = useState<Hypothesis[]>([]);
  const [loading, setLoading] = useState(false);
  const { agent } = useChatContext();

  useEffect(() => {
    const fetchData = async () => {
      if (!isWalletConnected || !chatId) return;

      // Check if agent exists and has a number
      if (!agent || !agent.number) {
        setLoading(false);
        return;
      }

      setLoading(true);
      try {
        // Fetch trades and hypotheses
        const [tradesData, hypothesesData] = await Promise.all([
          getBotLogs(chatId),
          getHypothesesByChatId(chatId)
        ]);

        setTrades(tradesData);
        setHypotheses(hypothesesData);

        // Process trades that don't have hypotheses
        for (const trade of tradesData) {
          // Skip failed trades
          if (trade.status.toLowerCase() === 'failed') continue;

          const existingHypothesis = hypothesesData.find((h: Hypothesis) => h.logId.toString() === trade._id.toString());
          if (!existingHypothesis && agent?.hypothesisStatus === 'on') {
            try {
              // Get the appropriate prompt based on trade side
              const prompt = trade.side.toLowerCase() === 'buy'
                ? `Analyze this ${trade.side.toLowerCase()} trade for ${trade.market} (Size: ${trade.size}, Total: ${trade.total}, Type: ${trade.type}) and provide a hypothesis  ${agent.buyReportPrompt? `that answers the following request from the user: ${agent.buyReportPrompt}` : "about why this buy decision was made"}`
                : `Analyze this ${trade.side.toLowerCase()} trade for ${trade.market} (Size: ${trade.size}, Total: ${trade.total}, Type: ${trade.type}) and provide a hypothesis ${agent.sellReportPrompt? `that answers the following request from the user: ${agent.sellReportPrompt}` : "about why this sell decision was made"}`;

              // Use the simplified searchOpenAI function directly (API key handled internally)
              const searchResult = await searchOpenAI(prompt);
              const response = searchResult.message;

              // Create new hypothesis
              const newHypothesis = await createHypothesis(trade._id, response);
              setHypotheses(prev => [...prev, newHypothesis]);
            } catch (error) {
              console.error('Error processing trade:', error);
            }
          }
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [chatId, isWalletConnected, agent]);

  if (!isWalletConnected) {
    return (
      <div className="h-[calc(100vh-200px)] w-full bg-transparent flex items-center justify-center">
        <div className="text-gray-400">Please connect your wallet to view backtesting results</div>
      </div>
    );
  }

  if (!agent || !agent.number) {
    return (
      <div className="h-[calc(100vh-200px)] w-full bg-transparent flex items-center justify-center">
        <div className="text-gray-400 text-center">
          <p className="text-lg mb-2">No trading hypotheses available</p>
          <p className="text-sm">Start trading to generate hypotheses about your trading decisions</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="h-[calc(100vh-200px)] w-full bg-transparent flex items-center justify-center">
        <div className="border-2 border-[#FFD837] bg-black/40 rounded-xl px-8 py-6 text-lg text-[#FFD837] font-medium shadow-lg">
          Analyzing your trades... <span className="ml-2">🐋</span>
        </div>
      </div>
    );
  }

  return (
    <div className="h-[calc(100vh-200px)] w-full bg-transparent">
      <div className="h-full overflow-y-scroll p-8">
        {(!hypotheses || hypotheses.length === 0) ? (
          <div className="h-full flex items-center justify-center">
            <div className="text-gray-400 text-center">
              <p className="text-lg mb-2">No trading hypotheses available</p>
              <p className="text-sm">Start trading to generate hypotheses about your trading decisions</p>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {hypotheses.map((hypothesis) => {
              const trade = trades.find(t => t._id === hypothesis.logId);
              if (!trade) return null;

              return (
                <div key={hypothesis._id} className="border-2 border-[#FFD837] bg-black/40 rounded-xl p-6 text-[#FFD837] shadow-lg">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-xl font-semibold mb-2">
                        {trade.market} - {trade.side.toUpperCase()}
                      </h3>
                      <p className="text-sm text-gray-400">
                        {format(new Date(trade.timestamps), 'MMM dd, yyyy HH:mm')}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-medium">Size: {trade.size}</p>
                      <p className="text-sm text-gray-400">Total: {trade.total}</p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <h4 className="text-lg font-medium mb-2">Hypothesis</h4>
                    <p className="text-white/90">{hypothesis.hypothesis}</p>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default BacktestingTab;
