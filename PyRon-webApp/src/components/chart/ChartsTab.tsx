import React from 'react';
import InfoBar from './InfoBar';
import ComparisonTools from './ComparisonTools';
import TradingViewWidget from '../TradingViewWidget';
import TimeHorizon from './TimeHorizon';
import { useChatContext } from '@/context/ChatContext';
import { convertTimeframeToTradingViewInterval, convertToTradingViewSymbol } from '@/utils/tradingViewUtils';

interface ChartsTabProps {
  assetName: string;
  assetSymbol: string;
  availableAssets: string[];
  comparisonAssets: string[];
  activeTimeframe: string;
  onAddComparison: (asset: string) => void;
  onRemoveComparison: (asset: string) => void;
  onTimeframeChange: (value: string) => void;
}

const ChartsTab: React.FC<ChartsTabProps> = ({
  assetName,
  assetSymbol,
  availableAssets,
  comparisonAssets,
  activeTimeframe,
  onAddComparison,
  onRemoveComparison,
  onTimeframeChange
}) => {
  const { agent } = useChatContext();

  // Get the symbol to use (agent's asset pair or fallback to assetSymbol)
  const currentAssetSymbol = agent?.assetPair || assetSymbol;

  // Convert to TradingView format
  const tradingViewSymbol = convertToTradingViewSymbol(currentAssetSymbol);
  const tradingViewInterval = convertTimeframeToTradingViewInterval(activeTimeframe);

  return (
    <div className="flex flex-col h-full relative">
      <InfoBar
        assetName={currentAssetSymbol}
        assetSymbol={currentAssetSymbol}
      />
      <ComparisonTools
        assetSymbol={currentAssetSymbol}
        availableAssets={availableAssets}
        comparisonAssets={comparisonAssets}
        onAddComparison={onAddComparison}
        onRemoveComparison={onRemoveComparison}
      />
      <div className="flex-1 relative flex flex-col">
        {/* TimeHorizon controls at the top */}
        <div className="relative z-10 flex-shrink-0">
          <TimeHorizon
            activeTimeframe={activeTimeframe}
            onTimeframeChange={onTimeframeChange}
          />
        </div>

        {/* TradingView widget takes remaining space */}
        <div className="flex-1 relative mt-2">
          <TradingViewWidget
            symbol={tradingViewSymbol}
            interval={tradingViewInterval}
            theme="dark"
            locale="en"
            autosize={true}
          />
        </div>
      </div>
    </div>
  );
};

export default ChartsTab;
