import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import ChatInterface from '../ChatInterface';

// Mock only the essential components that are actually rendered
vi.mock('../StockChart', () => ({
  default: () => <div data-testid="stock-chart">Stock Chart</div>
}));

vi.mock('../chat/ChatSection', () => ({
  default: () => <div data-testid="chat-section">Chat Section</div>
}));

vi.mock('../chat/ChatInputForm', () => ({
  default: () => <div data-testid="chat-input">Chat Input</div>
}));

// Mock stores with minimal required state
vi.mock('../../store/walletStore', () => ({
  useWalletStore: () => ({
    isConnected: false,
    walletAddress: null
  })
}));

// Mock context with minimal required state
vi.mock('../../context/ChatContext', () => ({
  ChatProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useChatContext: () => ({
    currentChatId: null,
    messages: [],
    loading: false,
    setMessages: vi.fn(),
    setCurrentChatId: vi.fn(),
    chats: [],
    setChats: vi.fn(),
    agent: null,
  }),
}));

// Mock router params
vi.mock('react-router-dom', () => ({
  useParams: () => ({ chatId: undefined })
}));

describe('ChatInterface Component', () => {
  it('should render main layout with resizable panels', () => {
    render(<ChatInterface />);

    // Check that both main components are rendered
    expect(screen.getByTestId('stock-chart')).toBeInTheDocument();
    expect(screen.getByTestId('chat-input')).toBeInTheDocument();
  });

  it('should show wallet connection prompt when not connected', () => {
    render(<ChatInterface />);

    // Should show the wallet connection message
    expect(screen.getByText('Connect your wallet to start chatting')).toBeInTheDocument();
  });
});
