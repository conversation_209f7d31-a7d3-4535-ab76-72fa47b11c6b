import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import StockChart from '../StockChart';

// Mock only essential child components
vi.mock('../chart/TabSelector', () => ({
  default: () => <div data-testid="tab-selector">Tab Selector</div>
}));

vi.mock('../chart/ChartsTab', () => ({
  default: () => <div data-testid="charts-tab">Charts Tab</div>
}));

vi.mock('../chart/TradeLog', () => ({
  default: () => <div data-testid="trade-log">Trade Log</div>
}));

vi.mock('../chart/BacktestingTab', () => ({
  default: () => <div data-testid="backtesting-tab">Backtesting Tab</div>
}));

vi.mock('../../store/walletStore', () => ({
  useWalletStore: () => ({
    isConnected: true
  })
}));

describe('StockChart Component', () => {
  it('should render tab selector and default charts tab', () => {
    render(<StockChart />);

    expect(screen.getByTestId('tab-selector')).toBeInTheDocument();
    expect(screen.getByTestId('charts-tab')).toBeInTheDocument();
  });
});
