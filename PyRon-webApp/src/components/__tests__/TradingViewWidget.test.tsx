import { render } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import TradingViewWidget from '../TradingViewWidget';

// Mock TradingView's external script loading
Object.defineProperty(window, 'TradingView', {
  value: {
    widget: vi.fn().mockImplementation(() => ({
      onChartReady: vi.fn(),
      remove: vi.fn()
    }))
  },
  writable: true
});

describe('TradingViewWidget', () => {
  it('should render without crashing', () => {
    expect(() => {
      render(<TradingViewWidget />);
    }).not.toThrow();
  });

  it('should accept props without crashing', () => {
    const props = {
      symbol: "BINANCE:ETHUSDT",
      interval: "1H",
      theme: "dark" as const
    };

    expect(() => {
      render(<TradingViewWidget {...props} />);
    }).not.toThrow();
  });
});
