import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import ChartsTab from '../chart/ChartsTab';

// Mock TradingViewWidget
vi.mock('../TradingViewWidget', () => ({
  default: ({ symbol, interval }: { symbol?: string; interval?: string }) => (
    <div data-testid="tradingview-widget" data-symbol={symbol} data-interval={interval}>
      TradingView Widget
    </div>
  )
}));

// Mock essential components
vi.mock('../chart/InfoBar', () => ({
  default: () => <div data-testid="info-bar">Info Bar</div>
}));

vi.mock('../chart/ComparisonTools', () => ({
  default: () => <div data-testid="comparison-tools">Comparison Tools</div>
}));

vi.mock('../chart/TimeHorizon', () => ({
  default: () => <div data-testid="time-horizon">Time Horizon</div>
}));

// Mock utilities
vi.mock('../../utils/tradingViewUtils', () => ({
  convertTimeframeToTradingViewInterval: (timeframe: string) => 'D',
  convertToTradingViewSymbol: (symbol: string) => `BINANCE:${symbol}USDT`
}));

// Mock context
vi.mock('../../context/ChatContext', () => ({
  useChatContext: () => ({
    agent: { assetPair: 'BTC' }
  })
}));

describe('ChartsTab with TradingView Integration', () => {
  const defaultProps = {
    assetName: 'Bitcoin',
    assetSymbol: 'BTC',
    availableAssets: ['BTC', 'ETH', 'SOL'],
    comparisonAssets: [],
    activeTimeframe: '1D',
    onAddComparison: vi.fn(),
    onRemoveComparison: vi.fn(),
    onTimeframeChange: vi.fn()
  };

  it('should render TradingView widget and essential components', () => {
    render(<ChartsTab {...defaultProps} />);

    expect(screen.getByTestId('tradingview-widget')).toBeInTheDocument();
    expect(screen.getByTestId('info-bar')).toBeInTheDocument();
    expect(screen.getByTestId('comparison-tools')).toBeInTheDocument();
    expect(screen.getByTestId('time-horizon')).toBeInTheDocument();
  });

  it('should pass symbol from agent context to TradingView widget', () => {
    render(<ChartsTab {...defaultProps} />);

    const tradingViewWidget = screen.getByTestId('tradingview-widget');
    expect(tradingViewWidget).toHaveAttribute('data-symbol', 'BINANCE:BTCUSDT');
  });
});
