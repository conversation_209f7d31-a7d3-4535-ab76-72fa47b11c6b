import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import Index from '../../pages/Index';

// Mock essential dependencies
vi.mock('../../store/walletStore', () => ({
  useWalletStore: () => ({
    isConnected: false,
    walletAddress: null,
  }),
}));

vi.mock('../../context/ChatContext', () => ({
  ChatProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useChatContext: () => ({
    currentChatId: null,
    messages: [],
    loading: false,
    setMessages: vi.fn(),
    setCurrentChatId: vi.fn(),
    chats: [],
    setChats: vi.fn(),
    agent: null,
  }),
}));

// Mock main components
vi.mock('../../components/Header', () => ({
  default: () => <div data-testid="header">Header</div>
}));

vi.mock('../../components/ChatInterface', () => ({
  default: () => <div data-testid="chat-interface">Chat Interface</div>
}));

vi.mock('../../components/SidebarMenu', () => ({
  default: ({ isOpen }: { isOpen: boolean }) =>
    <div data-testid="sidebar" style={{ display: isOpen ? 'block' : 'none' }}>Sidebar</div>
}));

vi.mock('../../components/SidebarBackdrop', () => ({
  default: ({ isOpen }: { isOpen: boolean }) =>
    isOpen ? <div data-testid="sidebar-backdrop">Backdrop</div> : null
}));

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Layout Integration', () => {
  it('should render main layout components', () => {
    render(
      <TestWrapper>
        <Index />
      </TestWrapper>
    );

    expect(screen.getByTestId('header')).toBeInTheDocument();
    expect(screen.getByTestId('chat-interface')).toBeInTheDocument();
    expect(screen.getByTestId('sidebar')).toBeInTheDocument();
  });
});
