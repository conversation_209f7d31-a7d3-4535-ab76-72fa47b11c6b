import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';

// Integration tests for scroll behavior
describe('Scroll Behavior Integration Tests', () => {
  // Mock window dimensions
  beforeEach(() => {
    Object.defineProperty(window, 'innerHeight', {
      writable: true,
      configurable: true,
      value: 800,
    });
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1200,
    });
  });

  it('should prevent body scroll when layout is properly constrained', () => {
    // Create a test component that simulates our layout
    const TestLayout = () => (
      <div className="flex flex-col h-screen w-full overflow-hidden" data-testid="main-container">
        <div className="flex-shrink-0 h-16 bg-gray-800" data-testid="header">
          Header Content
        </div>
        <div className="flex-1 flex flex-col min-h-0" data-testid="main-content">
          <div className="flex-1 overflow-y-auto" data-testid="scrollable-content">
            {/* Simulate long content */}
            {Array.from({ length: 100 }, (_, i) => (
              <div key={i} className="h-20 border-b">
                Content item {i}
              </div>
            ))}
          </div>
          <div className="flex-shrink-0 h-20 bg-gray-700" data-testid="footer">
            Footer Content
          </div>
        </div>
      </div>
    );

    render(<TestLayout />);

    const mainContainer = screen.getByTestId('main-container');
    const header = screen.getByTestId('header');
    const footer = screen.getByTestId('footer');
    const scrollableContent = screen.getByTestId('scrollable-content');

    // Verify layout structure
    expect(mainContainer).toHaveClass('overflow-hidden');

    // Find the actual flex-shrink-0 containers
    const headerContainer = document.querySelector('.flex-shrink-0');
    const footerContainer = document.querySelectorAll('.flex-shrink-0')[1];

    expect(headerContainer).toBeInTheDocument();
    expect(footerContainer).toBeInTheDocument();
    expect(scrollableContent).toHaveClass('overflow-y-auto');
  });

  it('should maintain header and footer visibility during content scroll', async () => {
    // Remove userEvent.setup() to avoid clipboard mock conflict

    const TestScrollLayout = () => (
      <div className="flex flex-col h-screen overflow-hidden">
        <header className="flex-shrink-0 h-16 bg-blue-500" data-testid="fixed-header">
          Always Visible Header
        </header>
        <main className="flex-1 min-h-0">
          <div className="h-full overflow-y-auto" data-testid="scroll-area">
            {Array.from({ length: 50 }, (_, i) => (
              <div key={i} className="h-32 border-b p-4">
                Scrollable content item {i}
              </div>
            ))}
          </div>
        </main>
        <footer className="flex-shrink-0 h-16 bg-green-500" data-testid="fixed-footer">
          Always Visible Footer
        </footer>
      </div>
    );

    render(<TestScrollLayout />);

    const header = screen.getByTestId('fixed-header');
    const footer = screen.getByTestId('fixed-footer');
    const scrollArea = screen.getByTestId('scroll-area');

    // Verify initial visibility
    expect(header).toBeVisible();
    expect(footer).toBeVisible();

    // Simulate scrolling in the content area (without user events to avoid clipboard conflicts)
    fireEvent.scroll(scrollArea, { target: { scrollTop: 500 } });

    // Header and footer should still be visible after scroll
    expect(header).toBeVisible();
    expect(footer).toBeVisible();
  });

  it('should handle window resize without breaking layout', () => {
    const TestResponsiveLayout = () => (
      <div className="flex flex-col h-screen w-full overflow-hidden">
        <div className="flex-shrink-0" data-testid="responsive-header">Header</div>
        <div className="flex-1 min-h-0 flex">
          <div className="flex-1 min-h-0" data-testid="left-panel">Left Panel</div>
          <div className="flex-1 min-h-0 flex flex-col">
            <div className="flex-1 overflow-y-auto" data-testid="chat-area">Chat</div>
            <div className="flex-shrink-0" data-testid="input-area">Input</div>
          </div>
        </div>
      </div>
    );

    const { rerender } = render(<TestResponsiveLayout />);

    // Simulate window resize
    Object.defineProperty(window, 'innerHeight', { value: 600 });
    Object.defineProperty(window, 'innerWidth', { value: 800 });

    rerender(<TestResponsiveLayout />);

    // Layout should still be intact
    const header = screen.getByTestId('responsive-header');
    const leftPanel = screen.getByTestId('left-panel');
    const chatArea = screen.getByTestId('chat-area');
    const inputArea = screen.getByTestId('input-area');

    expect(header).toBeInTheDocument();
    expect(leftPanel).toBeInTheDocument();
    expect(chatArea).toBeInTheDocument();
    expect(inputArea).toBeInTheDocument();
  });
});
