
import React, { useState } from 'react';
import { X } from 'lucide-react';
import { Input } from './ui/input';
import { Button } from './ui/button';
import { useAIModelStore } from '@/store/aiModelStore';

interface ApiKeyModalProps {
  onClose: () => void;
  onApiKeySaved: () => void;
}

const ApiKeyModal: React.FC<ApiKeyModalProps> = ({ onClose, onApiKeySaved }) => {
  const {
    openaiApiKey,
    setOpenAIKey
  } = useAIModelStore();

  const [openaiKeyInput, setOpenaiKeyInput] = useState(openaiApiKey || '');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSaveKey = () => {
    setIsSubmitting(true);

    if (openaiKeyInput.trim()) {
      setOpenAIKey(openaiKeyInput.trim());
    }

    setIsSubmitting(false);
    onApiKeySaved();
  };

  return (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50">
      <div className="bg-[#222222] border border-[#333333] rounded-lg p-5 w-full max-w-md mx-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-white">API Key Required</h3>
          <button onClick={onClose} className="text-gray-400 hover:text-white">
            <X size={20} />
          </button>
        </div>

        <div className="w-full">
          <p className="text-gray-300 mb-4">
            To use OpenAI's ChatGPT, please enter your OpenAI API key.
            Your key will be stored locally on your device and is not sent to our servers.
          </p>

          <div className="mb-4">
            <Input
              type="password"
              placeholder="sk-..."
              value={openaiKeyInput}
              onChange={(e) => setOpenaiKeyInput(e.target.value)}
              className="bg-[#333333] border-[#444444] text-white"
            />
          </div>

          <div className="mt-2 text-xs text-gray-400 text-center">
            <a
              href="https://platform.openai.com/api-keys"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-400 hover:underline"
            >
              Get an API key from OpenAI
            </a>
          </div>
        </div>

        <div className="flex justify-between gap-4 mt-6">
          <Button
            variant="outline"
            onClick={onClose}
            className="flex-1 border-[#444444] text-white hover:bg-[#333333]"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSaveKey}
            disabled={!openaiKeyInput.trim() || isSubmitting}
            className="flex-1 bg-blue-600/30 hover:bg-blue-600/40 border border-blue-500/30"
          >
            {isSubmitting ? 'Saving...' : 'Save Key'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ApiKeyModal;
