import React from 'react';
import { getOpenAIKey, setOpenAIKey } from '@/utils/openai';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Settings } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';

const OpenAISettings: React.FC = () => {
  const [isDialogOpen, setIsDialogOpen] = React.useState(false);
  const [openaiKeyInput, setOpenaiKeyInput] = React.useState(getOpenAIKey() || '');

  const handleSaveKey = () => {
    if (openaiKeyInput.trim()) {
      setOpenAIKey(openaiKeyInput.trim());
    }
    setIsDialogOpen(false);
  };

  return (
    <div className="flex items-center space-x-2 bg-[#222222] p-2 rounded-md">
      <Label className="text-sm text-gray-400">
        ChatGPT
      </Label>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger asChild>
          <Button variant="ghost" size="icon" className="ml-2">
            <Settings size={16} />
          </Button>
        </DialogTrigger>
        <DialogContent className="bg-[#222222] text-white border-[#333333]">
          <DialogHeader>
            <DialogTitle>OpenAI Settings</DialogTitle>
            <DialogDescription className="text-gray-400">
              Configure your OpenAI API key for ChatGPT
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="openai-key" className="text-white">OpenAI API Key</Label>
              <Input
                id="openai-key"
                type="password"
                placeholder="sk-..."
                value={openaiKeyInput}
                onChange={(e) => setOpenaiKeyInput(e.target.value)}
                className="bg-[#333333] border-[#444444] text-white"
              />
            </div>
          </div>

          <div className="flex justify-end">
            <Button
              onClick={handleSaveKey}
              className="bg-[#FFD433] text-black hover:bg-[#FFD433]/90"
            >
              Save
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default OpenAISettings;
