import React, { useEffect, useRef, useState } from 'react';

interface TradingViewWidgetProps {
  symbol?: string;
  interval?: string;
  theme?: 'light' | 'dark';
  locale?: string;
  autosize?: boolean;
}

/**
 * TradingViewWidget component that integrates the TradingView Chart Widget
 * Provides a clean, minimal chart interface for price analysis
 */
const TradingViewWidget: React.FC<TradingViewWidgetProps> = ({
  symbol = "BINANCE:BTCUSDT",
  interval = "D",
  theme = "dark",
  locale = "en",
  autosize = true
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerId] = useState(`tradingview_${Math.random().toString(36).substring(2, 9)}`);

  useEffect(() => {
    // Load the TradingView widget script
    const script = document.createElement('script');
    script.src = 'https://s3.tradingview.com/tv.js';
    script.async = true;
    script.onload = () => {
      if (window.TradingView) {
        new window.TradingView.widget({
          autosize,
          symbol,
          interval,
          timezone: "Etc/UTC",
          theme,
          style: "3", // Area chart style instead of candlesticks
          locale,
          toolbar_bg: "#1a1a1a",
          enable_publishing: false,
          withdateranges: true,
          hide_side_toolbar: true,
          allow_symbol_change: true,
          details: false, // Hide details to remove right sidebar
          hotlist: false,
          calendar: false,
          show_popup_button: false,
          hide_legend: true, // Hide legend for cleaner look
          hide_top_toolbar: false, // Keep top toolbar for time intervals
          container_id: containerId
        });
      }
    };

    // Add the script to the document
    document.head.appendChild(script);

    // Clean up
    return () => {
      script.remove();
    };
  }, [symbol, interval, theme, locale, autosize, containerId]);

  return (
    <div className="h-full w-full">
      <div
        id={containerId}
        ref={containerRef}
        className="h-full w-full"
      />
    </div>
  );
};

// Add TypeScript interface for the TradingView widget
declare global {
  interface Window {
    TradingView: {
      widget: any;
    };
  }
}

export default TradingViewWidget;
