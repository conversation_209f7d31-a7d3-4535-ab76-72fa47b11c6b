import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { DollarSign } from 'lucide-react';
import { depositFunction, withdrawFunction } from '@/lib/trade/drift';
import { getPosition } from '@/lib/position';
import { useWalletStore } from '@/store/walletStore';
import { useChatContext } from '@/context/ChatContext';
import { toast } from '@/hooks/use-toast';

interface ManageFundsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const ManageFundsDialog: React.FC<ManageFundsDialogProps> = ({
  open,
  onOpenChange
}) => {
  const [amount, setAmount] = useState<string>("");
  const [position, setPosition] = useState<any>(null);
  const { wallet, connection, driftClient, isConnected, walletAddress } = useWalletStore();
  const { agent } = useChatContext();
  const subAccountId = agent?.number;

  useEffect(() => {
    if (open && isConnected && walletAddress && agent?.botId) {
      fetchPosition();
    }
  }, [open, isConnected, walletAddress, agent?.botId]);

  const fetchPosition = async () => {
    if (isConnected && walletAddress && agent?.botId) {
      try {
        const positionData = await getPosition(agent?.botId || '');
        setPosition(positionData);
      } catch (error) {
        console.error('Failed to fetch position:', error);
        // Set default position data on error
        setPosition({
          positionSize: '0',
          pnl: '0',
          positionValueUsd: '0',
          portfolioValue: '0',
        });
      }
    }
  };

  const handleDeposit = async () => {
    if (wallet && connection && driftClient) {
      const result = await depositFunction(wallet, parseFloat(amount), connection, driftClient, false, subAccountId);
      if (result.signature) {
        console.log('Deposit successful:', result.signature);
        fetchPosition(); // Refresh position data
      }
    }
    else{
      toast({
        title: "cannot deposit",
        description: "please reconnect your wallet",
        variant: "destructive"
      });
    }
  };

  const handleWithdraw = async () => {
    // Validate withdrawal amount against available balance
    const withdrawAmount = parseFloat(amount);
    const availableBalance = parseFloat(position?.portfolioValue || '0');

    if (withdrawAmount > availableBalance) {
      toast({
        title: "Insufficient funds",
        description: "Withdrawal amount exceeds available balance",
        variant: "destructive"
      });
      return;
    }

    if (wallet && connection && driftClient) {
      const result = await withdrawFunction(wallet, withdrawAmount, connection, driftClient, true, subAccountId);
      if (result.signature) {
        console.log('Withdraw successful:', result.signature);
        fetchPosition(); // Refresh position data
      }
    }
    else{
      toast({
        title: "cannot withdraw",
        description: "please reconnect your wallet",
        variant: "destructive"
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent hideCloseButton={true} className="max-w-[750px] w-[90vw] bg-[#1a1a1a] text-white border border-[#333333] p-0 rounded-2xl overflow-hidden">
        <div className="flex flex-col space-y-3 p-6">
          <div className="flex flex-col gap-6">
            {/* Title area */}
            <div className="bg-[#FFD433] text-black font-medium py-3 px-5 rounded-xl w-full text-center text-lg">
              Manage Funds
            </div>
            <div className="bg-[#222222] border border-[#333333] rounded-xl py-3 px-4 text-center text-lg">
              {agent?.agentName || 'Agent Name'} - {agent?.assetPair || 'Asset Pair'}
            </div>
            
            {/* Input area for amount */}
            <div className="relative">
              <div className="absolute left-5 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
                <DollarSign className="h-5 w-5 text-[#FFD433]" />
                <span className="text-gray-400 text-lg">{'USDC'}</span>
              </div>
              <input
                type="text"
                value={amount}
                onChange={(e) => {
                  const value = e.target.value;
                  // Only allow positive numbers and decimals
                  if (value === '' || (/^\d*\.?\d*$/.test(value) && parseFloat(value) >= 0)) {
                    setAmount(value);
                  }
                }}
                className="w-full bg-[#222222] border border-[#333333] rounded-full py-4 pl-24 pr-5 text-white text-center text-3xl focus-visible:caret-center"
                placeholder="Enter amount"
              />
            </div>
            
            {/* Action buttons */}
            <div className="grid grid-cols-2 gap-4">
              <button onClick={handleDeposit} className="bg-[#222222] hover:bg-[#2a2a2a] text-white py-3 px-4 rounded-full border border-[#333333] text-lg font-medium transition-colors">
                Deposit
              </button>
              <button onClick={handleWithdraw} className="bg-[#222222] hover:bg-[#2a2a2a] text-white py-3 px-4 rounded-full border border-[#333333] text-lg font-medium transition-colors">
                Withdraw
              </button>
            </div>
            
            {/* Holdings info */}
            <div className="bg-[#222222] border border-[#333333] rounded-xl py-3 px-4 text-center text-lg">
              Current Holdings: {position ? position.portfolioValue : 'Loading...'} USD
            </div>
            
            {/* Position info */}
            <div className="bg-[#222222] border border-[#333333] rounded-xl py-3 px-4 text-center text-lg">
              Current Position: {position ? position.positionSize : 'Loading...'} {agent?.assetPair}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ManageFundsDialog;
