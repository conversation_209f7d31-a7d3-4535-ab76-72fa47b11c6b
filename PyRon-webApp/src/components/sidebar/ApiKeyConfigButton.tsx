import React from 'react';
import { Settings } from 'lucide-react';

interface ApiKeyConfigButtonProps {
  onClick: () => void;
}

const ApiKeyConfigButton: React.FC<ApiKeyConfigButtonProps> = ({ onClick }) => {
  return (
    <button
      onClick={onClick}
      className="w-full bg-[#222222] hover:bg-[#333333] border border-gold-light/20 text-white px-4 py-3 rounded-lg transition-colors flex items-center gap-3"
      aria-label="API Key Configuration"
    >
      <Settings 
        size={18} 
        className="text-gold-light" 
        data-testid="settings-icon"
      />
      <span className="text-sm font-medium">API Key</span>
    </button>
  );
};

export default ApiKeyConfigButton;
