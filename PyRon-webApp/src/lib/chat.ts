import { api } from '@/services/apiClient';
import { Chat, ChatMessage } from '@/types/chat';
import { v4 as uuidv4 } from 'uuid';
import { searchOpenAI } from '@/utils/openai';
import { parseMonthYear, formatMonthYear, dayDifference } from '@/utils/dateUtils';
import { useWalletStore } from '@/store/walletStore';
import { useAIModelStore } from '@/store/aiModelStore';


export const createNewChat = async (title: string = 'New Chat'): Promise<string> => {
  try {
    // Get wallet address from store
    const walletAddress = useWalletStore.getState().walletAddress;

    if (!walletAddress) {
      console.error("Cannot create chat: No wallet address available");
      return "";
    }

    // Include walletAddress in the request body
    const newChat = await api.post('/api/chats/add-chat', {
      title,
      walletAddress
    });

    return newChat._id;
  } catch (error) {
    console.error("Error creating new chat:", error);
    return "";
  }
};

export const getUserChats = async (walletAddress: string | null): Promise<Chat[]> => {
  if (!walletAddress) return [];
  try {
    return await api.get(`/api/chats/wallet/${walletAddress}`);
  } catch (error) {
    console.error('Error fetching user chats:', error);
    return [];
  }
};

export const getUserChatsWithAgent = async (walletAddress: string | null): Promise<Chat[]> => {
  if (!walletAddress) return [];
  try {
    return await api.get(`/api/chats/wallet/${walletAddress}/agents`);
  } catch (error) {
    console.error('Error fetching user chats:', error);
    return [];
  }
};

export const deleteChat = async (chatId: string): Promise<void> => {
  try {
    await api.delete(`/api/chats/${chatId}`);
  } catch (error) {
    console.error('Error deleting chat:', error);
  }
};

export const sendMessage = async (chatId: string, content: string, deepHypothesis: boolean = false): Promise<void> => {
  try {
    // Since we're only using ChatGPT now, we don't need to check the model type
    // The API key is handled internally by searchOpenAI function
    const userMessage: ChatMessage = {
      _id: uuidv4(),
      type: 'user',
      content,
      timestamp: new Date(),
    };

    // Send user message to backend
    await api.post(`/api/chats/${chatId}/messages`, userMessage);

    console.log('content', content, 'using simplified OpenAI web search');
    // Use the simplified searchOpenAI function directly
    const searchResult = await searchOpenAI(content);
    const response = searchResult.message;
    console.log('response', response);

    const aiMessage: ChatMessage = {
      _id: uuidv4(),
      type: 'ai',
      content: response,
      timestamp: new Date(),
    };

    // Send AI message to backend
    await api.post(`/api/chats/${chatId}/messages`, aiMessage);
  } catch (error) {
    console.error('Error sending message:', error);
  }
};

export const getChatMessages = async (chatId: string): Promise<ChatMessage[]> => {
  try {
    return await api.get(`/api/chats/${chatId}/messages`);
  } catch (error) {
    console.error('Error fetching chat messages:', error);
    return [];
  }
};
export function groupChatsByCustomLabel(chats) {
  // 1. Sort by created_at descending (newest first).
  //    Adjust if your timestamp is in seconds vs. ms.
  const sorted = [...chats].sort((a, b) => b.created_at - a.created_at);

  // We'll store groups in a Map: label => array of chats
  const groups = new Map();
  const now = new Date();

  for (const chat of sorted) {
    // If your timestamps are in seconds, do: new Date(chat.created_at * 1000)
    const chatDate = new Date(chat.created_at * 1000);

    // Find difference in *whole days* between now and the chat date
    const diffDays = dayDifference(chatDate, now);

    let label;
    if (diffDays === 0) {
      label = "Today";
    } else if (diffDays === 1) {
      label = "Yesterday";
    } else if (diffDays < 7) {
      label = "Last week";
    } else {
      // For older chats, group by "Month Year", e.g. "March 2025"
      label = formatMonthYear(chatDate);
    }

    if (!groups.has(label)) {
      groups.set(label, []);
    }
    groups.get(label).push(chat);
  }

  // 2. Build a final array in the order you want:
  //    [ Today, Yesterday, Last week, then older months in descending order ]
  const finalGroups = [];

  // First handle our “special” labels in a fixed order:
  const specialOrder = ["Today", "Yesterday", "Last week"];
  for (const special of specialOrder) {
    if (groups.has(special)) {
      finalGroups.push({
        day: special,
        chats: groups.get(special),
      });
      groups.delete(special);
    }
  }

  // 3. Sort all remaining labels (which are “Month Year”) in descending order.
  //    For example, “March 2025” should come before “February 2025”.
  const monthEntries = [];
  for (const [label, chatsArr] of groups.entries()) {
    monthEntries.push({ label, chats: chatsArr });
  }

  monthEntries.sort((a, b) => {
    // Convert “March 2025” => { month: 3, year: 2025 } to compare properly
    const dateA = parseMonthYear(a.label);
    const dateB = parseMonthYear(b.label);

    // Sort descending by year, then by month
    if (dateA.year !== dateB.year) {
      return dateB.year - dateA.year;
    }
    return dateB.month - dateA.month;
  });

  for (const entry of monthEntries) {
    finalGroups.push({ day: entry.label, chats: entry.chats });
  }

  return finalGroups;
}
