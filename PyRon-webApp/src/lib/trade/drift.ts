import { Wallet, DriftClient, BN, BulkA<PERSON>unt<PERSON>oader, getUserAccountPublicKey } from '@drift-labs/sdk';
import { ComputeBudgetProgram, Keypair, PublicKey, Transaction, type Connection } from '@solana/web3.js';
const USDC_MARKET_INDEX = 0;
import bs58 from 'bs58';
import { getTokenBalance } from './getTokenBalance';
import { USDC_TOKEN_ADDRESS } from '../../../constant';
import { toast } from '@/hooks/use-toast';
import { useWalletStore } from '@/store/walletStore';
import { api } from '@/services/apiClient';

export async function createClient(connection: Connection,wallet: any) {
  try {
	if(!wallet || !wallet.publicKey) throw new Error('Admin key not found');

	console.log('wallet', wallet);
	const driftClient = new DriftClient({
		connection: connection,
		wallet: wallet,
		env: 'mainnet-beta',
		opts: {
			commitment: 'max',
		},
        includeDelegates: true,
        
	});
  
    await driftClient.subscribe();
    return driftClient;

  } catch (error) {
    console.log('Error during subscribe:', error);
  }

}


export async function depositInit(driftClient: DriftClient, amount: number, subAccountId?: number) {
    try {
        const marketIndex = USDC_MARKET_INDEX; // USDC
        const amountBN = driftClient.convertToSpotPrecision(marketIndex, parseFloat(amount.toString()));
        if (!amountBN) {
            throw new Error("Failed to convert amount to BigNumber");
        }

        const associatedTokenAccount = await driftClient.getAssociatedTokenAccount(marketIndex);
        console.log('associatedTokenAccount', associatedTokenAccount.toString());
        console.log('amountBN', amountBN.toString());
        console.log('driftClient Wallet Public Key:', driftClient?.wallet?.publicKey?.toBase58());

        if(!subAccountId) {
            subAccountId = await getNextSubAccountIdInc0(driftClient);
            console.log('next subAccountId', subAccountId);
        }

        let ixs = await driftClient.createInitializeUserAccountAndDepositCollateralIxs(
            amountBN,
            associatedTokenAccount,
            marketIndex, 
            subAccountId,
        );
        return {ixs:ixs.ixs, subAccountId:subAccountId} ;
    } catch (error) {
        console.error('Error during deposit:', error);
        throw error; // Re-throwing the error for further handling or logging.
    }
}

export async function deposit(driftClient: DriftClient, amount: number, subAccountId?: number) {
  try {
      const marketIndex = USDC_MARKET_INDEX; // USDC
      const amountBN = driftClient.convertToSpotPrecision(marketIndex, parseFloat(amount.toString()));
      if (!amountBN) {
          throw new Error("Failed to convert amount to BigNumber");
      }

      const associatedTokenAccount = await driftClient.getAssociatedTokenAccount(marketIndex);
      console.log('associatedTokenAccount', associatedTokenAccount.toString());
      console.log('amountBN', amountBN.toString());

      if(!subAccountId) {
          console.log('subAccountId', subAccountId);
          return;
      }

      let ixs = await driftClient.getDepositTxnIx(
          amountBN,
          marketIndex, 
          associatedTokenAccount,
          subAccountId,
      );
      return {ixs:ixs, subAccountId:subAccountId} ;
  } catch (error) {
      console.error('Error during deposit:', error);
      throw error; // Re-throwing the error for further handling or logging.
  }
}

export async function withdraw(driftClient: DriftClient, amount: number, subAccountId: number) {
  try {
      const marketIndex = USDC_MARKET_INDEX; // USDC
      const amountBN = driftClient.convertToSpotPrecision(marketIndex, parseFloat(amount.toString()));
      if (!amountBN) {
          throw new Error("Failed to convert amount to BigNumber");
      }

      const associatedTokenAccount = await driftClient.getAssociatedTokenAccount(marketIndex);
      console.log('associatedTokenAccount', associatedTokenAccount.toString());
      console.log('amountBN', amountBN.toString());

      let ixs = await driftClient.getWithdrawalIxs(
          amountBN,
          marketIndex, 
          associatedTokenAccount,
          undefined,
          subAccountId,
      );
      return {ixs:ixs} ;
  } catch (error) {
      console.error('Error during withdraw:', error);
      throw error; // Re-throwing the error for further handling or logging.
  }
}

export function createKeypairFromSecretKey(base58String: string) {
    // Decode the base58 secret key to a Uint8Array
    const secretKeyUint8Array = bs58.decode(base58String);

    // Check if the decoded secret key is 64 bytes
    if (secretKeyUint8Array.length !== 64) {
        throw new Error('Invalid secret key length. The key must be 64 bytes.');
    }

    // Create the keypair
    const keypair = Keypair.fromSecretKey(secretKeyUint8Array);

    return keypair;
}



export async function depositFunction(wallet: any, amount: number, connection: Connection, driftClient: DriftClient, init: boolean, subAccountId?: number) {
    try {
        console.log("depositFunction : wallet", wallet);
        if (!wallet || !wallet.publicKey) return;

        // Ensure wallet has sendTransaction method
        if (typeof wallet.sendTransaction !== 'function') {
            console.error("Wallet does not have sendTransaction method");
            return;
        }

        let adminKey = import.meta.env.VITE_ADMIN_KEY;
        if (!adminKey) return;
        if (!connection) return;
        if (!driftClient) {
            driftClient = await createClient(connection, wallet);
            if (!driftClient) {
                toast({
                    title: "Failed to create drift client",
                    description: "Please try again.",
                    variant: "destructive"
                  });
                return;
            }
            const { setDriftClient } = useWalletStore.getState();
            setDriftClient(driftClient);
        }
        let initialBalance = await getTokenBalance(wallet, connection, USDC_TOKEN_ADDRESS);
        if (amount > initialBalance) {
            toast({
                title: "Insufficient balance",
                description: "Please try again.",
                variant: "destructive"
              });
            return;
        }
        let depositResult;
        if (init) {
            depositResult = await depositInit(driftClient, amount, subAccountId);
        } else {
            depositResult = await deposit(driftClient, amount, subAccountId);
        }
        if (!depositResult) return;
        const actualSubAccountId = depositResult.subAccountId;

        const userAccountPublicKey = await getUserAccountPublicKey(
            driftClient.program.programId,
            wallet.publicKey, // authority
            actualSubAccountId
        );
        console.log("userAccountPublicKey", userAccountPublicKey.toString(), actualSubAccountId);
        console.log("adminKey", adminKey);
        const delegateIx = await driftClient.program.methods
            .updateUserDelegate(actualSubAccountId, new PublicKey(adminKey))
            .accounts({
                user: userAccountPublicKey,
                authority: wallet.publicKey,
            })
            .instruction();

        let depositInstruction = depositResult.ixs;
        let priorityFeeIx = ComputeBudgetProgram.setComputeUnitPrice({
            microLamports: 100_000,
        });

        //transaction
        let transaction = new Transaction().add(
            priorityFeeIx,
            ...depositInstruction,
            delegateIx,
        );
        let blockhash = await connection.getLatestBlockhash();
        transaction.recentBlockhash = blockhash.blockhash;
        transaction.lastValidBlockHeight = blockhash.lastValidBlockHeight;
        transaction.feePayer = wallet.publicKey;
        console.log("initialBalance", initialBalance, typeof initialBalance);
        let signature = await wallet.sendTransaction(transaction, connection, {
            commitment: 'max',
        });
        console.log("deposit signature", signature);
        if (!signature) return { signature: null, subAccountId: null };

        // Wait for balance change
        try {
            await waitForUSDCBalanceChange(wallet, connection, initialBalance);
            driftClient = await createClient(connection, wallet);
            const { setDriftClient } = useWalletStore.getState();
            setDriftClient(driftClient);
            return { signature: signature, subAccountId: depositResult.subAccountId };
        } catch (error) {
            console.error("Balance did not change after deposit:", error);
            toast({
                title: "Deposit failed",
                description: "Please try again.",
                variant: "destructive"
              });
            return { signature: null, subAccountId: null };
        }
    } catch (e) {
        console.log("error", e);
        toast({
            title: "Deposit failed",
            description: "Please try again.",
            variant: "destructive"
          });
    }
    return { signature: null, subAccountId: null };
}

export async function withdrawFunction(wallet: any, amount: number, connection: Connection, driftClient: DriftClient, init: boolean, subAccountId?: number) {
    try {
        if (!wallet || !wallet.publicKey) return;


        if (!connection) return;
        if (!driftClient) return;

        if (!subAccountId) return { signature: null };

        let withdrawResult = await withdraw(driftClient, amount, subAccountId);
        if (!withdrawResult) return { signature: null };

        let withdrawInstruction = withdrawResult.ixs;

        let priorityFeeIx = ComputeBudgetProgram.setComputeUnitPrice({
            microLamports: 100_000,
        });

        //transaction
        let transaction = new Transaction().add(
            priorityFeeIx,
            ...withdrawInstruction,
        );
        let blockhash = await connection.getLatestBlockhash();
        transaction.recentBlockhash = blockhash.blockhash;
        transaction.lastValidBlockHeight = blockhash.lastValidBlockHeight;
        transaction.feePayer = wallet.publicKey;
        const initialBalance = await getTokenBalance(wallet, connection, USDC_TOKEN_ADDRESS);
        let signature = await wallet.sendTransaction(transaction, connection, {
            commitment: 'max',
        });
        console.log("withdraw signature", signature);
        // Wait for balance change
        try {
            await waitForUSDCBalanceChange(wallet, connection, initialBalance);
            return { signature: signature };
        } catch (error) {
            console.error("Balance did not change after withdrawal:", error);
            return { signature: null };
        }
    } catch (e) {
        console.log("error", e);
    }
    return { signature: null };
}

async function waitForUSDCBalanceChange(wallet: Wallet, connection: Connection, initialBalance: number, timeout: number = 30000): Promise<void> {
    const startTime = Date.now();

    return new Promise((resolve, reject) => {
        const checkBalance = async () => {
            const currentBalance = await getUSDCBalance(wallet, connection);
            console.log("currentBalance", currentBalance, typeof currentBalance);
            console.log(currentBalance !== initialBalance);
            if (currentBalance !== initialBalance) {
                resolve();
            } else if (Date.now() - startTime > timeout) {
                reject(new Error("Timeout waiting for balance change"));
            } else {
                setTimeout(checkBalance, 1000);
            }
        };

        setTimeout(checkBalance, 1000);
    });
}

  async function getUSDCBalance(wallet: Wallet, connection: Connection): Promise<number> {
    try {
      const balance = await getTokenBalance(wallet, connection, USDC_TOKEN_ADDRESS);
      return balance;
    } catch (error) {
      console.error("Failed to get USDC balance:", error);
      throw error;
    }
  }

  async function getSubAccounts(driftClient: DriftClient): Promise<number[]> {
      // Fetch all user accounts associated with the current authority
      const userAccounts = await driftClient.getUserAccountsForAuthority(driftClient.wallet.publicKey);
  
      // Extract and return the sub-account IDs from the fetched user accounts
      const subAccountIds = userAccounts.map(account => account.subAccountId);
      console.log('subAccountIds', subAccountIds);
      return subAccountIds;
  }
  export async function getNextSubAccountIdInc0(
    driftClient: DriftClient
  ): Promise<number> {
    try {
      // Modern SDK (≥ v2.60) exposes this shorthand.
      const nextIdBn = await driftClient.getNextSubAccountId();      // may throw on fresh wallets
      const total = (nextIdBn as BN).toNumber?.() ?? Number(nextIdBn);
      return total;             // e.g. total = 0  → []
    } catch (err: any) {
      // If the user has never opened a sub‑account, no stats PDA exists yet.
      const noAccount =
        err.message?.includes("Account does not exist") ||
        err.message?.includes("could not find account")
        || err.message?.includes("Cannot read properties of undefined (reading 'numberOfSubAccountsCreated')");             // Anchor & RPC wording
      if (noAccount) return 0;
      console.log('error nextSubAccountIdInc0', err);
      return null;
    }
  }
  
export async function fetchUserPositions(walletAddress: string): Promise<{ market: string; position: number }[]> {
  try {
    const data = await api.get(`/api/trade/get-user-positions?pubkey=${walletAddress}`);
    // Fix: Add null check for positions field
    return data.positions || [];
  } catch (error) {
    console.error('Failed to fetch positions:', error);
    return [];
  }
}
export async function fetchUserActivities(walletAddress: string): Promise<any> {
  try {
    const data = await api.get(`/api/trade/get-user-activity?pubkey=${walletAddress}`);
    return data;
  } catch (error) {
    console.error('Failed to fetch activities:', error);
    return {};
  }
}  
