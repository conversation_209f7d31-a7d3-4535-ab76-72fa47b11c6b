import { api } from '@/services/apiClient';

export async function getUserActivity(pubkey: string) {
  try {
    // Replace axios with authenticated api client
    const response = await api.get(`/api/trade/get-user-activity?pubkey=${pubkey}`);
    const data = response; // api.get already returns the data
    const allRecords: any[] = [];

    // Rest of function remains unchanged
    for (const agentKey in data) {
      if (!Object.prototype.hasOwnProperty.call(data, agentKey)) continue;

      // Fix: Add null check before destructuring to prevent crash
      const agentData = data[agentKey];
      if (!agentData || typeof agentData !== 'object') continue;

      const { agentName, orders } = agentData;
      console.log("agentName***", agentName);
      console.log("orders", orders);

      if (!orders || !orders.records || !Array.isArray(orders.records)) continue;
      orders.records.forEach((record: any) => {
        allRecords.push({
          date: new Date(record.ts * 1000).toLocaleString(),
          amount: Number(record.quoteAssetAmountFilled).toFixed(2),
          fee: record.takerFee,
          direction: record.takerOrderDirection,
          txId: record.txSig,
          agentName: agentName,
        });
      });
    }
    console.log({allRecords});
    return allRecords;
  } catch (error) {
    console.error("Failed to get user activity", error);
    return [];
  }
}

export async function getUserTotalAssets(pubkey: string) {
  try {
    // Replace axios with authenticated api client
    const response = await api.get(`/api/trade/get-user-assets?pubkey=${pubkey}`);
    // Fix: Handle different scenarios as expected by tests
    if (response.assets === null || response.assets === undefined) {
      return 0;
    }
    return Number(response.assets).toFixed(2);
  } catch (error) {
    console.error("Failed to get user total assets", error);
    return 0;
  }
}

export async function getPosition(agentId: string) {
  try {
    // Replace fetch with authenticated api client
    const data = await api.get(`/api/trade/get-drift-position?agentId=${agentId}`);

    if(!data || !data.response) return { positionSize: "0", pnl: "0", positionValueUsd: "0", portfolioValue: "0" };

    // Keep original logic but fix the precision issue with positionValueUsd
    let positionSize = Number(data.response.position).toFixed(2);
    let pnl = Number(data.response.pnl).toFixed(2);
    let positionValueUsd = Math.abs(Number(data.response.positionValue)); // Fix: Return number, not string
    let portfolioValue = Number(data.response.portfolioValue).toFixed(2);
    console.log(positionSize, pnl, positionValueUsd, portfolioValue);

    return { positionSize, pnl, positionValueUsd, portfolioValue };
  } catch (error) {
    console.log("error", error);
    return { positionSize: "0", pnl: "0", positionValueUsd: "0", portfolioValue: "0" };
  }
}
