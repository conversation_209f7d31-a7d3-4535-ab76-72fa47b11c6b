// No imports needed for this simplified version

// Interface for the chat messages
export interface ChatMessage {
  type: 'user' | 'ai';
  content: string;
}

// Interface for web search results
interface SearchResult {
  link: string;
  title: string;
  snippet: string;
}

interface WebSearchResponse {
  results: SearchResult[];
  message: string;
}

// Check if the OpenAI API key is stored in localStorage
export const getOpenAIKey = (): string | null => {
  return localStorage.getItem('openai_api_key');
};

// Store the OpenAI API key in localStorage
export const setOpenAIKey = (key: string): void => {
  localStorage.setItem('openai_api_key', key);
};

// Perform web search using OpenAI Web Search API with integrated AI response
export const searchOpenAI = async (query: string): Promise<WebSearchResponse> => {
  try {
    // Get OpenAI API key
    const apiKey = getOpenAIKey();
    if (!apiKey) {
      return {
        results: [],
        message: "OpenAI API key not found. Please set your API key."
      };
    }

    // Create a comprehensive prompt that includes the financial assistant role
    const systemPrompt = `You are a helpful financial assistant specializing in cryptocurrency trading.
    Provide accurate, precise and concise advice and analysis on trading strategies, market trends, and technical analysis using websearch tool`;

    const response = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: "gpt-4o-search-preview",
        messages: [
          { role: "system", content: systemPrompt },
          { role: "user", content: query }
        ],
        web_search_options: { search_context_size: "medium" }
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('OpenAI Web Search API error:', errorData);
      return {
        results: [],
        message: "Failed to fetch web search results from OpenAI"
      };
    }

    const data = await response.json();

    // Get the AI's response which includes web search context
    const message = data.choices[0]?.message?.content || "";

    // With the new search-preview model, we don't need to parse separate results
    // The AI response already includes the web search context
    return {
      results: [], // No separate results needed with integrated search
      message: message
    };
  } catch (error) {
    console.error('Error performing OpenAI web search:', error);
    return {
      results: [],
      message: "Failed to fetch web search results"
    };
  }
};


