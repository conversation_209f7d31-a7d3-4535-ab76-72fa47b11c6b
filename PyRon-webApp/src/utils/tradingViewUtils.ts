/**
 * Utility functions for TradingView integration
 */

/**
 * Converts app timeframe format to TradingView interval format
 * @param timeframe - App timeframe (e.g., '1m', '5m', '1h', '1D', '1W', '1M')
 * @returns TradingView interval format
 */
export function convertTimeframeToTradingViewInterval(timeframe: string): string {
  const timeframeMap: Record<string, string> = {
    '1m': '1',
    '5m': '5',
    '15m': '15',
    '30m': '30',
    '1h': '60',
    '2h': '120',
    '4h': '240',
    '6h': '360',
    '8h': '480',
    '12h': '720',
    '1D': 'D',
    '1W': 'W',
    '1M': 'M'
  };

  return timeframeMap[timeframe] || 'D'; // Default to daily if not found
}

/**
 * Converts asset symbol to TradingView symbol format
 * @param assetSymbol - Asset symbol (e.g., 'BTC', 'ETH')
 * @param exchange - Exchange name (default: 'BINANCE')
 * @returns TradingView symbol format (e.g., 'BINANCE:BTCUSDT')
 */
export function convertToTradingViewSymbol(assetSymbol: string, exchange: string = 'BINANCE'): string {
  // Remove any existing USDT suffix and add it back
  const cleanSymbol = assetSymbol.replace(/USDT$/, '');
  return `${exchange}:${cleanSymbol}USDT`;
}
