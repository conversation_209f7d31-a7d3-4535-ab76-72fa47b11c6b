import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { useWalletStore, initializeWallet } from '../../store/walletStore';
import {
  createMockWalletAdapter,
  createMockConnection,
  createMockDriftClient,
  setupWalletMocks,
  mockAuthService
} from '../utils/mockWallet';

// Mock external dependencies
vi.mock('@solana/web3.js', () => ({
  Connection: vi.fn().mockImplementation(() => ({
    getLatestBlockhash: vi.fn().mockResolvedValue({
      blockhash: 'mock-blockhash',
      lastValidBlockHeight: 123456,
    }),
    sendTransaction: vi.fn().mockResolvedValue('mock-signature'),
    confirmTransaction: vi.fn().mockResolvedValue({ value: { err: null } }),
    getAccountInfo: vi.fn().mockResolvedValue(null),
  })),
}));

vi.mock('@solana/wallet-adapter-phantom', () => ({
  PhantomWalletAdapter: vi.fn().mockImplementation(() => ({
    connected: false,
    publicKey: null,
    connect: vi.fn().mockResolvedValue(undefined),
    disconnect: vi.fn().mockResolvedValue(undefined),
    on: vi.fn(),
    off: vi.fn(),
    removeAllListeners: vi.fn(),
    sendTransaction: vi.fn().mockResolvedValue('mock-signature'),
    signTransaction: vi.fn(),
    signAllTransactions: vi.fn(),
  })),
}));

vi.mock('@drift-labs/sdk', () => ({
  DriftClient: vi.fn().mockImplementation(() => ({
    subscribe: vi.fn().mockResolvedValue(undefined),
    unsubscribe: vi.fn().mockResolvedValue(undefined),
    convertToSpotPrecision: vi.fn(),
    getAssociatedTokenAccount: vi.fn(),
    getUserAccount: vi.fn(),
    getUser: vi.fn(),
  })),
}));

vi.mock('@/lib/trade/drift', () => ({
  createClient: vi.fn().mockResolvedValue({
    subscribe: vi.fn().mockResolvedValue(undefined),
    unsubscribe: vi.fn().mockResolvedValue(undefined),
    convertToSpotPrecision: vi.fn(),
    getAssociatedTokenAccount: vi.fn(),
    getUserAccount: vi.fn(),
    getUser: vi.fn(),
  }),
}));

vi.mock('@/services/authService', () => ({
  authenticateWallet: vi.fn().mockResolvedValue(true),
  logout: vi.fn().mockResolvedValue(undefined),
}));

describe('WalletStore', () => {
  beforeEach(async () => {
    setupWalletMocks();
    // Reset the store state before each test
    useWalletStore.setState({
      isConnected: false,
      wallet: null,
      walletType: null,
      connection: null,
      driftClient: null,
      walletAddress: null,
      currentChatId: null,
      agent: null,
      chats: [],
    });

    // Reset all mocks
    vi.clearAllMocks();

    // Reset auth service mocks
    const { authenticateWallet, logout } = await import('@/services/authService');
    vi.mocked(authenticateWallet).mockResolvedValue(true);
    vi.mocked(logout).mockResolvedValue(undefined);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Initial State', () => {
    it('should have correct initial state', () => {
      const state = useWalletStore.getState();

      expect(state.isConnected).toBe(false);
      expect(state.wallet).toBeNull();
      expect(state.walletType).toBeNull();
      expect(state.connection).toBeNull();
      expect(state.driftClient).toBeNull();
      expect(state.walletAddress).toBeNull();
      expect(state.currentChatId).toBeNull();
      expect(state.agent).toBeNull();
      expect(state.chats).toEqual([]);
    });

    it('should have all required action methods', () => {
      const state = useWalletStore.getState();

      expect(typeof state.connectWallet).toBe('function');
      expect(typeof state.disconnectWallet).toBe('function');
      expect(typeof state.setWalletType).toBe('function');
      expect(typeof state.resetChatState).toBe('function');
      expect(typeof state.setDriftClient).toBe('function');
    });
  });

  describe('Wallet Connection', () => {
    it('should connect wallet successfully', async () => {
      const { PhantomWalletAdapter } = await import('@solana/wallet-adapter-phantom');
      const mockAdapter = createMockWalletAdapter(false, 'test-address');
      vi.mocked(PhantomWalletAdapter).mockImplementation(() => mockAdapter);

      const { connectWallet } = useWalletStore.getState();

      // Mock the async operations to resolve quickly
      mockAdapter.connect.mockResolvedValue(undefined);

      await connectWallet('test-address');

      const state = useWalletStore.getState();
      expect(state.isConnected).toBe(true);
      expect(state.walletAddress).toBe('test-address');
      expect(state.walletType).toBe('phantom');
      expect(mockAdapter.connect).toHaveBeenCalled();
    }, 10000); // Increase timeout

    it('should handle wallet connection failure', async () => {
      const { PhantomWalletAdapter } = await import('@solana/wallet-adapter-phantom');
      const mockAdapter = createMockWalletAdapter(false);
      mockAdapter.connect.mockRejectedValue(new Error('Connection failed'));
      vi.mocked(PhantomWalletAdapter).mockImplementation(() => mockAdapter);

      const { connectWallet } = useWalletStore.getState();
      await connectWallet('test-address');

      const state = useWalletStore.getState();
      expect(state.isConnected).toBe(false);
      expect(state.walletAddress).toBeNull();
    });

    it('should create connection with correct RPC URL', async () => {
      const { Connection } = await import('@solana/web3.js');
      const { connectWallet } = useWalletStore.getState();

      await connectWallet('test-address');

      expect(Connection).toHaveBeenCalledWith(
        'https://api.mainnet-beta.solana.com',
        'confirmed'
      );
    });

    it('should set up wallet event listeners', async () => {
      const { PhantomWalletAdapter } = await import('@solana/wallet-adapter-phantom');
      const mockAdapter = createMockWalletAdapter(false, 'test-address');
      vi.mocked(PhantomWalletAdapter).mockImplementation(() => mockAdapter);

      const { connectWallet } = useWalletStore.getState();
      await connectWallet('test-address');

      expect(mockAdapter.on).toHaveBeenCalledWith('connect', expect.any(Function));
      expect(mockAdapter.on).toHaveBeenCalledWith('disconnect', expect.any(Function));
    }, 10000); // Increase timeout

    it('should handle already connected wallet', async () => {
      const { PhantomWalletAdapter } = await import('@solana/wallet-adapter-phantom');
      const mockAdapter = createMockWalletAdapter(true, 'test-address');
      vi.mocked(PhantomWalletAdapter).mockImplementation(() => mockAdapter);

      const { connectWallet } = useWalletStore.getState();
      await connectWallet('test-address');

      // Should not call connect again if already connected
      expect(mockAdapter.connect).not.toHaveBeenCalled();
    }, 10000); // Increase timeout
  });

  describe('Wallet Disconnection', () => {
    it('should disconnect wallet successfully', async () => {
      // First set connected state with a mock wallet
      const mockWallet = createMockWalletAdapter(true, 'test-address');
      useWalletStore.setState({
        isConnected: true,
        walletAddress: 'test-address',
        walletType: 'phantom',
        wallet: mockWallet,
      });

      const { disconnectWallet } = useWalletStore.getState();
      await disconnectWallet();

      const state = useWalletStore.getState();
      expect(state.isConnected).toBe(false);
      expect(state.wallet).toBeNull();
      expect(state.walletType).toBeNull();
      expect(state.connection).toBeNull();
      expect(state.driftClient).toBeNull();
      expect(state.walletAddress).toBeNull();
    });

    it('should call logout service on disconnect', async () => {
      const mockWallet = createMockWalletAdapter(true, 'test-address');
      useWalletStore.setState({
        isConnected: true,
        walletAddress: 'test-address',
        wallet: mockWallet,
      });

      const { logout } = await import('@/services/authService');
      const { disconnectWallet } = useWalletStore.getState();

      // Clear previous calls and reset the mock
      vi.mocked(logout).mockClear();

      await disconnectWallet();

      expect(logout).toHaveBeenCalled();
    });
  });

  describe('Chat State Management', () => {
    it('should reset chat state', () => {
      // Set some chat state
      useWalletStore.setState({
        currentChatId: 'chat-123',
        agent: { id: 'agent-1' },
        chats: [{ id: 'chat-1' }],
      });

      const { resetChatState } = useWalletStore.getState();
      resetChatState();

      const state = useWalletStore.getState();
      expect(state.currentChatId).toBeNull();
      expect(state.agent).toBeNull();
      expect(state.chats).toEqual([]);
    });

    it('should set drift client', () => {
      const mockDriftClient = { subscribe: vi.fn() };
      
      const { setDriftClient } = useWalletStore.getState();
      setDriftClient(mockDriftClient as any);

      const state = useWalletStore.getState();
      expect(state.driftClient).toBe(mockDriftClient);
    });

    it('should set wallet type', () => {
      const { setWalletType } = useWalletStore.getState();
      setWalletType('phantom');

      const state = useWalletStore.getState();
      expect(state.walletType).toBe('phantom');
    });
  });

  describe('Wallet Initialization', () => {
    it('should initialize wallet from persisted state', async () => {
      // Mock the persisted state structure that zustand uses
      const persistedState = JSON.stringify({
        state: {
          isConnected: false,
          walletType: 'phantom',
          walletAddress: 'test-address',
        },
        version: 0,
      });

      vi.mocked(localStorage.getItem).mockReturnValue(persistedState);

      const { PhantomWalletAdapter } = await import('@solana/wallet-adapter-phantom');
      const mockAdapter = createMockWalletAdapter(true, 'test-address');
      vi.mocked(PhantomWalletAdapter).mockImplementation(() => mockAdapter);

      // Set the initial state to simulate persisted state
      useWalletStore.setState({
        isConnected: false,
        walletType: 'phantom',
        walletAddress: 'test-address',
      });

      await initializeWallet();

      const state = useWalletStore.getState();
      expect(state.isConnected).toBe(true);
      expect(state.walletAddress).toBe('test-address');
    });

    it('should handle initialization with no persisted wallet', async () => {
      vi.mocked(localStorage.getItem).mockReturnValue(null);

      await initializeWallet();

      const state = useWalletStore.getState();
      expect(state.isConnected).toBe(false);
      expect(state.walletAddress).toBeNull();
    });

    it('should handle initialization errors gracefully', async () => {
      const persistedState = JSON.stringify({
        state: {
          isConnected: false,
          walletType: 'phantom',
          walletAddress: 'test-address',
        },
        version: 0,
      });

      vi.mocked(localStorage.getItem).mockReturnValue(persistedState);

      const { PhantomWalletAdapter } = await import('@solana/wallet-adapter-phantom');
      const mockAdapter = createMockWalletAdapter(false);
      mockAdapter.connect.mockRejectedValue(new Error('Init failed'));
      vi.mocked(PhantomWalletAdapter).mockImplementation(() => mockAdapter);

      // Set the initial state to simulate persisted state
      useWalletStore.setState({
        isConnected: false,
        walletType: 'phantom',
        walletAddress: 'test-address',
      });

      // Should not throw
      await expect(initializeWallet()).resolves.not.toThrow();

      const state = useWalletStore.getState();
      expect(state.isConnected).toBe(false);
    });
  });

  describe('Persistence', () => {
    it('should persist wallet state to localStorage', async () => {
      const { PhantomWalletAdapter } = await import('@solana/wallet-adapter-phantom');
      const mockAdapter = createMockWalletAdapter(false, 'test-address');
      vi.mocked(PhantomWalletAdapter).mockImplementation(() => mockAdapter);

      const { connectWallet } = useWalletStore.getState();
      await connectWallet('test-address');

      // Since zustand persist middleware handles this automatically,
      // we just verify the state is correct
      const state = useWalletStore.getState();
      expect(state.walletAddress).toBe('test-address');
      expect(state.isConnected).toBe(true);
      expect(state.walletType).toBe('phantom');
    });

    it('should restore wallet state from localStorage', () => {
      // Mock persisted state
      const persistedState = JSON.stringify({
        state: {
          walletAddress: 'test-address',
          isConnected: false, // Persisted state should have isConnected as false
          walletType: 'phantom',
        },
        version: 0,
      });

      vi.mocked(localStorage.getItem).mockReturnValue(persistedState);

      // Manually set the state to simulate restoration
      useWalletStore.setState({
        walletAddress: 'test-address',
        isConnected: false,
        walletType: 'phantom',
      });

      const state = useWalletStore.getState();
      expect(state.walletAddress).toBe('test-address');
      expect(state.walletType).toBe('phantom');
    });
  });

  describe('Error Handling', () => {
    it('should handle missing public key during connection', async () => {
      const { PhantomWalletAdapter } = await import('@solana/wallet-adapter-phantom');
      const mockAdapter = createMockWalletAdapter(false);
      mockAdapter.publicKey = null;
      vi.mocked(PhantomWalletAdapter).mockImplementation(() => mockAdapter);

      const { connectWallet } = useWalletStore.getState();
      await connectWallet('test-address');

      const state = useWalletStore.getState();
      expect(state.isConnected).toBe(false);
    });

    it('should handle drift client creation failure', async () => {
      const { PhantomWalletAdapter } = await import('@solana/wallet-adapter-phantom');
      const mockAdapter = createMockWalletAdapter(false, 'test-address');
      vi.mocked(PhantomWalletAdapter).mockImplementation(() => mockAdapter);

      // Mock drift client creation to fail
      const { createClient } = await import('@/lib/trade/drift');
      vi.mocked(createClient).mockRejectedValue(new Error('Drift client failed'));

      const { connectWallet } = useWalletStore.getState();
      await connectWallet('test-address');

      // Should still connect wallet even if drift client fails
      const state = useWalletStore.getState();
      expect(state.isConnected).toBe(true);
      expect(state.walletAddress).toBe('test-address');
      expect(state.driftClient).toBeNull();
    });
  });
});
