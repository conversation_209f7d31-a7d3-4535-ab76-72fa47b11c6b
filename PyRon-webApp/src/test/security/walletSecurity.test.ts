import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { useWalletStore } from '@/store/walletStore';
import { useAIModelStore } from '@/store/aiModelStore';
import { getOpenAIKey, setOpenAIKey } from '@/utils/openai';
import { createKeypairFromSecretKey } from '@/lib/trade/drift';
import {
  createSecurityLogCapture,
  checkForSensitiveDataInLogs,
  SENSITIVE_PATTERNS,
  createSecureLocalStorageMock,
  validateApiKeySecurity,
  createSecureWalletMock,
  checkWalletSecurityViolations
} from '../utils/securityTestUtils';

// Mock wallet store
vi.mock('@/store/walletStore', () => ({
  useWalletStore: {
    getState: vi.fn(() => ({
      isConnected: false,
      wallet: null,
      walletType: null,
      connection: null,
      driftClient: null,
      walletAddress: null,
      connectWallet: vi.fn(),
      disconnectWallet: vi.fn(),
    })),
  },
}));

// Mock AI model store
vi.mock('@/store/aiModelStore', () => ({
  useAIModelStore: {
    getState: vi.fn(() => ({
      currentModel: 'gpt',
      openaiApiKey: null,
      setCurrentModel: vi.fn(),
      setOpenAIKey: vi.fn(),
      getCurrentApiKey: vi.fn(),
    })),
  },
}));

// Mock auth service
vi.mock('@/services/authService', () => ({
  authenticateWallet: vi.fn(),
  logout: vi.fn(),
}));

// Mock drift client creation
vi.mock('@/lib/trade/drift', () => ({
  createClient: vi.fn(),
  createKeypairFromSecretKey: vi.fn(),
}));

describe('Wallet Security Tests', () => {
  let logCapture: ReturnType<typeof createSecurityLogCapture>;
  let mockLocalStorage: ReturnType<typeof createSecureLocalStorageMock>;

  beforeEach(() => {
    vi.clearAllMocks();
    logCapture = createSecurityLogCapture();
    mockLocalStorage = createSecureLocalStorageMock();
    
    // Mock localStorage globally
    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage,
      writable: true,
    });
  });

  afterEach(() => {
    logCapture.restore();
    mockLocalStorage.clearLog();
  });

  describe('Private Key Handling Security', () => {
    it('should never expose private keys in console logs', async () => {
      const secureWallet = createSecureWalletMock();
      secureWallet.publicKey = { toString: () => 'test-public-key' };
      secureWallet.connected = true;

      // Mock the connectWallet function
      const mockConnectWallet = vi.fn();
      vi.mocked(useWalletStore.getState).mockReturnValue({
        ...useWalletStore.getState(),
        connectWallet: mockConnectWallet,
      });

      await mockConnectWallet('test-address');

      // Check all logs for sensitive data
      const allLogs = [...logCapture.logs, ...logCapture.errors, ...logCapture.warns];
      const violations = checkForSensitiveDataInLogs(allLogs, SENSITIVE_PATTERNS.PRIVATE_KEYS);

      expect(violations).toHaveLength(0);
    });

    it('should not store private keys in localStorage', () => {
      // Simulate wallet connection
      mockLocalStorage.setItem('wallet_address', 'test-address');
      mockLocalStorage.setItem('wallet_type', 'phantom');

      // Check that no private key patterns are stored
      mockLocalStorage.accessLog.forEach(entry => {
        if (entry.action === 'set' && entry.value) {
          const violations = checkForSensitiveDataInLogs([entry.value], SENSITIVE_PATTERNS.PRIVATE_KEYS);
          expect(violations).toHaveLength(0);
        }
      });
    });

    it('should not expose private keys in wallet state', () => {
      const state = useWalletStore.getState();

      // Convert state to string to check for sensitive patterns
      const stateString = JSON.stringify(state);
      const violations = checkForSensitiveDataInLogs([stateString], SENSITIVE_PATTERNS.PRIVATE_KEYS);

      expect(violations).toHaveLength(0);
    });

    it('should handle keypair creation securely', () => {
      // Test the createKeypairFromSecretKey function
      const testSecretKey = 'test-secret-key-base58';

      // Mock the function to simulate error handling
      vi.mocked(createKeypairFromSecretKey).mockImplementation(() => {
        throw new Error('Invalid secret key length. The key must be 64 bytes.');
      });

      try {
        createKeypairFromSecretKey(testSecretKey);
      } catch (error) {
        // Function should handle errors without exposing the secret key
        const errorMessage = error instanceof Error ? error.message : String(error);
        expect(errorMessage).not.toContain(testSecretKey);
      }

      // Check logs don't contain the secret key
      const allLogs = [...logCapture.logs, ...logCapture.errors, ...logCapture.warns];
      allLogs.forEach(log => {
        expect(log).not.toContain(testSecretKey);
      });
    });
  });

  describe('Transaction Signing Security', () => {
    it('should sign transactions without exposing private keys', async () => {
      const secureWallet = createSecureWalletMock();
      secureWallet.publicKey = { toString: () => 'test-public-key' };
      secureWallet.connected = true;

      // Simulate transaction signing
      const mockTransaction = { recentBlockhash: 'test-blockhash' };
      await secureWallet.signTransaction(mockTransaction);

      // Check that signing operation was logged but no sensitive data exposed
      const violations = checkWalletSecurityViolations(secureWallet);
      expect(violations).toHaveLength(0);

      // Verify signing was called
      expect(secureWallet.signTransaction).toHaveBeenCalledWith(mockTransaction);
    });

    it('should handle multiple transaction signing securely', async () => {
      const secureWallet = createSecureWalletMock();
      secureWallet.publicKey = { toString: () => 'test-public-key' };
      secureWallet.connected = true;

      // Simulate multiple transaction signing
      const mockTransactions = [
        { recentBlockhash: 'test-blockhash-1' },
        { recentBlockhash: 'test-blockhash-2' }
      ];
      await secureWallet.signAllTransactions(mockTransactions);

      // Check security log
      const violations = checkWalletSecurityViolations(secureWallet);
      expect(violations).toHaveLength(0);

      // Verify the operation was logged correctly
      const signAllEntry = secureWallet.securityLog.find(entry => entry.action === 'signAllTransactions');
      expect(signAllEntry).toBeDefined();
      expect(signAllEntry?.data).toBe('2_transactions_signed');
    });

    it('should not log transaction details that could contain sensitive data', async () => {
      const secureWallet = createSecureWalletMock();

      // Simulate transaction with potentially sensitive data
      const transactionWithSensitiveData = {
        recentBlockhash: 'test-blockhash',
        instructions: [{ data: 'private-key-data' }]
      };

      await secureWallet.signTransaction(transactionWithSensitiveData);

      // Check that transaction details are not logged
      const allLogs = [...logCapture.logs, ...logCapture.errors, ...logCapture.warns];
      allLogs.forEach(log => {
        expect(log).not.toContain('private-key-data');
        expect(log).not.toContain('instructions');
      });
    });
  });

  describe('API Key Storage Security', () => {
    it('should store API keys securely in localStorage', () => {
      const testApiKey = 'sk-test1234567890abcdef';

      // Test setting API key
      setOpenAIKey(testApiKey);

      // Verify it was stored
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('openai_api_key', testApiKey);

      // Verify retrieval works
      const retrievedKey = getOpenAIKey();
      expect(retrievedKey).toBe(testApiKey);
    });

    it('should validate API key format and security', () => {
      // Test valid API key
      const validKey = 'sk-1234567890abcdef1234567890abcdef';
      const validResult = validateApiKeySecurity(validKey);
      expect(validResult.isSecure).toBe(true);
      expect(validResult.issues).toHaveLength(0);

      // Test invalid API key - too short
      const shortKey = 'sk-123';
      const shortResult = validateApiKeySecurity(shortKey);
      expect(shortResult.isSecure).toBe(false);
      expect(shortResult.issues).toContain('API key is too short');

      // Test invalid API key - contains spaces
      const spacedKey = 'sk-123 456 789';
      const spacedResult = validateApiKeySecurity(spacedKey);
      expect(spacedResult.isSecure).toBe(false);
      expect(spacedResult.issues).toContain('API key contains spaces');

      // Test test/dummy key
      const testKey = 'sk-test1234567890';
      const testResult = validateApiKeySecurity(testKey);
      expect(testResult.isSecure).toBe(false);
      expect(testResult.issues).toContain('API key appears to be a test/dummy key');
    });

    it('should not expose API keys in console logs', () => {
      const testApiKey = 'sk-real1234567890abcdef';

      // Set API key and trigger operations that might log
      setOpenAIKey(testApiKey);
      const { setOpenAIKey: storeSetKey } = useAIModelStore.getState();
      storeSetKey(testApiKey);

      // Check all logs for API key exposure
      const allLogs = [...logCapture.logs, ...logCapture.errors, ...logCapture.warns];
      allLogs.forEach(log => {
        expect(log).not.toContain(testApiKey);
        expect(log).not.toContain('sk-real1234567890abcdef');
      });
    });

    it('should handle API key errors securely', () => {
      // Test with null/undefined API key
      const nullResult = validateApiKeySecurity(null);
      expect(nullResult.isSecure).toBe(true);

      const undefinedResult = validateApiKeySecurity(undefined as any);
      expect(undefinedResult.isSecure).toBe(true);

      // Test retrieval of non-existent key
      mockLocalStorage.getItem.mockReturnValue(null);
      const retrievedKey = getOpenAIKey();
      expect(retrievedKey).toBeNull();
    });

    it('should not store API keys in zustand persist storage', () => {
      const testApiKey = 'sk-test1234567890abcdef';
      const { setOpenAIKey } = useAIModelStore.getState();

      setOpenAIKey(testApiKey);

      // Check that API key is not in the persisted state
      // The store should only persist currentModel, not API keys
      const persistedData = mockLocalStorage.accessLog.find(
        entry => entry.key === 'ai-model-storage' && entry.action === 'set'
      );

      if (persistedData && persistedData.value) {
        expect(persistedData.value).not.toContain(testApiKey);
        expect(persistedData.value).not.toContain('openaiApiKey');
      }
    });

    it('should handle API key removal securely', () => {
      const testApiKey = 'sk-test1234567890abcdef';

      // Set and then remove API key
      setOpenAIKey(testApiKey);
      mockLocalStorage.removeItem('openai_api_key');

      // Verify removal
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('openai_api_key');

      // Verify key is no longer retrievable
      mockLocalStorage.getItem.mockReturnValue(null);
      const retrievedKey = getOpenAIKey();
      expect(retrievedKey).toBeNull();
    });
  });

  describe('General Security Measures', () => {
    it('should not expose sensitive data in error messages', () => {
      const sensitiveData = 'sk-sensitive1234567890';

      try {
        // Simulate an error that might contain sensitive data
        throw new Error(`API call failed with key: ${sensitiveData}`);
      } catch (error) {
        // In a real application, error handling should sanitize sensitive data
        const errorMessage = error instanceof Error ? error.message : String(error);

        // This test documents current behavior - in production,
        // error messages should be sanitized
        console.error('Sanitized error:', 'API call failed');
      }

      // Check that sanitized error was logged instead of raw error
      const errorLogs = logCapture.errors;
      const hasSanitizedError = errorLogs.some(log =>
        log.includes('Sanitized error') && !log.includes(sensitiveData)
      );
      expect(hasSanitizedError).toBe(true);
    });

    it('should clear sensitive data on logout', () => {
      const testApiKey = 'sk-test1234567890abcdef';
      const testWalletAddress = 'test-wallet-address';

      // Set up data
      setOpenAIKey(testApiKey);
      mockLocalStorage.setItem('wallet_address', testWalletAddress);

      // Simulate logout - clear sensitive data
      mockLocalStorage.removeItem('openai_api_key');
      mockLocalStorage.removeItem('jwt_token');

      // Verify sensitive data was removed
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('openai_api_key');
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('jwt_token');
    });

    it('should validate that no sensitive patterns appear in component state', () => {
      // Get current states
      const walletState = useWalletStore.getState();
      const aiModelState = useAIModelStore.getState();

      // Convert states to strings for pattern checking
      const walletStateString = JSON.stringify(walletState);
      const aiModelStateString = JSON.stringify(aiModelState);

      // Check for sensitive patterns in wallet state
      const walletViolations = checkForSensitiveDataInLogs(
        [walletStateString],
        [...SENSITIVE_PATTERNS.PRIVATE_KEYS, ...SENSITIVE_PATTERNS.WALLET_SECRETS]
      );
      expect(walletViolations).toHaveLength(0);

      // Check for sensitive patterns in AI model state (excluding legitimate API key storage)
      const aiModelViolations = checkForSensitiveDataInLogs(
        [aiModelStateString],
        SENSITIVE_PATTERNS.PRIVATE_KEYS
      );
      expect(aiModelViolations).toHaveLength(0);
    });
  });
});
