import { describe, it, expect, vi, beforeEach } from 'vitest';
import { getUserActivity, getUserTotalAssets, getPosition } from '@/lib/position';
import { depositFunction, withdrawFunction } from '@/lib/trade/drift';
import { api } from '@/services/apiClient';

// Mock dependencies
vi.mock('@/services/apiClient');
vi.mock('@/lib/trade/getTokenBalance');
vi.mock('@/store/walletStore');
vi.mock('@/hooks/use-toast');

// Mock Solana web3
vi.mock('@solana/web3.js', () => ({
  Connection: vi.fn(),
  PublicKey: vi.fn().mockImplementation((key) => ({
    toBase58: () => key,
    toString: () => key
  })),
  Transaction: vi.fn().mockImplementation(() => ({
    add: vi.fn().mockReturnThis(),
    recentBlockhash: null,
    lastValidBlockHeight: null,
    feePayer: null
  })),
  ComputeBudgetProgram: {
    setComputeUnitPrice: vi.fn().mockReturnValue('mock-priority-fee-ix')
  }
}));

// Mock Drift SDK
vi.mock('@drift-labs/sdk', () => ({
  DriftClient: vi.fn(),
  BN: vi.fn(),
  getUserAccountPublicKey: vi.fn().mockResolvedValue('mock-user-account-pubkey')
}));

// Mock the createClient function
vi.mock('@/lib/trade/drift', async () => {
  const actual = await vi.importActual('@/lib/trade/drift');
  return {
    ...actual,
    createClient: vi.fn().mockResolvedValue({
      subscribe: vi.fn(),
      convertToSpotPrecision: vi.fn(),
      getAssociatedTokenAccount: vi.fn(),
    })
  };
});

const mockApi = vi.mocked(api);

describe('Trading and Position Management', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('User Activity', () => {
    it('should fetch and format user activity data correctly', async () => {
      const mockUserActivityData = {
        'agent-1': {
          agentName: 'Test Agent',
          orders: {
            records: [
              {
                ts: **********,
                quoteAssetAmountFilled: 100.50,
                takerFee: 0.1,
                takerOrderDirection: 'long',
                txSig: 'test-signature-1',
              },
            ],
          },
        },
      };

      mockApi.get.mockResolvedValue(mockUserActivityData);
      const result = await getUserActivity('test-pubkey');

      expect(mockApi.get).toHaveBeenCalledWith('/api/trade/get-user-activity?pubkey=test-pubkey');
      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        date: new Date(********** * 1000).toLocaleString(),
        amount: '100.50',
        fee: 0.1,
        direction: 'long',
        txId: 'test-signature-1',
        agentName: 'Test Agent',
      });
    });

    it('should handle API failure and invalid data', async () => {
      mockApi.get.mockRejectedValue(new Error('API Error'));
      const result = await getUserActivity('test-pubkey');
      expect(result).toEqual([]);
    });
  });

  describe('User Assets', () => {
    it('should fetch and format total assets correctly', async () => {
      mockApi.get.mockResolvedValue({ assets: 1250.75 });
      const result = await getUserTotalAssets('test-pubkey');

      expect(mockApi.get).toHaveBeenCalledWith('/api/trade/get-user-assets?pubkey=test-pubkey');
      expect(result).toBe('1250.75');
    });

    it('should handle missing or null assets', async () => {
      mockApi.get.mockResolvedValue({ assets: null });
      const result = await getUserTotalAssets('test-pubkey');
      expect(result).toBe(0);
    });
  });

  describe('Position Data', () => {
    it('should fetch and format position data correctly', async () => {
      const mockPositionData = {
        response: {
          position: 1.5,
          pnl: 25.50,
          positionValue: -150.75,
          portfolioValue: 1250.00,
        },
      };

      mockApi.get.mockResolvedValue(mockPositionData);
      const result = await getPosition('test-agent-id');

      expect(mockApi.get).toHaveBeenCalledWith('/api/trade/get-drift-position?agentId=test-agent-id');
      expect(result).toEqual({
        positionSize: '1.50',
        pnl: '25.50',
        positionValueUsd: 150.75, // Should be absolute value
        portfolioValue: '1250.00',
      });
    });

    it('should handle missing or null response data', async () => {
      mockApi.get.mockResolvedValue({});
      const result = await getPosition('test-agent-id');

      expect(result).toEqual({
        positionSize: '0',
        pnl: '0',
        positionValueUsd: '0',
        portfolioValue: '0',
      });
    });
  });

  describe('Deposit Function', () => {
    const createMockWallet = (signature = 'mock-signature') => ({
      publicKey: 'test-wallet-pubkey',
      sendTransaction: vi.fn().mockResolvedValue(signature)
    });

    const createMockConnection = () => ({
      getLatestBlockhash: vi.fn().mockResolvedValue({
        blockhash: 'mock-blockhash',
        lastValidBlockHeight: 12345
      })
    });

    const createMockDriftClient = () => ({
      convertToSpotPrecision: vi.fn().mockReturnValue(BigInt(*********)),
      getAssociatedTokenAccount: vi.fn().mockResolvedValue('mock-token-account'),
      createInitializeUserAccountAndDepositCollateralIxs: vi.fn().mockResolvedValue({
        ixs: ['mock-init-ix'],
        subAccountId: 1
      }),
      program: {
        programId: 'mock-program-id',
        methods: {
          updateUserDelegate: vi.fn().mockReturnValue({
            accounts: vi.fn().mockReturnThis(),
            instruction: vi.fn().mockResolvedValue('mock-delegate-ix')
          })
        }
      }
    });

    it('should execute deposit function successfully', async () => {
      // Mock getTokenBalance to return sufficient balance
      const { getTokenBalance } = await import('@/lib/trade/getTokenBalance');
      vi.mocked(getTokenBalance).mockResolvedValue(1000);

      // Mock wallet store
      const { useWalletStore } = await import('@/store/walletStore');
      const mockSetDriftClient = vi.fn();
      vi.mocked(useWalletStore).getState = vi.fn().mockReturnValue({
        setDriftClient: mockSetDriftClient
      });

      // Set up environment
      import.meta.env.VITE_ADMIN_KEY = 'mock-admin-key-pubkey';

      const mockWallet = createMockWallet('successful-deposit-signature');
      const mockConnection = createMockConnection();
      const mockDriftClient = createMockDriftClient();

      const result = await depositFunction(mockWallet as any, 100, mockConnection as any, mockDriftClient as any, true);

      // Verify core deposit business logic
      expect(getTokenBalance).toHaveBeenCalled();
      expect(mockDriftClient.convertToSpotPrecision).toHaveBeenCalledWith(0, 100);
      expect(mockDriftClient.getAssociatedTokenAccount).toHaveBeenCalledWith(0);
      expect(result).toBeDefined();
    });

    it('should handle missing admin key', async () => {
      const originalEnv = import.meta.env.VITE_ADMIN_KEY;
      delete import.meta.env.VITE_ADMIN_KEY;

      const result = await depositFunction(createMockWallet() as any, 100, createMockConnection() as any, createMockDriftClient() as any, true);
      expect(result).toBeUndefined();

      // Restore admin key
      import.meta.env.VITE_ADMIN_KEY = originalEnv;
    });
  });

  describe('Withdraw Function', () => {
    const createMockWallet = (signature = 'mock-signature') => ({
      publicKey: 'test-wallet-pubkey',
      sendTransaction: vi.fn().mockResolvedValue(signature)
    });

    const createMockConnection = () => ({
      getLatestBlockhash: vi.fn().mockResolvedValue({
        blockhash: 'mock-blockhash',
        lastValidBlockHeight: 12345
      })
    });

    const createMockDriftClient = () => ({
      convertToSpotPrecision: vi.fn().mockReturnValue(BigInt(********)),
      getAssociatedTokenAccount: vi.fn().mockResolvedValue('mock-token-account'),
      getWithdrawalIxs: vi.fn().mockResolvedValue(['mock-withdraw-ix'])
    });

    it('should execute withdraw function successfully', async () => {
      const { getTokenBalance } = await import('@/lib/trade/getTokenBalance');
      vi.mocked(getTokenBalance).mockResolvedValue(500);

      const mockWallet = createMockWallet('successful-withdraw-signature');
      const mockConnection = createMockConnection();
      const mockDriftClient = createMockDriftClient();

      const result = await withdrawFunction(mockWallet as any, 50, mockConnection as any, mockDriftClient as any, false, 1);

      // Verify core withdraw business logic
      expect(mockDriftClient.convertToSpotPrecision).toHaveBeenCalledWith(0, 50);
      expect(mockDriftClient.getWithdrawalIxs).toHaveBeenCalled();
      expect(result).toBeDefined();
    });

    it('should handle missing subAccountId', async () => {
      const result = await withdrawFunction(createMockWallet() as any, 50, createMockConnection() as any, createMockDriftClient() as any, false);
      expect(result).toEqual({ signature: null });
    });
  });
});
