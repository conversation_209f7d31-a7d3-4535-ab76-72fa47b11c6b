import { describe, it, expect, vi, beforeEach } from 'vitest';
import { searchOpenAI } from '@/utils/openai';

// Mock the localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock fetch
global.fetch = vi.fn();

describe('OpenAI Web Search', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return error message when no API key is found', async () => {
    localStorageMock.getItem.mockReturnValue(null);

    const result = await searchOpenAI('test query');

    expect(result).toEqual({
      results: [],
      message: "OpenAI API key not found. Please set your API key."
    });
  });

  it('should make single API call with search-preview model and return AI response', async () => {
    const mockApiKey = 'test-api-key';
    localStorageMock.getItem.mockReturnValue(mockApiKey);

    // Mock response from gpt-4o-search-preview with web search integrated
    const mockResponse = {
      choices: [{
        message: {
          content: 'Based on my web search, here are the latest insights about your query: Test search results with comprehensive analysis and up-to-date information.',
        }
      }]
    };

    (global.fetch as any).mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve(mockResponse)
    });

    const result = await searchOpenAI('test query');

    // Should make only ONE API call with the new search-preview model
    expect(global.fetch).toHaveBeenCalledTimes(1);
    expect(global.fetch).toHaveBeenCalledWith('https://api.openai.com/v1/chat/completions',
      expect.objectContaining({
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${mockApiKey}`
        },
        body: expect.stringContaining('"model":"gpt-4o-search-preview"')
      })
    );

    // Verify the body contains the expected system message
    const fetchCall = (global.fetch as any).mock.calls[0];
    const requestBody = JSON.parse(fetchCall[1].body);
    expect(requestBody.messages[0].role).toBe('system');
    expect(requestBody.messages[0].content).toContain('financial assistant specializing in cryptocurrency trading');
    expect(requestBody.messages[1].role).toBe('user');
    expect(requestBody.messages[1].content).toBe('test query');
    expect(requestBody.web_search_options).toEqual({ search_context_size: 'medium' });

    // Should return the AI's response that includes web search context
    expect(result).toEqual({
      results: [], // No separate results parsing needed with new model
      message: 'Based on my web search, here are the latest insights about your query: Test search results with comprehensive analysis and up-to-date information.'
    });
  });

  it('should handle API errors gracefully', async () => {
    const mockApiKey = 'test-api-key';
    localStorageMock.getItem.mockReturnValue(mockApiKey);

    (global.fetch as any).mockResolvedValueOnce({
      ok: false,
      json: () => Promise.resolve({ error: { message: 'API Error' } })
    });

    const result = await searchOpenAI('test query');

    expect(result).toEqual({
      results: [],
      message: "Failed to fetch web search results from OpenAI"
    });
  });

  it('should handle network errors gracefully', async () => {
    const mockApiKey = 'test-api-key';
    localStorageMock.getItem.mockReturnValue(mockApiKey);

    (global.fetch as any).mockRejectedValueOnce(new Error('Network error'));

    const result = await searchOpenAI('test query');

    expect(result).toEqual({
      results: [],
      message: "Failed to fetch web search results"
    });
  });

  it('should use search-preview model with proper web_search_options configuration', async () => {
    const mockApiKey = 'test-api-key';
    localStorageMock.getItem.mockReturnValue(mockApiKey);

    const mockResponse = {
      choices: [{
        message: {
          content: 'Comprehensive search results with integrated web context and analysis.',
        }
      }]
    };

    (global.fetch as any).mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve(mockResponse)
    });

    const result = await searchOpenAI('test query');

    expect(global.fetch).toHaveBeenCalledWith('https://api.openai.com/v1/chat/completions',
      expect.objectContaining({
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${mockApiKey}`
        },
        body: expect.stringContaining('"model":"gpt-4o-search-preview"')
      })
    );

    // Verify the request body structure
    const fetchCall = (global.fetch as any).mock.calls[0];
    const requestBody = JSON.parse(fetchCall[1].body);
    expect(requestBody.messages[0].content).toContain('financial assistant specializing in cryptocurrency trading');
    expect(requestBody.web_search_options).toEqual({ search_context_size: 'medium' });

    expect(result.message).toBe('Comprehensive search results with integrated web context and analysis.');
  });
});
