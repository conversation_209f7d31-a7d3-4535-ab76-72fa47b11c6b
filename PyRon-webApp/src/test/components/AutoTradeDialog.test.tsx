import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import AutoTradeDialog from '../../components/AutoTradeDialog';
import { ChatProvider } from '../../context/ChatContext';

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn(() => Promise.resolve()),
  },
});

// Mock window.alert
Object.assign(window, {
  alert: vi.fn(),
});

// Mock dependencies
vi.mock('../../lib/chat', () => ({
  createNewChat: vi.fn(),
  getUserChats: vi.fn(),
  getChatMessages: vi.fn().mockResolvedValue([])
}));

vi.mock('../../lib/agents', () => ({
  saveOrUpdateAgent: vi.fn(),
  getUserAgentByChatId: vi.fn()
}));

vi.mock('../../store/walletStore', () => {
  // Create a mock function that can be both called as a hook and has getState method
  const mockWalletStore = vi.fn(() => ({
    isConnected: true,
    walletAddress: 'test-wallet-address'
  }));

  mockWalletStore.getState = vi.fn(() => ({
    isConnected: true,
    walletAddress: 'test-wallet-address'
  }));

  return {
    useWalletStore: mockWalletStore
  };
});

vi.mock('../../components/DepositDialog', () => ({
  default: () => <div data-testid="deposit-dialog">Deposit Dialog</div>
}));

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <ChatProvider>
    {children}
  </ChatProvider>
);

describe('AutoTradeDialog - Override Rules', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset clipboard mock
    vi.mocked(navigator.clipboard.writeText).mockClear();
  });

  it('should display override buy rule configuration', async () => {
    await act(async () => {
      render(
        <TestWrapper>
          <AutoTradeDialog open={true} onOpenChange={() => {}} />
        </TestWrapper>
      );
    });

    // Should have Override Buy rule type
    expect(screen.getByText('Override Buy')).toBeInTheDocument();
  });

  it('should display override sell rule configuration', async () => {
    await act(async () => {
      render(
        <TestWrapper>
          <AutoTradeDialog open={true} onOpenChange={() => {}} />
        </TestWrapper>
      );
    });

    // Should have Override Sell rule type
    expect(screen.getByText('Override Sell')).toBeInTheDocument();
  });

  it('should generate override buy alert with correct action', async () => {
    await act(async () => {
      render(
        <TestWrapper>
          <AutoTradeDialog open={true} onOpenChange={() => {}} />
        </TestWrapper>
      );
    });

    // Find override buy copy button using test id
    const overrideBuyCopyButton = screen.getByTestId('copy-action-overrideBuy');
    expect(overrideBuyCopyButton).toBeInTheDocument();

    // Click the copy button
    await act(async () => {
      fireEvent.click(overrideBuyCopyButton);
    });

    // Verify clipboard was called with correct action
    expect(navigator.clipboard.writeText).toHaveBeenCalledWith(
      expect.stringContaining('"action": "overrideBuy"')
    );
  });

  it('should generate override sell alert with correct action', async () => {
    await act(async () => {
      render(
        <TestWrapper>
          <AutoTradeDialog open={true} onOpenChange={() => {}} />
        </TestWrapper>
      );
    });

    // Find override sell copy button using test id
    const overrideSellCopyButton = screen.getByTestId('copy-action-overrideSell');
    expect(overrideSellCopyButton).toBeInTheDocument();

    // Click the copy button
    await act(async () => {
      fireEvent.click(overrideSellCopyButton);
    });

    // Verify clipboard was called with correct action
    expect(navigator.clipboard.writeText).toHaveBeenCalledWith(
      expect.stringContaining('"action": "overrideSell"')
    );
  });

  it('should save override confirmation values to agent', async () => {
    const mockSaveOrUpdateAgent = vi.fn();
    const mockCreateNewChat = vi.fn().mockResolvedValue('test-chat-id');

    const agentsModule = await import('../../lib/agents');
    const chatModule = await import('../../lib/chat');

    vi.mocked(agentsModule.saveOrUpdateAgent).mockImplementation(mockSaveOrUpdateAgent);
    vi.mocked(chatModule.createNewChat).mockImplementation(mockCreateNewChat);

    await act(async () => {
      render(
        <TestWrapper>
          <AutoTradeDialog open={true} onOpenChange={() => {}} />
        </TestWrapper>
      );
    });

    // Fill in required form fields first
    const agentNameInput = screen.getByPlaceholderText('Your Agent\'s Name');
    await act(async () => {
      fireEvent.change(agentNameInput, { target: { value: 'Test Agent' } });
    });

    const assetSelect = screen.getByDisplayValue('-- Select the asset to auto-trade--');
    await act(async () => {
      fireEvent.change(assetSelect, { target: { value: 'SOL' } });
    });

    // Find override buy select using test id
    const overrideBuySelect = screen.getByTestId('rule-select-overrideBuy');
    await act(async () => {
      fireEvent.change(overrideBuySelect, { target: { value: '5' } });
    });

    // Find override sell select using test id
    const overrideSellSelect = screen.getByTestId('rule-select-overrideSell');
    await act(async () => {
      fireEvent.change(overrideSellSelect, { target: { value: '10' } });
    });

    // Find and click the Auto-Trade button (which acts as save)
    const autoTradeButton = screen.getByText('Auto-Trade');
    await act(async () => {
      fireEvent.click(autoTradeButton);
    });

    await waitFor(() => {
      expect(mockSaveOrUpdateAgent).toHaveBeenCalledWith(
        expect.objectContaining({
          requiredBuyConfirmationsOverride: 5,
          requiredSellConfirmationsOverride: 10
        }),
        expect.any(String)
      );
    });
  });

  it('should generate correct rule sentence for override buy', async () => {
    await act(async () => {
      render(
        <TestWrapper>
          <AutoTradeDialog open={true} onOpenChange={() => {}} />
        </TestWrapper>
      );
    });

    // Should show rule sentence like "If 1 buy confirm & lookback = buy(close bar) then LONG"
    // Use getAllByText and check the first one since there are multiple options
    const buyRuleTexts = screen.getAllByText(/If \d+ buy confirm & lookback = buy\(close bar\) then LONG/);
    expect(buyRuleTexts.length).toBeGreaterThan(0);
  });

  it('should generate correct rule sentence for override sell', async () => {
    await act(async () => {
      render(
        <TestWrapper>
          <AutoTradeDialog open={true} onOpenChange={() => {}} />
        </TestWrapper>
      );
    });

    // Should show rule sentence like "If 1 sell confirm & lookback = sell(close bar) then SHORT"
    // Use getAllByText and check the first one since there are multiple options
    const sellRuleTexts = screen.getAllByText(/If \d+ sell confirm & lookback = sell\(close bar\) then SHORT/);
    expect(sellRuleTexts.length).toBeGreaterThan(0);
  });

  it('should allow override confirmation values from 1 to 60', async () => {
    await act(async () => {
      render(
        <TestWrapper>
          <AutoTradeDialog open={true} onOpenChange={() => {}} />
        </TestWrapper>
      );
    });

    // Check that override buy select has options 1-60
    const overrideBuySelect = screen.getByTestId('rule-select-overrideBuy');
    const options = overrideBuySelect.querySelectorAll('option');

    expect(options).toHaveLength(60); // Should have 60 options (1-60)
    expect(options[0]).toHaveValue('1');
    expect(options[59]).toHaveValue('60');
  });

  it('should include override rules in rule configs state', async () => {
    await act(async () => {
      render(
        <TestWrapper>
          <AutoTradeDialog open={true} onOpenChange={() => {}} />
        </TestWrapper>
      );
    });

    // Should have 8 total rules (6 existing + 2 new override rules)
    const ruleRows = screen.getAllByRole('row'); // Rules have role="row" attribute
    expect(ruleRows).toHaveLength(8); // 6 existing + 2 override rules
  });

  it('should update override rule confirmation count when changed', async () => {
    await act(async () => {
      render(
        <TestWrapper>
          <AutoTradeDialog open={true} onOpenChange={() => {}} />
        </TestWrapper>
      );
    });

    // Find override buy select and change value
    const overrideBuySelect = screen.getByTestId('rule-select-overrideBuy');
    await act(async () => {
      fireEvent.change(overrideBuySelect, { target: { value: '25' } });
    });

    // Should update the display to show new value
    expect(overrideBuySelect).toHaveValue('25');
  });
});

describe('AutoTradeDialog - Agent Creation', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should display agent creation form fields', async () => {
    await act(async () => {
      render(
        <TestWrapper>
          <AutoTradeDialog open={true} onOpenChange={() => {}} />
        </TestWrapper>
      );
    });

    // Should have agent name input
    expect(screen.getByPlaceholderText('Your Agent\'s Name')).toBeInTheDocument();

    // Should have asset pair selection
    expect(screen.getByDisplayValue('-- Select the asset to auto-trade--')).toBeInTheDocument();

    // Should have webhook URL input field
    const webhookInput = screen.getByDisplayValue(new RegExp(import.meta.env.VITE_WEBHOOK_URL || 'webhook'));
    expect(webhookInput).toBeInTheDocument();
  });

  it('should validate required fields for agent creation', async () => {
    const mockSaveOrUpdateAgent = vi.fn();
    const agentsModule = await import('../../lib/agents');
    vi.mocked(agentsModule.saveOrUpdateAgent).mockImplementation(mockSaveOrUpdateAgent);

    await act(async () => {
      render(
        <TestWrapper>
          <AutoTradeDialog open={true} onOpenChange={() => {}} />
        </TestWrapper>
      );
    });

    // Try to submit without filling required fields
    const autoTradeButton = screen.getByText('Auto-Trade');
    await act(async () => {
      fireEvent.click(autoTradeButton);
    });

    // Should not call saveOrUpdateAgent with empty fields
    expect(mockSaveOrUpdateAgent).not.toHaveBeenCalled();
  });

  it('should generate unique webhook URL with bot ID', async () => {
    await act(async () => {
      render(
        <TestWrapper>
          <AutoTradeDialog open={true} onOpenChange={() => {}} />
        </TestWrapper>
      );
    });

    // Should display webhook URL input with environment variable and unique ID
    const webhookInput = screen.getByDisplayValue(new RegExp(import.meta.env.VITE_WEBHOOK_URL || 'webhook'));
    expect(webhookInput).toBeInTheDocument();
  });

  it('should create new chat when agent is created', async () => {
    const mockCreateNewChat = vi.fn().mockResolvedValue('new-chat-id');
    const mockSaveOrUpdateAgent = vi.fn().mockResolvedValue({ success: true });

    const chatModule = await import('../../lib/chat');
    const agentsModule = await import('../../lib/agents');

    vi.mocked(chatModule.createNewChat).mockImplementation(mockCreateNewChat);
    vi.mocked(agentsModule.saveOrUpdateAgent).mockImplementation(mockSaveOrUpdateAgent);

    await act(async () => {
      render(
        <TestWrapper>
          <AutoTradeDialog open={true} onOpenChange={() => {}} />
        </TestWrapper>
      );
    });

    // Fill required fields
    const agentNameInput = screen.getByPlaceholderText('Your Agent\'s Name');
    const assetSelect = screen.getByDisplayValue('-- Select the asset to auto-trade--');

    await act(async () => {
      fireEvent.change(agentNameInput, { target: { value: 'Test Agent' } });
      fireEvent.change(assetSelect, { target: { value: 'SOL' } });
    });

    // Submit form
    const autoTradeButton = screen.getByText('Auto-Trade');
    await act(async () => {
      fireEvent.click(autoTradeButton);
    });

    await waitFor(() => {
      expect(mockCreateNewChat).toHaveBeenCalledWith('Test Agent');
    });
  });
});

describe('AutoTradeDialog - Trading Rules Configuration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should display Long and Short rule configurations', async () => {
    await act(async () => {
      render(
        <TestWrapper>
          <AutoTradeDialog open={true} onOpenChange={() => {}} />
        </TestWrapper>
      );
    });

    // Should have Long rule configuration
    expect(screen.getByText('Long')).toBeInTheDocument();

    // Should have Short rule configuration
    expect(screen.getByText('Short')).toBeInTheDocument();
  });

  it('should allow confirmation count settings for Long rules', async () => {
    await act(async () => {
      render(
        <TestWrapper>
          <AutoTradeDialog open={true} onOpenChange={() => {}} />
        </TestWrapper>
      );
    });

    // Find Long rule confirmation select
    const longRuleSelect = screen.getByTestId('rule-select-Long');
    expect(longRuleSelect).toBeInTheDocument();

    // Should have confirmation count options
    const options = longRuleSelect.querySelectorAll('option');
    expect(options.length).toBeGreaterThan(0);
  });

  it('should allow confirmation count settings for Short rules', async () => {
    await act(async () => {
      render(
        <TestWrapper>
          <AutoTradeDialog open={true} onOpenChange={() => {}} />
        </TestWrapper>
      );
    });

    // Find Short rule confirmation select
    const shortRuleSelect = screen.getByTestId('rule-select-Short');
    expect(shortRuleSelect).toBeInTheDocument();

    // Should have confirmation count options
    const options = shortRuleSelect.querySelectorAll('option');
    expect(options.length).toBeGreaterThan(0);
  });

  it('should generate correct rule sentence for Long configuration', async () => {
    await act(async () => {
      render(
        <TestWrapper>
          <AutoTradeDialog open={true} onOpenChange={() => {}} />
        </TestWrapper>
      );
    });

    // Should show Long rule sentence
    const longRuleTexts = screen.getAllByText(/If \d+ buy confirm & lookback = buy\(close bar\) then LONG/);
    expect(longRuleTexts.length).toBeGreaterThan(0);
  });

  it('should generate correct rule sentence for Short configuration', async () => {
    await act(async () => {
      render(
        <TestWrapper>
          <AutoTradeDialog open={true} onOpenChange={() => {}} />
        </TestWrapper>
      );
    });

    // Should show Short rule sentence
    const shortRuleTexts = screen.getAllByText(/If \d+ sell confirm & lookback = sell\(close bar\) then SHORT/);
    expect(shortRuleTexts.length).toBeGreaterThan(0);
  });

  it('should save trading rule configurations to agent', async () => {
    const mockSaveOrUpdateAgent = vi.fn();
    const mockCreateNewChat = vi.fn().mockResolvedValue('test-chat-id');

    const agentsModule = await import('../../lib/agents');
    const chatModule = await import('../../lib/chat');

    vi.mocked(agentsModule.saveOrUpdateAgent).mockImplementation(mockSaveOrUpdateAgent);
    vi.mocked(chatModule.createNewChat).mockImplementation(mockCreateNewChat);

    await act(async () => {
      render(
        <TestWrapper>
          <AutoTradeDialog open={true} onOpenChange={() => {}} />
        </TestWrapper>
      );
    });

    // Fill required fields
    const agentNameInput = screen.getByPlaceholderText('Your Agent\'s Name');
    const assetSelect = screen.getByDisplayValue('-- Select the asset to auto-trade--');

    await act(async () => {
      fireEvent.change(agentNameInput, { target: { value: 'Test Agent' } });
      fireEvent.change(assetSelect, { target: { value: 'SOL' } });
    });

    // Configure Long rule
    const longRuleSelect = screen.getByTestId('rule-select-Long');
    await act(async () => {
      fireEvent.change(longRuleSelect, { target: { value: '3' } });
    });

    // Configure Short rule
    const shortRuleSelect = screen.getByTestId('rule-select-Short');
    await act(async () => {
      fireEvent.change(shortRuleSelect, { target: { value: '2' } });
    });

    // Submit form
    const autoTradeButton = screen.getByText('Auto-Trade');
    await act(async () => {
      fireEvent.click(autoTradeButton);
    });

    await waitFor(() => {
      expect(mockSaveOrUpdateAgent).toHaveBeenCalledWith(
        expect.objectContaining({
          requiredBuyConfirmationsOpen: 3,
          requiredSellConfirmationsOpen: 2
        }),
        expect.any(String)
      );
    });
  });
});

describe('AutoTradeDialog - Auto-Trade Toggle', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should display auto-trade toggle button', async () => {
    await act(async () => {
      render(
        <TestWrapper>
          <AutoTradeDialog open={true} onOpenChange={() => {}} />
        </TestWrapper>
      );
    });

    // Should have Auto-Trade button
    const autoTradeButton = screen.getByText('Auto-Trade');
    expect(autoTradeButton).toBeInTheDocument();

    // Should show OFF status initially
    expect(screen.getByText('OFF')).toBeInTheDocument();
  });

  it('should toggle trading status from OFF to ON', async () => {
    const mockSaveOrUpdateAgent = vi.fn().mockResolvedValue({
      success: true,
      number: 1, // Existing agent with deposit
      tradingStatus: 'on'
    });
    const mockCreateNewChat = vi.fn().mockResolvedValue('test-chat-id');

    const agentsModule = await import('../../lib/agents');
    const chatModule = await import('../../lib/chat');

    vi.mocked(agentsModule.saveOrUpdateAgent).mockImplementation(mockSaveOrUpdateAgent);
    vi.mocked(chatModule.createNewChat).mockImplementation(mockCreateNewChat);

    await act(async () => {
      render(
        <TestWrapper>
          <AutoTradeDialog open={true} onOpenChange={() => {}} />
        </TestWrapper>
      );
    });

    // Fill required fields
    const agentNameInput = screen.getByPlaceholderText('Your Agent\'s Name');
    const assetSelect = screen.getByDisplayValue('-- Select the asset to auto-trade--');

    await act(async () => {
      fireEvent.change(agentNameInput, { target: { value: 'Test Agent' } });
      fireEvent.change(assetSelect, { target: { value: 'SOL' } });
    });

    // Click Auto-Trade button to toggle
    const autoTradeButton = screen.getByText('Auto-Trade');
    await act(async () => {
      fireEvent.click(autoTradeButton);
    });

    await waitFor(() => {
      expect(mockSaveOrUpdateAgent).toHaveBeenCalledWith(
        expect.objectContaining({
          tradingStatus: 'off' // Should toggle to off first, then on
        }),
        expect.any(String)
      );
    });
  });

  it('should verify agent state updates when toggling', async () => {
    const mockSaveOrUpdateAgent = vi.fn().mockResolvedValue({
      success: true,
      number: 1,
      tradingStatus: 'on'
    });
    const mockCreateNewChat = vi.fn().mockResolvedValue('test-chat-id');

    const agentsModule = await import('../../lib/agents');
    const chatModule = await import('../../lib/chat');

    vi.mocked(agentsModule.saveOrUpdateAgent).mockImplementation(mockSaveOrUpdateAgent);
    vi.mocked(chatModule.createNewChat).mockImplementation(mockCreateNewChat);

    await act(async () => {
      render(
        <TestWrapper>
          <AutoTradeDialog open={true} onOpenChange={() => {}} />
        </TestWrapper>
      );
    });

    // Fill required fields and submit
    const agentNameInput = screen.getByPlaceholderText('Your Agent\'s Name');
    const assetSelect = screen.getByDisplayValue('-- Select the asset to auto-trade--');

    await act(async () => {
      fireEvent.change(agentNameInput, { target: { value: 'Test Agent' } });
      fireEvent.change(assetSelect, { target: { value: 'SOL' } });
    });

    const autoTradeButton = screen.getByText('Auto-Trade');
    await act(async () => {
      fireEvent.click(autoTradeButton);
    });

    // Should save agent with updated trading status
    await waitFor(() => {
      expect(mockSaveOrUpdateAgent).toHaveBeenCalled();
    });
  });

  it('should require deposit for new agents before enabling trading', async () => {
    const mockSaveOrUpdateAgent = vi.fn().mockResolvedValue({
      success: true,
      number: null // New agent without deposit
    });
    const mockCreateNewChat = vi.fn().mockResolvedValue('test-chat-id');

    const agentsModule = await import('../../lib/agents');
    const chatModule = await import('../../lib/chat');

    vi.mocked(agentsModule.saveOrUpdateAgent).mockImplementation(mockSaveOrUpdateAgent);
    vi.mocked(chatModule.createNewChat).mockImplementation(mockCreateNewChat);

    await act(async () => {
      render(
        <TestWrapper>
          <AutoTradeDialog open={true} onOpenChange={() => {}} />
        </TestWrapper>
      );
    });

    // Fill required fields
    const agentNameInput = screen.getByPlaceholderText('Your Agent\'s Name');
    const assetSelect = screen.getByDisplayValue('-- Select the asset to auto-trade--');

    await act(async () => {
      fireEvent.change(agentNameInput, { target: { value: 'Test Agent' } });
      fireEvent.change(assetSelect, { target: { value: 'SOL' } });
    });

    // Try to enable trading
    const autoTradeButton = screen.getByText('Auto-Trade');
    await act(async () => {
      fireEvent.click(autoTradeButton);
    });

    // Should open deposit dialog for new agents
    await waitFor(() => {
      expect(screen.getByTestId('deposit-dialog')).toBeInTheDocument();
    });
  });

  it('should handle trading status persistence', async () => {
    const mockSaveOrUpdateAgent = vi.fn().mockResolvedValue({
      success: true,
      number: 1,
      tradingStatus: 'on'
    });
    const mockCreateNewChat = vi.fn().mockResolvedValue('test-chat-id');

    const agentsModule = await import('../../lib/agents');
    const chatModule = await import('../../lib/chat');
    vi.mocked(agentsModule.saveOrUpdateAgent).mockImplementation(mockSaveOrUpdateAgent);
    vi.mocked(chatModule.createNewChat).mockImplementation(mockCreateNewChat);

    await act(async () => {
      render(
        <TestWrapper>
          <AutoTradeDialog open={true} onOpenChange={() => {}} />
        </TestWrapper>
      );
    });

    // Fill in all required fields
    const agentNameInput = screen.getByPlaceholderText('Your Agent\'s Name');
    const assetSelect = screen.getByDisplayValue('-- Select the asset to auto-trade--');

    await act(async () => {
      fireEvent.change(agentNameInput, { target: { value: 'Test Agent' } });
      fireEvent.change(assetSelect, { target: { value: 'SOL' } });
    });

    const autoTradeButton = screen.getByText('Auto-Trade');
    await act(async () => {
      fireEvent.click(autoTradeButton);
    });

    await waitFor(() => {
      expect(mockSaveOrUpdateAgent).toHaveBeenCalledWith(
        expect.objectContaining({
          tradingStatus: expect.any(String)
        }),
        expect.any(String)
      );
    });
  });
});
