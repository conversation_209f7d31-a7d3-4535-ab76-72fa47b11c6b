import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import DepositDialog from '@/components/DepositDialog';
import { useWalletStore } from '@/store/walletStore';
import { depositFunction } from '@/lib/trade/drift';
import { saveOrUpdateAgent } from '@/lib/agents';
import { toast } from '@/hooks/use-toast';
import { createMockWalletAdapter, createMockConnection, createMockDriftClient } from '@/test/utils/mockWallet';

// Mock dependencies
vi.mock('@/store/walletStore');
vi.mock('@/lib/trade/drift');
vi.mock('@/lib/agents');
vi.mock('@/hooks/use-toast');

// Mock UI components
vi.mock('@/components/ui/dialog', () => ({
  Dialog: ({ children, open }: any) => open ? <div data-testid="dialog">{children}</div> : null,
  DialogContent: ({ children }: any) => <div data-testid="dialog-content">{children}</div>,
  DialogTitle: ({ children }: any) => <div data-testid="dialog-title">{children}</div>,
}));

vi.mock('@reach/visually-hidden', () => ({
  VisuallyHidden: ({ children }: any) => <div style={{ display: 'none' }}>{children}</div>,
}));

vi.mock('lucide-react', () => ({
  DollarSign: () => <div data-testid="dollar-sign-icon" />,
}));

const mockUseWalletStore = vi.mocked(useWalletStore);
const mockDepositFunction = vi.mocked(depositFunction);
const mockSaveOrUpdateAgent = vi.mocked(saveOrUpdateAgent);
const mockToast = vi.mocked(toast);

describe('DepositDialog', () => {
  const mockAgent = {
    botId: 'test-bot-id',
    agentName: 'Test Agent', // Fixed: component uses agentName, not name
    number: undefined,
    tradingStatus: 'off',
    deposit: 0,
  };

  const mockOnDepositSuccess = vi.fn();
  const mockOnOpenChange = vi.fn();

  const defaultProps = {
    open: true,
    onOpenChange: mockOnOpenChange,
    agent: mockAgent,
    onDepositSuccess: mockOnDepositSuccess,
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock useWalletStore.getState() method
    const mockWalletState = {
      wallet: createMockWalletAdapter(true, 'test-wallet-address'),
      connection: createMockConnection(),
      driftClient: createMockDriftClient(),
      isConnected: true,
      walletAddress: 'test-wallet-address',
      walletType: null,
      currentChatId: null,
      agent: null,
      chats: [],
      connectWallet: vi.fn(),
      disconnectWallet: vi.fn(),
      setWalletType: vi.fn(),
      resetChatState: vi.fn(),
      setDriftClient: vi.fn(),
    };

    mockUseWalletStore.getState = vi.fn().mockReturnValue(mockWalletState);
    mockUseWalletStore.mockReturnValue(mockWalletState);
  });

  describe('Dialog UI', () => {
    it('should render dialog when open is true', () => {
      render(<DepositDialog {...defaultProps} />);

      expect(screen.getByTestId('dialog')).toBeInTheDocument();
      expect(screen.getByText('Test Agent')).toBeInTheDocument(); // Agent name
      expect(screen.getByText('USDC')).toBeInTheDocument();
      expect(screen.getByText('Deposit')).toBeInTheDocument();
    });

    it('should not render dialog when open is false', () => {
      render(<DepositDialog {...defaultProps} open={false} />);

      expect(screen.queryByTestId('dialog')).not.toBeInTheDocument();
    });

    it('should render agent name in the dialog', () => {
      render(<DepositDialog {...defaultProps} />);

      expect(screen.getByText('Test Agent')).toBeInTheDocument();
    });
  });

  describe('Input Validation', () => {
    it('should accept valid numeric input', () => {
      render(<DepositDialog {...defaultProps} />);

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: '100.50' } });

      expect(input).toHaveValue('100.50');
    });

    it('should handle invalid amount validation', () => {
      render(<DepositDialog {...defaultProps} />);

      const input = screen.getByRole('textbox');
      const depositButton = screen.getByText('Deposit');

      // Test with invalid amount (0)
      fireEvent.change(input, { target: { value: '0' } });
      fireEvent.click(depositButton);

      expect(mockToast).toHaveBeenCalledWith({
        title: 'Invalid Amount',
        description: 'Please enter a valid deposit amount greater than zero.',
        variant: 'destructive',
      });
    });

    it('should handle negative amounts', () => {
      render(<DepositDialog {...defaultProps} />);

      const input = screen.getByRole('textbox');
      const depositButton = screen.getByText('Deposit');

      fireEvent.change(input, { target: { value: '-50' } });
      fireEvent.click(depositButton);

      expect(mockToast).toHaveBeenCalledWith({
        title: 'Invalid Amount',
        description: 'Please enter a valid deposit amount greater than zero.',
        variant: 'destructive',
      });
    });

    it('should handle non-numeric input', () => {
      render(<DepositDialog {...defaultProps} />);

      const input = screen.getByRole('textbox');
      const depositButton = screen.getByText('Deposit');

      fireEvent.change(input, { target: { value: 'abc' } });
      fireEvent.click(depositButton);

      expect(mockToast).toHaveBeenCalledWith({
        title: 'Invalid Amount',
        description: 'Please enter a valid deposit amount greater than zero.',
        variant: 'destructive',
      });
    });
  });

  describe('Deposit Transaction', () => {
    it('should execute successful deposit for new user', async () => {
      mockDepositFunction.mockResolvedValue({
        signature: 'test-signature',
        subAccountId: 1,
      });
      mockSaveOrUpdateAgent.mockResolvedValue({ success: true });

      render(<DepositDialog {...defaultProps} />);
      
      const input = screen.getByRole('textbox');
      const depositButton = screen.getByText('Deposit');
      
      fireEvent.change(input, { target: { value: '100' } });
      fireEvent.click(depositButton);
      
      await waitFor(() => {
        expect(mockDepositFunction).toHaveBeenCalledWith(
          expect.any(Object), // wallet
          100,
          expect.any(Object), // connection
          expect.any(Object), // driftClient
          true, // init
          undefined // subAccountId
        );
      });
      
      expect(mockSaveOrUpdateAgent).toHaveBeenCalledWith(
        expect.objectContaining({
          ...mockAgent,
          number: 1,
          tradingStatus: 'on',
          deposit: 100,
        }),
        mockAgent.botId
      );
      
      expect(mockOnDepositSuccess).toHaveBeenCalled();
      expect(mockToast).toHaveBeenCalledWith({
        title: 'Deposit successful',
        description: 'Your deposit has been successfully processed.',
        variant: 'default',
      });
    });

    it('should execute successful deposit with existing sub-account', async () => {
      const agentWithSubAccount = { ...mockAgent, number: 2 };
      mockDepositFunction.mockResolvedValue({
        signature: 'test-signature',
        subAccountId: 2,
      });

      render(<DepositDialog {...defaultProps} agent={agentWithSubAccount} />);
      
      const input = screen.getByRole('textbox');
      const depositButton = screen.getByText('Deposit');

      fireEvent.change(input, { target: { value: '50' } });
      fireEvent.click(depositButton);

      await waitFor(() => {
        expect(mockDepositFunction).toHaveBeenCalledWith(
          expect.any(Object),
          50,
          expect.any(Object),
          expect.any(Object),
          true,
          undefined
        );
      });
    });

    it('should handle deposit failure', async () => {
      mockDepositFunction.mockRejectedValue(new Error('Transaction failed'));

      render(<DepositDialog {...defaultProps} />);

      const input = screen.getByRole('textbox');
      const depositButton = screen.getByText('Deposit');
      
      fireEvent.change(input, { target: { value: '100' } });
      fireEvent.click(depositButton);
      
      await waitFor(() => {
        expect(mockToast).toHaveBeenCalledWith({
          title: 'Deposit failed',
          description: 'Please try again.',
          variant: 'destructive',
        });
      });
    });

    it('should handle wallet not connected after valid amount', () => {
      const disconnectedState = {
        wallet: null,
        connection: null,
        driftClient: null,
        isConnected: false,
        walletAddress: null,
        walletType: null,
        currentChatId: null,
        agent: null,
        chats: [],
        connectWallet: vi.fn(),
        disconnectWallet: vi.fn(),
        setWalletType: vi.fn(),
        resetChatState: vi.fn(),
        setDriftClient: vi.fn(),
      };

      mockUseWalletStore.getState = vi.fn().mockReturnValue(disconnectedState);

      render(<DepositDialog {...defaultProps} />);

      const input = screen.getByRole('textbox');
      const depositButton = screen.getByText('Deposit');

      // Enter valid amount first, then wallet validation should trigger
      fireEvent.change(input, { target: { value: '100' } });
      fireEvent.click(depositButton);

      expect(mockToast).toHaveBeenCalledWith({
        title: 'Wallet Not Connected',
        description: 'Please reconnect your wallet to proceed with the deposit.',
        variant: 'destructive',
      });
    });

    it('should show loading state during deposit', async () => {
      mockDepositFunction.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 1000)));

      render(<DepositDialog {...defaultProps} />);
      
      const input = screen.getByRole('textbox');
      const depositButton = screen.getByText('Deposit');

      fireEvent.change(input, { target: { value: '100' } });
      fireEvent.click(depositButton);

      expect(screen.getByText('Processing ...')).toBeInTheDocument(); // Fixed: component shows "Processing ..."
      expect(depositButton).toBeDisabled();
    });
  });

  describe('Agent State Updates', () => {
    it('should update agent with sub-account ID after successful deposit', async () => {
      mockDepositFunction.mockResolvedValue({
        signature: 'test-signature',
        subAccountId: 3,
      });
      mockSaveOrUpdateAgent.mockResolvedValue({ success: true });

      render(<DepositDialog {...defaultProps} />);

      const input = screen.getByRole('textbox');
      const depositButton = screen.getByText('Deposit');

      fireEvent.change(input, { target: { value: '200' } });
      fireEvent.click(depositButton);

      await waitFor(() => {
        expect(mockSaveOrUpdateAgent).toHaveBeenCalledWith(
          expect.objectContaining({
            number: 3,
            tradingStatus: 'on',
            deposit: 200,
          }),
          mockAgent.botId
        );
      });
    });

    it('should call onDepositSuccess with updated agent', async () => {
      mockDepositFunction.mockResolvedValue({
        signature: 'test-signature',
        subAccountId: 1,
      });
      mockSaveOrUpdateAgent.mockResolvedValue({ success: true });

      render(<DepositDialog {...defaultProps} />);

      const input = screen.getByRole('textbox');
      const depositButton = screen.getByText('Deposit');
      
      fireEvent.change(input, { target: { value: '150' } });
      fireEvent.click(depositButton);
      
      await waitFor(() => {
        expect(mockOnDepositSuccess).toHaveBeenCalledWith(
          expect.objectContaining({
            ...mockAgent,
            number: 1,
            tradingStatus: 'on',
            deposit: 150,
          })
        );
      });
    });
  });
});
