import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ManageFundsDialog from '@/components/ManageFundsDialog';

// Mock the external dependencies
vi.mock('@/lib/trade/drift', () => ({
  depositFunction: vi.fn(),
  withdrawFunction: vi.fn(),
}));

vi.mock('@/lib/position', () => ({
  getPosition: vi.fn(),
}));

vi.mock('@/hooks/use-toast', () => ({
  toast: vi.fn(),
}));

// Mock the wallet store
vi.mock('@/store/walletStore', () => ({
  useWalletStore: vi.fn(),
}));

// Mock the chat context
vi.mock('@/context/ChatContext', () => ({
  useChatContext: vi.fn(),
}));

// Mock UI components to avoid portal issues
vi.mock('@/components/ui/dialog', () => ({
  Dialog: ({ children, open }: any) => open ? <div data-testid="dialog">{children}</div> : null,
  DialogContent: ({ children }: any) => <div data-testid="dialog-content">{children}</div>,
}));

vi.mock('lucide-react', () => ({
  DollarSign: () => <div data-testid="dollar-sign-icon" />,
}));

// Import the mocked functions
import { depositFunction, withdrawFunction } from '@/lib/trade/drift';
import { getPosition } from '@/lib/position';
import { toast } from '@/hooks/use-toast';
import { useWalletStore } from '@/store/walletStore';
import { useChatContext } from '@/context/ChatContext';

const mockDepositFunction = vi.mocked(depositFunction);
const mockWithdrawFunction = vi.mocked(withdrawFunction);
const mockGetPosition = vi.mocked(getPosition);
const mockToast = vi.mocked(toast);
const mockUseWalletStore = vi.mocked(useWalletStore);
const mockUseChatContext = vi.mocked(useChatContext);

describe('ManageFundsDialog', () => {
  const mockAgent = {
    botId: 'test-bot-id',
    agentName: 'Test Agent',
    assetPair: 'SOL/USD',
    number: 1,
    tradingStatus: 'on',
    deposit: 100,
  };

  const mockPosition = {
    positionSize: '1.5',
    pnl: '25.50',
    positionValueUsd: '150.75',
    portfolioValue: '1250.00',
  };

  const mockOnOpenChange = vi.fn();

  const defaultProps = {
    open: true,
    onOpenChange: mockOnOpenChange,
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock wallet store with connected state
    mockUseWalletStore.mockReturnValue({
      wallet: { connected: true },
      connection: { endpoint: 'mock' },
      driftClient: { mock: true },
      isConnected: true,
      walletAddress: 'test-wallet-address',
    });

    // Mock chat context with agent
    mockUseChatContext.mockReturnValue({
      agent: mockAgent,
      setAgent: vi.fn(),
      currentChatId: null,
      setCurrentChatId: vi.fn(),
      chats: [],
      setChats: vi.fn(),
      messages: [],
      setMessages: vi.fn(),
      loading: false,
    });

    // Default position data
    mockGetPosition.mockResolvedValue(mockPosition);
  });

  describe('Dialog UI', () => {
    it('should render dialog when open is true', async () => {
      render(<ManageFundsDialog {...defaultProps} />);

      expect(screen.getByTestId('dialog')).toBeInTheDocument();
      expect(screen.getByText('Manage Funds')).toBeInTheDocument();

      const input = screen.getByRole('textbox');
      expect(input).toBeInTheDocument();
      expect(screen.getByText('Deposit')).toBeInTheDocument();
      expect(screen.getByText('Withdraw')).toBeInTheDocument();
    });

    it('should not render dialog when open is false', () => {
      render(<ManageFundsDialog {...defaultProps} open={false} />);

      expect(screen.queryByTestId('dialog')).not.toBeInTheDocument();
    });

    it('should display current holdings', async () => {
      render(<ManageFundsDialog {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByText('Current Holdings: 1250.00 USD')).toBeInTheDocument();
      });
    });

    it('should show loading state for holdings initially', () => {
      mockGetPosition.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 1000)));
      
      render(<ManageFundsDialog {...defaultProps} />);
      
      expect(screen.getByText('Current Holdings: Loading... USD')).toBeInTheDocument();
    });

    it('should fetch position data when dialog opens', async () => {
      render(<ManageFundsDialog {...defaultProps} />);
      
      await waitFor(() => {
        expect(mockGetPosition).toHaveBeenCalledWith(mockAgent.botId);
      });
    });
  });

  describe('Amount Input Validation', () => {
    it('should accept valid numeric input', () => {
      render(<ManageFundsDialog {...defaultProps} />);

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: '100.50' } });

      expect(input).toHaveValue('100.50');
    });

    it('should reject negative amounts', () => {
      render(<ManageFundsDialog {...defaultProps} />);

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: '-50' } });

      expect(input).toHaveValue('');
    });

    it('should reject non-numeric input', () => {
      render(<ManageFundsDialog {...defaultProps} />);

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: 'abc' } });

      expect(input).toHaveValue('');
    });

    it('should validate withdrawal amount against available balance', async () => {
      render(<ManageFundsDialog {...defaultProps} />);

      await waitFor(() => {
        expect(mockGetPosition).toHaveBeenCalled();
      });

      const input = screen.getByRole('textbox');
      const withdrawButton = screen.getByText('Withdraw');

      fireEvent.change(input, { target: { value: '2000' } }); // More than portfolio value
      fireEvent.click(withdrawButton);

      expect(mockToast).toHaveBeenCalledWith({
        title: 'Insufficient funds',
        description: 'Withdrawal amount exceeds available balance',
        variant: 'destructive',
      });
    });
  });

  describe('Deposit Functionality', () => {
    it('should execute successful deposit', async () => {
      mockDepositFunction.mockResolvedValue({
        signature: 'test-deposit-signature',
        subAccountId: 1,
      });

      render(<ManageFundsDialog {...defaultProps} />);

      const input = screen.getByRole('textbox');
      const depositButton = screen.getByText('Deposit');

      fireEvent.change(input, { target: { value: '100' } });
      fireEvent.click(depositButton);

      await waitFor(() => {
        expect(mockDepositFunction).toHaveBeenCalledWith(
          expect.any(Object), // wallet
          100,
          expect.any(Object), // connection
          expect.any(Object), // driftClient
          false, // init
          mockAgent.number // subAccountId
        );
      });
    });

    it('should refresh position data after successful deposit', async () => {
      mockDepositFunction.mockResolvedValue({
        signature: 'test-deposit-signature',
        subAccountId: 1,
      });

      render(<ManageFundsDialog {...defaultProps} />);

      const input = screen.getByRole('textbox');
      const depositButton = screen.getByText('Deposit');

      fireEvent.change(input, { target: { value: '100' } });
      fireEvent.click(depositButton);

      await waitFor(() => {
        expect(mockGetPosition).toHaveBeenCalledTimes(2); // Initial load + refresh after deposit
      });
    });

    it('should handle deposit failure when wallet not connected', () => {
      mockUseWalletStore.mockReturnValue({
        wallet: null,
        connection: null,
        driftClient: null,
        isConnected: false,
        walletAddress: null,
        walletType: null,
        currentChatId: null,
        agent: null,
        chats: [],
        connectWallet: vi.fn(),
        disconnectWallet: vi.fn(),
        setWalletType: vi.fn(),
        resetChatState: vi.fn(),
        setDriftClient: vi.fn(),
      });

      render(<ManageFundsDialog {...defaultProps} />);
      
      const depositButton = screen.getByText('Deposit');
      fireEvent.click(depositButton);
      
      expect(mockToast).toHaveBeenCalledWith({
        title: 'cannot deposit',
        description: 'please reconnect your wallet',
        variant: 'destructive',
      });
    });
  });

  describe('Withdraw Functionality', () => {
    it('should execute successful withdrawal', async () => {
      mockWithdrawFunction.mockResolvedValue({
        signature: 'test-withdraw-signature',
      });

      render(<ManageFundsDialog {...defaultProps} />);

      await waitFor(() => {
        expect(mockGetPosition).toHaveBeenCalled();
      });

      const input = screen.getByRole('textbox');
      const withdrawButton = screen.getByText('Withdraw');

      fireEvent.change(input, { target: { value: '50' } });
      fireEvent.click(withdrawButton);

      await waitFor(() => {
        expect(mockWithdrawFunction).toHaveBeenCalledWith(
          expect.any(Object), // wallet
          50,
          expect.any(Object), // connection
          expect.any(Object), // driftClient
          true, // init
          mockAgent.number // subAccountId
        );
      });
    });

    it('should refresh position data after successful withdrawal', async () => {
      mockWithdrawFunction.mockResolvedValue({
        signature: 'test-withdraw-signature',
      });

      render(<ManageFundsDialog {...defaultProps} />);

      await waitFor(() => {
        expect(mockGetPosition).toHaveBeenCalled();
      });

      const input = screen.getByRole('textbox');
      const withdrawButton = screen.getByText('Withdraw');

      fireEvent.change(input, { target: { value: '50' } });
      fireEvent.click(withdrawButton);

      await waitFor(() => {
        expect(mockGetPosition).toHaveBeenCalledTimes(2); // Initial load + refresh after withdrawal
      });
    });

    it('should handle withdrawal failure when wallet not connected', () => {
      mockUseWalletStore.mockReturnValue({
        wallet: null,
        connection: null,
        driftClient: null,
        isConnected: false,
        walletAddress: null,
        walletType: null,
        currentChatId: null,
        agent: null,
        chats: [],
        connectWallet: vi.fn(),
        disconnectWallet: vi.fn(),
        setWalletType: vi.fn(),
        resetChatState: vi.fn(),
        setDriftClient: vi.fn(),
      });

      render(<ManageFundsDialog {...defaultProps} />);
      
      const withdrawButton = screen.getByText('Withdraw');
      fireEvent.click(withdrawButton);
      
      expect(mockToast).toHaveBeenCalledWith({
        title: 'cannot withdraw',
        description: 'please reconnect your wallet',
        variant: 'destructive',
      });
    });

    it('should handle withdrawal with insufficient funds', async () => {
      render(<ManageFundsDialog {...defaultProps} />);

      await waitFor(() => {
        expect(mockGetPosition).toHaveBeenCalled();
      });

      const input = screen.getByRole('textbox');
      const withdrawButton = screen.getByText('Withdraw');

      // Try to withdraw more than available
      fireEvent.change(input, { target: { value: '2000' } });
      fireEvent.click(withdrawButton);

      expect(mockToast).toHaveBeenCalledWith({
        title: 'Insufficient funds',
        description: 'Withdrawal amount exceeds available balance',
        variant: 'destructive',
      });

      expect(mockWithdrawFunction).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should handle position fetch failure', async () => {
      mockGetPosition.mockRejectedValue(new Error('API Error'));

      render(<ManageFundsDialog {...defaultProps} />);
      
      await waitFor(() => {
        expect(screen.getByText('Current Holdings: 0 USD')).toBeInTheDocument();
      });
    });

    it('should handle missing agent', () => {
      mockUseChatContext.mockReturnValue({
        agent: null,
        setAgent: vi.fn(),
        currentChatId: null,
        setCurrentChatId: vi.fn(),
        chats: [],
        setChats: vi.fn(),
        messages: [],
        setMessages: vi.fn(),
        loading: false,
      });

      render(<ManageFundsDialog {...defaultProps} />);

      expect(mockGetPosition).not.toHaveBeenCalled();
      expect(screen.getByText('Current Holdings: Loading... USD')).toBeInTheDocument();
    });
  });
});
