import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';

// Simple mocks
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({ toast: vi.fn() }),
}));

vi.mock('@/store/walletStore', () => ({
  useWalletStore: vi.fn(() => ({
    isConnected: false,
    walletAddress: null,
    connectWallet: vi.fn(),
    disconnectWallet: vi.fn(),
  })),
}));

vi.mock('@/utils/phantomWallet', () => ({
  isPhantomInstalled: vi.fn(() => true),
  connectPhantomWallet: vi.fn(),
  disconnectPhantomWallet: vi.fn(),
}));

vi.mock('@/lib/trade/drift', () => ({
  fetchUserPositions: vi.fn(() => Promise.resolve([])),
  fetchUserActivities: vi.fn(() => Promise.resolve({})),
}));

vi.mock('@/components/PhantomPrompt', () => ({
  default: () => <div data-testid="phantom-prompt">Phantom Prompt</div>,
}));

import WalletConnect from '../../../components/WalletConnect';

describe('WalletConnect Simple Test', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render wallet connect button', () => {
    render(<WalletConnect />);
    
    expect(screen.getByText('Connect Phantom')).toBeInTheDocument();
  });

  it('should render install phantom when not available', async () => {
    const { isPhantomInstalled } = await import('@/utils/phantomWallet');
    vi.mocked(isPhantomInstalled).mockReturnValue(false);

    render(<WalletConnect />);
    
    expect(screen.getByText('Install Phantom')).toBeInTheDocument();
  });
});
