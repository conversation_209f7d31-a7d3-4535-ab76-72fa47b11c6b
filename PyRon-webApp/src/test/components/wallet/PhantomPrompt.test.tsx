import { render, screen, fireEvent, act } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import PhantomPrompt from '../../../components/PhantomPrompt';
import { setupWalletMocks } from '../../utils/mockWallet';

describe('PhantomPrompt Component - RED PHASE (Failing Tests)', () => {
  const mockOnClose = vi.fn();

  beforeEach(() => {
    setupWalletMocks();
    vi.clearAllMocks();
  });

  describe('Component Rendering', () => {
    it('should render the phantom prompt dialog', () => {
      render(<PhantomPrompt onClose={mockOnClose} />);

      expect(screen.getByText('Phantom Wallet Required')).toBeInTheDocument();
      expect(screen.getByText('Connect with Phantom wallet to access your personal chats and trading features.')).toBeInTheDocument();
    });

    it('should display the wallet icon', () => {
      render(<PhantomPrompt onClose={mockOnClose} />);

      // Check for wallet icon (Lucide icon)
      const walletIcon = document.querySelector('.lucide-wallet');
      expect(walletIcon).toBeInTheDocument();
    });

    it('should show information about why Phantom is required', () => {
      render(<PhantomPrompt onClose={mockOnClose} />);

      expect(screen.getByText('Why Phantom?')).toBeInTheDocument();
      expect(screen.getByText(/Phantom is a secure crypto wallet built for Solana/)).toBeInTheDocument();
    });

    it('should display install and close buttons', () => {
      render(<PhantomPrompt onClose={mockOnClose} />);

      expect(screen.getByText('Install Phantom Wallet')).toBeInTheDocument();
      expect(screen.getByText('Maybe Later')).toBeInTheDocument();
    });

    it('should display close button (×) in top right corner', () => {
      render(<PhantomPrompt onClose={mockOnClose} />);

      const closeButton = screen.getByText('×');
      expect(closeButton).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('should call onClose when close button (×) is clicked', () => {
      render(<PhantomPrompt onClose={mockOnClose} />);

      const closeButton = screen.getByText('×');
      fireEvent.click(closeButton);

      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    it('should call onClose when "Maybe Later" button is clicked', () => {
      render(<PhantomPrompt onClose={mockOnClose} />);

      const maybeLaterButton = screen.getByText('Maybe Later');
      fireEvent.click(maybeLaterButton);

      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    it('should open Phantom download page when "Install Phantom Wallet" is clicked', () => {
      render(<PhantomPrompt onClose={mockOnClose} />);

      const installButton = screen.getByText('Install Phantom Wallet');
      fireEvent.click(installButton);

      expect(window.open).toHaveBeenCalledWith('https://phantom.app/download', '_blank');
    });

    it('should not call onClose when install button is clicked', () => {
      render(<PhantomPrompt onClose={mockOnClose} />);

      const installButton = screen.getByText('Install Phantom Wallet');
      fireEvent.click(installButton);

      expect(mockOnClose).not.toHaveBeenCalled();
    });
  });

  describe('Styling and Layout', () => {
    it('should have proper modal overlay styling', () => {
      render(<PhantomPrompt onClose={mockOnClose} />);

      const overlay = document.querySelector('.fixed.inset-0.bg-black\\/80');
      expect(overlay).toBeInTheDocument();
    });

    it('should center the modal content', () => {
      render(<PhantomPrompt onClose={mockOnClose} />);

      const overlay = document.querySelector('.flex.items-center.justify-center');
      expect(overlay).toBeInTheDocument();
    });

    it('should have proper z-index for modal', () => {
      render(<PhantomPrompt onClose={mockOnClose} />);

      const overlay = document.querySelector('.z-50');
      expect(overlay).toBeInTheDocument();
    });

    it('should style the install button with gold theme', () => {
      render(<PhantomPrompt onClose={mockOnClose} />);

      const installButton = screen.getByText('Install Phantom Wallet');
      expect(installButton).toHaveClass('bg-gold-light');
    });

    it('should style the maybe later button as outline variant', () => {
      render(<PhantomPrompt onClose={mockOnClose} />);

      const maybeLaterButton = screen.getByText('Maybe Later');
      expect(maybeLaterButton.closest('button')).toHaveClass('border-gray-700');
    });
  });

  describe('Accessibility', () => {
    it('should have proper button roles', () => {
      render(<PhantomPrompt onClose={mockOnClose} />);

      const buttons = screen.getAllByRole('button');
      expect(buttons).toHaveLength(3); // Close (×), Install, Maybe Later
    });

    it('should have proper heading structure', () => {
      render(<PhantomPrompt onClose={mockOnClose} />);

      const heading = screen.getByRole('heading', { level: 2 });
      expect(heading).toHaveTextContent('Phantom Wallet Required');
    });

    it('should support keyboard navigation', () => {
      render(<PhantomPrompt onClose={mockOnClose} />);

      const installButton = screen.getByText('Install Phantom Wallet');
      
      // Focus the button
      act(() => {
        installButton.focus();
      });

      expect(document.activeElement).toBe(installButton);
    });

    it('should handle escape key to close modal', () => {
      render(<PhantomPrompt onClose={mockOnClose} />);

      // Simulate escape key press
      fireEvent.keyDown(document, { key: 'Escape', code: 'Escape' });

      // The component may trigger multiple escape events due to event bubbling
      // We just need to verify that onClose was called at least once
      expect(mockOnClose).toHaveBeenCalled();
    });
  });

  describe('Content Validation', () => {
    it('should display correct Phantom download URL', () => {
      render(<PhantomPrompt onClose={mockOnClose} />);

      const installButton = screen.getByText('Install Phantom Wallet');
      fireEvent.click(installButton);

      expect(window.open).toHaveBeenCalledWith('https://phantom.app/download', '_blank');
    });

    it('should have informative description text', () => {
      render(<PhantomPrompt onClose={mockOnClose} />);

      const description = screen.getByText(/Connect with Phantom wallet to access your personal chats and trading features/);
      expect(description).toBeInTheDocument();
    });

    it('should explain Phantom wallet benefits', () => {
      render(<PhantomPrompt onClose={mockOnClose} />);

      const benefits = screen.getByText(/allows you to safely store assets, interact with dApps, and access your personal data/);
      expect(benefits).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should handle window.open failure gracefully', () => {
      // Mock window.open to return null (popup blocked)
      vi.mocked(window.open).mockReturnValue(null);

      render(<PhantomPrompt onClose={mockOnClose} />);

      const installButton = screen.getByText('Install Phantom Wallet');

      // Should not throw an error when clicked
      expect(() => {
        fireEvent.click(installButton);
      }).not.toThrow();

      // Verify window.open was called
      expect(window.open).toHaveBeenCalledWith(
        'https://phantom.app/download',
        '_blank'
      );
    });

    it('should handle missing onClose prop gracefully', () => {
      // This test will fail initially as we need to add proper prop validation
      expect(() => {
        render(<PhantomPrompt onClose={undefined as any} />);
      }).not.toThrow();
    });
  });
});
