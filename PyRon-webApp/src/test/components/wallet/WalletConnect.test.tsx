import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock dependencies using vi.mock with factory functions
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({ toast: vi.fn() }),
}));

vi.mock('@/store/walletStore', () => ({
  useWalletStore: vi.fn(),
}));

vi.mock('@/utils/phantomWallet', () => ({
  isPhantomInstalled: vi.fn(),
  connectPhantomWallet: vi.fn(),
  disconnectPhantomWallet: vi.fn(),
}));

vi.mock('@/lib/trade/drift', () => ({
  fetchUserPositions: vi.fn(),
  fetchUserActivities: vi.fn(),
}));

vi.mock('@/components/PhantomPrompt', () => ({
  default: ({ onClose }: { onClose: () => void }) => (
    <div data-testid="phantom-prompt">
      <button onClick={onClose} data-testid="close-phantom-prompt">
        Close Phantom Prompt
      </button>
    </div>
  ),
}));

// Import the component and test utilities
import WalletConnect from '../../../components/WalletConnect';
import {
  createMockWalletStore,
  mockWalletData,
  mockPositions,
  mockActivities,
  setupWalletMocks
} from '../../utils/mockWallet';

describe('WalletConnect Component - RED PHASE (Failing Tests)', () => {
  beforeEach(() => {
    setupWalletMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Phantom Wallet Detection', () => {
    it('should detect when Phantom wallet is installed', async () => {
      const { isPhantomInstalled } = await import('@/utils/phantomWallet');
      const { useWalletStore } = await import('@/store/walletStore');

      vi.mocked(isPhantomInstalled).mockReturnValue(true);
      vi.mocked(useWalletStore).mockReturnValue(createMockWalletStore());

      await act(async () => {
        render(<WalletConnect />);
      });

      expect(screen.getByText('Connect Phantom')).toBeInTheDocument();
    });

    it('should detect when Phantom wallet is not installed', async () => {
      const { isPhantomInstalled } = await import('@/utils/phantomWallet');
      const { useWalletStore } = await import('@/store/walletStore');

      vi.mocked(isPhantomInstalled).mockReturnValue(false);
      vi.mocked(useWalletStore).mockReturnValue(createMockWalletStore());

      await act(async () => {
        render(<WalletConnect />);
      });

      expect(screen.getByText('Install Phantom')).toBeInTheDocument();
    });

    it('should show Phantom installation prompt when wallet not installed', async () => {
      const { isPhantomInstalled } = await import('@/utils/phantomWallet');
      const { useWalletStore } = await import('@/store/walletStore');

      vi.mocked(isPhantomInstalled).mockReturnValue(false);
      vi.mocked(useWalletStore).mockReturnValue(createMockWalletStore());

      await act(async () => {
        render(<WalletConnect />);
      });

      const connectButton = screen.getByText('Install Phantom');
      await act(async () => {
        fireEvent.click(connectButton);
      });

      expect(screen.getByTestId('phantom-prompt')).toBeInTheDocument();
    });
  });

  describe('Wallet Connection Flow', () => {
    it('should successfully connect to Phantom wallet', async () => {
      const { isPhantomInstalled, connectPhantomWallet } = await import('@/utils/phantomWallet');
      const { useWalletStore } = await import('@/store/walletStore');
      const { useToast } = await import('@/hooks/use-toast');

      vi.mocked(isPhantomInstalled).mockReturnValue(true);
      vi.mocked(connectPhantomWallet).mockResolvedValue(mockWalletData);

      const mockStore = createMockWalletStore();
      vi.mocked(useWalletStore).mockReturnValue(mockStore);

      await act(async () => {
        render(<WalletConnect />);
      });

      const connectButton = screen.getByText('Connect Phantom');
      await act(async () => {
        fireEvent.click(connectButton);
      });

      await waitFor(() => {
        expect(connectPhantomWallet).toHaveBeenCalled();
        expect(mockStore.connectWallet).toHaveBeenCalledWith(mockWalletData.address);
      });
    });

    it('should handle wallet connection failure', async () => {
      const { isPhantomInstalled, connectPhantomWallet } = await import('@/utils/phantomWallet');
      const { useWalletStore } = await import('@/store/walletStore');
      const { useToast } = await import('@/hooks/use-toast');

      vi.mocked(isPhantomInstalled).mockReturnValue(true);
      vi.mocked(connectPhantomWallet).mockResolvedValue(null);
      vi.mocked(useWalletStore).mockReturnValue(createMockWalletStore());

      await act(async () => {
        render(<WalletConnect />);
      });

      const connectButton = screen.getByText('Connect Phantom');
      await act(async () => {
        fireEvent.click(connectButton);
      });

      await waitFor(() => {
        expect(connectPhantomWallet).toHaveBeenCalled();
      });
    });

    it('should store wallet display address in localStorage on successful connection', async () => {
      const { isPhantomInstalled, connectPhantomWallet } = await import('@/utils/phantomWallet');
      const { useWalletStore } = await import('@/store/walletStore');

      vi.mocked(isPhantomInstalled).mockReturnValue(true);
      vi.mocked(connectPhantomWallet).mockResolvedValue(mockWalletData);

      const mockStore = createMockWalletStore();
      vi.mocked(useWalletStore).mockReturnValue(mockStore);

      await act(async () => {
        render(<WalletConnect />);
      });

      const connectButton = screen.getByText('Connect Phantom');
      await act(async () => {
        fireEvent.click(connectButton);
      });

      await waitFor(() => {
        expect(localStorage.setItem).toHaveBeenCalledWith('wallet_display', mockWalletData.display);
      });
    });
  });

  describe('Connected Wallet UI', () => {
    it('should display wallet address when connected', async () => {
      const { useWalletStore } = await import('@/store/walletStore');

      // Mock localStorage to return the wallet display address
      vi.mocked(localStorage.getItem).mockReturnValue(mockWalletData.display);

      const mockStore = createMockWalletStore({
        isConnected: true,
        walletAddress: mockWalletData.address,
      });
      vi.mocked(useWalletStore).mockReturnValue(mockStore);

      await act(async () => {
        render(<WalletConnect />);
      });

      // Should display shortened wallet address
      expect(screen.getByText(mockWalletData.display)).toBeInTheDocument();
    });
  });
});
