import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import ApiKeyConfigButton from '@/components/sidebar/ApiKeyConfigButton';

describe('ApiKeyConfigButton', () => {
  it('should render the API key config button', () => {
    const mockOnClick = vi.fn();

    render(<ApiKeyConfigButton onClick={mockOnClick} />);

    const button = screen.getByRole('button', { name: /api key/i });
    expect(button).toBeInTheDocument();
  });

  it('should display settings icon', () => {
    const mockOnClick = vi.fn();

    render(<ApiKeyConfigButton onClick={mockOnClick} />);

    const settingsIcon = screen.getByTestId('settings-icon');
    expect(settingsIcon).toBeInTheDocument();
  });

  it('should call onClick when button is clicked', () => {
    const mockOnClick = vi.fn();

    render(<ApiKeyConfigButton onClick={mockOnClick} />);

    const button = screen.getByRole('button', { name: /api key/i });
    fireEvent.click(button);

    expect(mockOnClick).toHaveBeenCalledTimes(1);
  });

  it('should have proper styling classes', () => {
    const mockOnClick = vi.fn();

    render(<ApiKeyConfigButton onClick={mockOnClick} />);

    const button = screen.getByRole('button', { name: /api key/i });
    expect(button).toHaveClass('w-full');
    expect(button).toHaveClass('bg-[#222222]');
  });
});
