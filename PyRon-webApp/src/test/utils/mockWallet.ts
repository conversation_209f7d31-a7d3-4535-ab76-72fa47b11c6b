import { vi } from 'vitest';

// Mock PublicKey class
export class MockPublicKey {
  private _key: string;

  constructor(key: string) {
    this._key = key;
  }

  toString(): string {
    return this._key;
  }

  toBase58(): string {
    return this._key;
  }
}

// Mock wallet adapter
export const createMockWalletAdapter = (connected = false, publicKey?: string) => ({
  connected,
  publicKey: publicKey ? new MockPublicKey(publicKey) : null,
  connect: vi.fn().mockResolvedValue(undefined),
  disconnect: vi.fn().mockResolvedValue(undefined),
  on: vi.fn(),
  off: vi.fn(),
  removeAllListeners: vi.fn(),
  sendTransaction: vi.fn().mockResolvedValue('mock-signature'),
  signTransaction: vi.fn(),
  signAllTransactions: vi.fn(),
});

// Mock Connection class
export const createMockConnection = () => ({
  getLatestBlockhash: vi.fn().mockResolvedValue({
    blockhash: 'mock-blockhash',
    lastValidBlockHeight: 123456,
  }),
  sendTransaction: vi.fn().mockResolvedValue('mock-signature'),
  confirmTransaction: vi.fn().mockResolvedValue({ value: { err: null } }),
  getAccountInfo: vi.fn().mockResolvedValue(null),
});

// Mock DriftClient
export const createMockDriftClient = () => ({
  subscribe: vi.fn().mockResolvedValue(undefined),
  unsubscribe: vi.fn().mockResolvedValue(undefined),
  convertToSpotPrecision: vi.fn(),
  getAssociatedTokenAccount: vi.fn(),
  getUserAccount: vi.fn(),
  getUser: vi.fn(),
});

// Mock wallet data for testing
export const mockWalletData = {
  address: 'HN7cABqLq46Es1jh92dQQisAq662SmxELLLsHHe4YWrH',
  display: 'HN7c...YWrH',
};

// Mock wallet store state
export const createMockWalletStore = (overrides = {}) => ({
  isConnected: false,
  wallet: null,
  walletType: null,
  connection: null,
  driftClient: null,
  walletAddress: null,
  currentChatId: null,
  agent: null,
  chats: [],
  connectWallet: vi.fn(),
  disconnectWallet: vi.fn(),
  setWalletType: vi.fn(),
  resetChatState: vi.fn(),
  setDriftClient: vi.fn(),
  ...overrides,
});

// Mock positions data
export const mockPositions = [
  { market: 'SOL-PERP', position: 1.5 },
  { market: 'BTC-PERP', position: 0.1 },
];

// Mock activities data
export const mockActivities = {
  'agent-1': {
    agentName: 'Test Agent',
    orders: {
      records: [
        {
          ts: **********, // 2022-01-01
          quoteAssetAmountFilled: 100,
          takerFee: 0.1,
          takerOrderDirection: 'long',
          txSig: 'test-signature-1',
          symbol: 'SOL-PERP',
          baseAssetAmountFilled: 1.5,
        },
      ],
    },
  },
};

// Mock toast function
export const mockToast = vi.fn();

// Mock localStorage
export const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

// Mock fetch for API calls
export const mockFetch = vi.fn();

// Mock auth service functions
export const mockAuthService = {
  authenticateWallet: vi.fn().mockResolvedValue(true),
  logout: vi.fn().mockResolvedValue(undefined),
};

// Setup global mocks for wallet testing
export const setupWalletMocks = () => {
  // Mock localStorage
  Object.defineProperty(window, 'localStorage', {
    value: mockLocalStorage,
    writable: true,
  });

  // Mock window.open
  Object.defineProperty(window, 'open', {
    value: vi.fn(),
    writable: true,
  });

  // Mock fetch globally
  global.fetch = mockFetch.mockResolvedValue({
    ok: true,
    json: vi.fn().mockResolvedValue({ success: true }),
    text: vi.fn().mockResolvedValue(''),
  });

  // Mock environment variables
  vi.stubEnv('VITE_RPC_URL', 'https://api.mainnet-beta.solana.com');

  // Reset all mocks
  vi.clearAllMocks();
};

// Test data for different scenarios
export const testWalletAddresses = {
  valid: 'HN7cABqLq46Es1jh92dQQisAq662SmxELLLsHHe4YWrH',
  invalid: 'invalid-address',
  empty: '',
};

export const testDisplayAddresses = {
  valid: 'HN7c...YWrH',
  short: 'abc...xyz',
};
