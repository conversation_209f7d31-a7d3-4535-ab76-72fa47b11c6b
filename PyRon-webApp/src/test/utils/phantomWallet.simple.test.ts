import { describe, it, expect, vi, beforeEach } from 'vitest';

// Mock PhantomWalletAdapter
vi.mock('@solana/wallet-adapter-phantom', () => ({
  PhantomWalletAdapter: vi.fn(),
}));

import {
  isPhantomInstalled,
  connectPhantomWallet,
  disconnectPhantomWallet
} from '../../utils/phantomWallet';

describe('PhantomWallet Utils - Simple Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('isPhantomInstalled', () => {
    it('should return true when Phantom wallet is installed', async () => {
      const { PhantomWalletAdapter } = await import('@solana/wallet-adapter-phantom');
      vi.mocked(PhantomWalletAdapter).mockImplementation(() => ({} as any));

      const result = isPhantomInstalled();

      expect(result).toBe(true);
      expect(PhantomWalletAdapter).toHaveBeenCalled();
    });

    it('should return false when Phantom wallet is not installed', async () => {
      const { PhantomWalletAdapter } = await import('@solana/wallet-adapter-phantom');
      vi.mocked(PhantomWalletAdapter).mockImplementation(() => {
        throw new Error('Phantom not found');
      });

      const result = isPhantomInstalled();

      expect(result).toBe(false);
    });

    it('should handle adapter instantiation errors gracefully', async () => {
      const { PhantomWalletAdapter } = await import('@solana/wallet-adapter-phantom');
      vi.mocked(PhantomWalletAdapter).mockImplementation(() => {
        throw new Error('Unexpected error');
      });

      expect(() => isPhantomInstalled()).not.toThrow();
      expect(isPhantomInstalled()).toBe(false);
    });
  });

  describe('connectPhantomWallet', () => {
    it('should successfully connect to Phantom wallet', async () => {
      const { PhantomWalletAdapter } = await import('@solana/wallet-adapter-phantom');
      const mockAdapter = {
        connected: false,
        publicKey: {
          toString: () => 'HN7cABqLq46Es1jh92dQQisAq662SmxELLLsHHe4YWrH'
        },
        connect: vi.fn().mockResolvedValue(undefined),
      };

      vi.mocked(PhantomWalletAdapter).mockImplementation(() => mockAdapter as any);

      const result = await connectPhantomWallet();

      expect(mockAdapter.connect).toHaveBeenCalled();
      expect(result).toEqual({
        address: 'HN7cABqLq46Es1jh92dQQisAq662SmxELLLsHHe4YWrH',
        display: 'HN7c...YWrH',
      });
    });

    it('should not call connect if wallet is already connected', async () => {
      const { PhantomWalletAdapter } = await import('@solana/wallet-adapter-phantom');
      const mockAdapter = {
        connected: true,
        publicKey: {
          toString: () => 'HN7cABqLq46Es1jh92dQQisAq662SmxELLLsHHe4YWrH'
        },
        connect: vi.fn(),
      };

      vi.mocked(PhantomWalletAdapter).mockImplementation(() => mockAdapter as any);

      const result = await connectPhantomWallet();

      expect(mockAdapter.connect).not.toHaveBeenCalled();
      expect(result).toEqual({
        address: 'HN7cABqLq46Es1jh92dQQisAq662SmxELLLsHHe4YWrH',
        display: 'HN7c...YWrH',
      });
    });

    it('should return null when connection fails', async () => {
      const { PhantomWalletAdapter } = await import('@solana/wallet-adapter-phantom');
      const mockAdapter = {
        connected: false,
        publicKey: null,
        connect: vi.fn().mockRejectedValue(new Error('Connection failed')),
      };

      vi.mocked(PhantomWalletAdapter).mockImplementation(() => mockAdapter as any);

      const result = await connectPhantomWallet();

      expect(result).toBeNull();
    });

    it('should return null when no public key is available', async () => {
      const { PhantomWalletAdapter } = await import('@solana/wallet-adapter-phantom');
      const mockAdapter = {
        connected: false,
        publicKey: null,
        connect: vi.fn().mockResolvedValue(undefined),
      };

      vi.mocked(PhantomWalletAdapter).mockImplementation(() => mockAdapter as any);

      const result = await connectPhantomWallet();

      expect(result).toBeNull();
    });

    it('should format wallet address correctly for display', async () => {
      const { PhantomWalletAdapter } = await import('@solana/wallet-adapter-phantom');
      const testAddress = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz123456789';
      const mockAdapter = {
        connected: false,
        publicKey: {
          toString: () => testAddress
        },
        connect: vi.fn().mockResolvedValue(undefined),
      };

      vi.mocked(PhantomWalletAdapter).mockImplementation(() => mockAdapter as any);

      const result = await connectPhantomWallet();

      expect(result?.display).toBe('ABCD...6789');
      expect(result?.address).toBe(testAddress);
    });

    it('should handle adapter instantiation errors', async () => {
      const { PhantomWalletAdapter } = await import('@solana/wallet-adapter-phantom');
      vi.mocked(PhantomWalletAdapter).mockImplementation(() => {
        throw new Error('Adapter creation failed');
      });

      const result = await connectPhantomWallet();

      expect(result).toBeNull();
    });
  });

  describe('disconnectPhantomWallet', () => {
    it('should successfully disconnect from Phantom wallet', async () => {
      const { PhantomWalletAdapter } = await import('@solana/wallet-adapter-phantom');
      const mockAdapter = {
        connected: true,
        disconnect: vi.fn().mockResolvedValue(undefined),
      };

      vi.mocked(PhantomWalletAdapter).mockImplementation(() => mockAdapter as any);

      const result = await disconnectPhantomWallet();

      expect(mockAdapter.disconnect).toHaveBeenCalled();
      expect(result).toBe(true);
    });

    it('should not call disconnect if wallet is not connected', async () => {
      const { PhantomWalletAdapter } = await import('@solana/wallet-adapter-phantom');
      const mockAdapter = {
        connected: false,
        disconnect: vi.fn(),
      };

      vi.mocked(PhantomWalletAdapter).mockImplementation(() => mockAdapter as any);

      const result = await disconnectPhantomWallet();

      expect(mockAdapter.disconnect).not.toHaveBeenCalled();
      expect(result).toBe(true);
    });

    it('should return false when disconnection fails', async () => {
      const { PhantomWalletAdapter } = await import('@solana/wallet-adapter-phantom');
      const mockAdapter = {
        connected: true,
        disconnect: vi.fn().mockRejectedValue(new Error('Disconnect failed')),
      };

      vi.mocked(PhantomWalletAdapter).mockImplementation(() => mockAdapter as any);

      const result = await disconnectPhantomWallet();

      expect(result).toBe(false);
    });

    it('should handle adapter instantiation errors during disconnect', async () => {
      const { PhantomWalletAdapter } = await import('@solana/wallet-adapter-phantom');
      vi.mocked(PhantomWalletAdapter).mockImplementation(() => {
        throw new Error('Adapter creation failed');
      });

      const result = await disconnectPhantomWallet();

      expect(result).toBe(false);
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty wallet address', async () => {
      const { PhantomWalletAdapter } = await import('@solana/wallet-adapter-phantom');
      const mockAdapter = {
        connected: false,
        publicKey: {
          toString: () => ''
        },
        connect: vi.fn().mockResolvedValue(undefined),
      };

      vi.mocked(PhantomWalletAdapter).mockImplementation(() => mockAdapter as any);

      const result = await connectPhantomWallet();

      expect(result).toBeNull();
    });

    it('should handle very long wallet addresses', async () => {
      const { PhantomWalletAdapter } = await import('@solana/wallet-adapter-phantom');
      const longAddress = 'A'.repeat(100);
      const mockAdapter = {
        connected: false,
        publicKey: {
          toString: () => longAddress
        },
        connect: vi.fn().mockResolvedValue(undefined),
      };

      vi.mocked(PhantomWalletAdapter).mockImplementation(() => mockAdapter as any);

      const result = await connectPhantomWallet();

      expect(result?.display).toBe('AAAA...AAAA');
      expect(result?.address).toBe(longAddress);
    });
  });
});
