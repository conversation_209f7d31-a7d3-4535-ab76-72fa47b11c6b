import { vi } from 'vitest';

/**
 * Security testing utilities for wallet and API key security
 */

// Mock console methods to capture logs for security testing
export const createSecurityLogCapture = () => {
  const logs: string[] = [];
  const errors: string[] = [];
  const warns: string[] = [];

  const originalConsole = {
    log: console.log,
    error: console.error,
    warn: console.warn,
  };

  // Override console methods to capture output
  console.log = vi.fn((...args) => {
    logs.push(args.join(' '));
    originalConsole.log(...args);
  });

  console.error = vi.fn((...args) => {
    errors.push(args.join(' '));
    originalConsole.error(...args);
  });

  console.warn = vi.fn((...args) => {
    warns.push(args.join(' '));
    originalConsole.warn(...args);
  });

  return {
    logs,
    errors,
    warns,
    restore: () => {
      console.log = originalConsole.log;
      console.error = originalConsole.error;
      console.warn = originalConsole.warn;
    },
    clear: () => {
      logs.length = 0;
      errors.length = 0;
      warns.length = 0;
    }
  };
};

// Check if any sensitive data appears in logs
export const checkForSensitiveDataInLogs = (logs: string[], sensitivePatterns: string[]) => {
  const violations: string[] = [];
  
  logs.forEach((log, index) => {
    sensitivePatterns.forEach(pattern => {
      if (log.toLowerCase().includes(pattern.toLowerCase())) {
        violations.push(`Log ${index}: Found sensitive pattern "${pattern}" in: ${log}`);
      }
    });
  });
  
  return violations;
};

// Common sensitive data patterns to check for
export const SENSITIVE_PATTERNS = {
  PRIVATE_KEYS: [
    'private key',
    'privatekey',
    'secret key',
    'secretkey',
    'keypair',
    'seed phrase',
    'mnemonic'
  ],
  API_KEYS: [
    'sk-', // OpenAI API key prefix
    'api_key',
    'apikey',
    'bearer ',
    'authorization:'
  ],
  WALLET_SECRETS: [
    'wallet secret',
    'wallet private',
    'signing key'
  ]
};

// Mock localStorage with security tracking
export const createSecureLocalStorageMock = () => {
  const storage: Record<string, string> = {};
  const accessLog: Array<{ action: string; key: string; value?: string }> = [];

  return {
    getItem: vi.fn((key: string) => {
      accessLog.push({ action: 'get', key });
      return storage[key] || null;
    }),
    setItem: vi.fn((key: string, value: string) => {
      accessLog.push({ action: 'set', key, value });
      storage[key] = value;
    }),
    removeItem: vi.fn((key: string) => {
      accessLog.push({ action: 'remove', key });
      delete storage[key];
    }),
    clear: vi.fn(() => {
      accessLog.push({ action: 'clear', key: 'all' });
      Object.keys(storage).forEach(key => delete storage[key]);
    }),
    storage,
    accessLog,
    clearLog: () => accessLog.length = 0
  };
};

// Validate API key format and security
export const validateApiKeySecurity = (apiKey: string | null) => {
  const issues: string[] = [];

  if (!apiKey) {
    return { isSecure: true, issues: [] }; // No key is fine
  }

  // Check for common security issues
  if (apiKey.length < 10) {
    issues.push('API key is too short');
  }

  if (apiKey.includes(' ')) {
    issues.push('API key contains spaces');
  }

  if (!/^[a-zA-Z0-9\-_]+$/.test(apiKey)) {
    issues.push('API key contains invalid characters');
  }

  // Check for test/dummy keys
  const testPatterns = ['test', 'dummy', 'fake', 'mock', 'example'];
  if (testPatterns.some(pattern => apiKey.toLowerCase().includes(pattern))) {
    issues.push('API key appears to be a test/dummy key');
  }

  return {
    isSecure: issues.length === 0,
    issues
  };
};

// Mock wallet adapter with security tracking
export const createSecureWalletMock = () => {
  const securityLog: Array<{ action: string; data?: any }> = [];

  return {
    connected: false,
    publicKey: null,
    connect: vi.fn().mockImplementation(async () => {
      securityLog.push({ action: 'connect' });
      return undefined;
    }),
    disconnect: vi.fn().mockImplementation(async () => {
      securityLog.push({ action: 'disconnect' });
      return undefined;
    }),
    signTransaction: vi.fn().mockImplementation(async (transaction) => {
      securityLog.push({ action: 'signTransaction', data: 'transaction_signed' });
      return transaction;
    }),
    signAllTransactions: vi.fn().mockImplementation(async (transactions) => {
      securityLog.push({ action: 'signAllTransactions', data: `${transactions.length}_transactions_signed` });
      return transactions;
    }),
    sendTransaction: vi.fn().mockImplementation(async () => {
      securityLog.push({ action: 'sendTransaction' });
      return 'mock-signature';
    }),
    on: vi.fn(),
    off: vi.fn(),
    removeAllListeners: vi.fn(),
    securityLog,
    clearSecurityLog: () => securityLog.length = 0
  };
};

// Check for private key exposure in wallet operations
export const checkWalletSecurityViolations = (walletMock: any) => {
  const violations: string[] = [];
  
  walletMock.securityLog.forEach((entry: any, index: number) => {
    // Check if any operation logs sensitive data
    if (entry.data && typeof entry.data === 'string') {
      if (entry.data.includes('private') || entry.data.includes('secret')) {
        violations.push(`Security violation in operation ${index}: ${entry.action} exposed sensitive data`);
      }
    }
  });

  return violations;
};
