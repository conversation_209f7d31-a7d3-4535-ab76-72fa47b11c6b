import '@testing-library/jest-dom'
import { vi } from 'vitest'

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock scrollIntoView
Element.prototype.scrollIntoView = vi.fn()

// Mock getBoundingClientRect
Element.prototype.getBoundingClientRect = vi.fn(() => ({
  width: 120,
  height: 120,
  top: 0,
  left: 0,
  bottom: 0,
  right: 0,
  x: 0,
  y: 0,
  toJSON: vi.fn(),
}))

// Mock window.getComputedStyle
window.getComputedStyle = vi.fn(() => ({
  getPropertyValue: vi.fn(),
})) as any

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
  writable: true,
})

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
  writable: true,
})

// Mock window.open
Object.defineProperty(window, 'open', {
  writable: true,
  value: vi.fn(),
})

// Mock window.alert, confirm, prompt
Object.defineProperty(window, 'alert', {
  writable: true,
  value: vi.fn(),
})

Object.defineProperty(window, 'confirm', {
  writable: true,
  value: vi.fn(() => true),
})

Object.defineProperty(window, 'prompt', {
  writable: true,
  value: vi.fn(() => 'mock-input'),
})

// Mock navigator.clipboard
Object.defineProperty(navigator, 'clipboard', {
  value: {
    writeText: vi.fn(() => Promise.resolve()),
    readText: vi.fn(() => Promise.resolve('mock-text')),
  },
  writable: true,
})

// Mock fetch globally with intelligent routing for different APIs
global.fetch = vi.fn().mockImplementation((url: string, options?: any) => {
  // Mock OpenAI API responses
  if (typeof url === 'string' && url.includes('api.openai.com')) {
    return Promise.resolve({
      ok: true,
      status: 200,
      json: () => Promise.resolve({
        choices: [{
          message: {
            content: 'Mock OpenAI response with web search results integrated. This is a test response that simulates the AI assistant providing trading advice based on web search.'
          }
        }]
      }),
      text: () => Promise.resolve('Mock OpenAI text response'),
    })
  }

  // Mock other API calls with generic success response
  return Promise.resolve({
    ok: true,
    status: 200,
    json: () => Promise.resolve({ success: true, data: 'mock-data' }),
    text: () => Promise.resolve('mock-response'),
  })
})

// Mock HTMLCanvasElement.getContext
HTMLCanvasElement.prototype.getContext = vi.fn()

// Mock all environment variables used in the application
vi.stubEnv('VITE_RPC_URL', 'https://mock-rpc-url.com')
vi.stubEnv('VITE_BASE_PYRON_URL', 'https://mock-api.test.com')
vi.stubEnv('VITE_WEBHOOK_URL', 'https://mock-webhook.test.com')
vi.stubEnv('VITE_ADMIN_KEY', 'mock-admin-key-for-testing')
vi.stubEnv('VITE_ENVIRONMENT', 'test')

// Mock Solana Web3.js globally
vi.mock('@solana/web3.js', () => ({
  Connection: vi.fn().mockImplementation(() => ({
    getLatestBlockhash: vi.fn().mockResolvedValue({
      blockhash: 'mock-blockhash',
      lastValidBlockHeight: 123456,
    }),
    sendTransaction: vi.fn().mockResolvedValue('mock-signature'),
    confirmTransaction: vi.fn().mockResolvedValue({ value: { err: null } }),
    getAccountInfo: vi.fn().mockResolvedValue(null),
    getBalance: vi.fn().mockResolvedValue(**********), // 1 SOL in lamports
  })),
  PublicKey: vi.fn().mockImplementation((key) => ({
    toString: () => key || 'mock-public-key',
    toBase58: () => key || 'mock-public-key',
  })),
  Keypair: {
    generate: vi.fn().mockReturnValue({
      publicKey: { toString: () => 'mock-keypair-public-key' },
      secretKey: new Uint8Array(64),
    }),
  },
  SystemProgram: {
    transfer: vi.fn().mockReturnValue({ keys: [], programId: 'mock-program-id' }),
  },
  Transaction: vi.fn().mockImplementation(() => ({
    add: vi.fn(),
    sign: vi.fn(),
    serialize: vi.fn().mockReturnValue(new Uint8Array()),
  })),
}))

// Mock Drift SDK globally
vi.mock('@drift-labs/sdk', () => ({
  DriftClient: vi.fn().mockImplementation(() => ({
    subscribe: vi.fn().mockResolvedValue(undefined),
    unsubscribe: vi.fn().mockResolvedValue(undefined),
    convertToSpotPrecision: vi.fn().mockReturnValue(1000000),
    getAssociatedTokenAccount: vi.fn().mockResolvedValue(null),
    getUserAccount: vi.fn().mockResolvedValue({
      authority: 'mock-authority',
      subAccountId: 0,
    }),
    getUser: vi.fn().mockResolvedValue({
      getUserAccount: vi.fn().mockReturnValue({
        authority: 'mock-authority',
        subAccountId: 0,
      }),
    }),
    deposit: vi.fn().mockResolvedValue('mock-deposit-signature'),
    withdraw: vi.fn().mockResolvedValue('mock-withdraw-signature'),
  })),
  BN: vi.fn().mockImplementation((value) => ({
    toString: () => value?.toString() || '0',
    toNumber: () => Number(value) || 0,
  })),
}))

// Mock Phantom Wallet Adapter globally
vi.mock('@solana/wallet-adapter-phantom', () => ({
  PhantomWalletAdapter: vi.fn().mockImplementation(() => ({
    connected: false,
    publicKey: null,
    connect: vi.fn().mockResolvedValue(undefined),
    disconnect: vi.fn().mockResolvedValue(undefined),
    on: vi.fn(),
    off: vi.fn(),
    removeAllListeners: vi.fn(),
    sendTransaction: vi.fn().mockResolvedValue('mock-signature'),
    signTransaction: vi.fn().mockResolvedValue({}),
    signAllTransactions: vi.fn().mockResolvedValue([]),
  })),
}))

// Ensure clean test environment between tests
beforeEach(() => {
  // Clear all mocks before each test
  vi.clearAllMocks()

  // Reset DOM state
  document.body.innerHTML = ''
  document.head.innerHTML = ''

  // Clear any existing timers
  vi.clearAllTimers()

  // Reset localStorage
  localStorage.clear()

  // Reset any global state that might interfere with tests
  if (typeof window !== 'undefined') {
    // Reset scroll lock attributes that might persist between tests
    document.body.removeAttribute('data-scroll-locked')
    document.body.style.removeProperty('pointer-events')
  }
})

// Mock Solana Web3.js completely to prevent any real blockchain interactions
vi.mock('@solana/web3.js', async () => {
  const actual = await vi.importActual('@solana/web3.js')
  return {
    ...actual,
    PublicKey: class MockPublicKey {
      constructor(public key: string) {}
      toString() { return this.key }
      toBase58() { return this.key }
      toBytes() { return new Uint8Array(32) }
      equals(other: any) { return this.key === other.key }
      static findProgramAddressSync() {
        return ['mock-address', 255]
      }
      static findProgramAddress() {
        return Promise.resolve(['mock-address', 255])
      }
      static createProgramAddressSync() {
        return 'mock-program-address'
      }
      static isOnCurve() { return true }
    },
    Connection: vi.fn().mockImplementation(() => ({
      getLatestBlockhash: vi.fn().mockResolvedValue({
        blockhash: 'mock-blockhash',
        lastValidBlockHeight: 123456,
      }),
      sendTransaction: vi.fn().mockResolvedValue('mock-signature'),
      confirmTransaction: vi.fn().mockResolvedValue({ value: { err: null } }),
      getAccountInfo: vi.fn().mockResolvedValue(null),
      getBalance: vi.fn().mockResolvedValue(**********), // 1 SOL in lamports
      getTokenAccountBalance: vi.fn().mockResolvedValue({
        value: { amount: '1000000', decimals: 6, uiAmount: 1 }
      }),
      getTokenAccountsByOwner: vi.fn().mockResolvedValue({ value: [] }),
      getParsedAccountInfo: vi.fn().mockResolvedValue({ value: null }),
      getSlot: vi.fn().mockResolvedValue(123456),
      getBlockHeight: vi.fn().mockResolvedValue(123456),
      getRecentBlockhash: vi.fn().mockResolvedValue({
        blockhash: 'mock-blockhash',
        feeCalculator: { lamportsPerSignature: 5000 }
      }),
    })),
    Transaction: vi.fn().mockImplementation(() => ({
      add: vi.fn().mockReturnThis(),
      recentBlockhash: '',
      lastValidBlockHeight: 0,
      feePayer: null,
      sign: vi.fn(),
      serialize: vi.fn().mockReturnValue(new Uint8Array()),
      signatures: [],
    })),
    VersionedTransaction: vi.fn().mockImplementation(() => ({
      sign: vi.fn(),
      serialize: vi.fn().mockReturnValue(new Uint8Array()),
      signatures: [],
    })),
    ComputeBudgetProgram: {
      setComputeUnitPrice: vi.fn().mockReturnValue('mock-compute-instruction'),
      setComputeUnitLimit: vi.fn().mockReturnValue('mock-compute-limit-instruction'),
    },
    SystemProgram: {
      transfer: vi.fn().mockReturnValue('mock-transfer-instruction'),
      createAccount: vi.fn().mockReturnValue('mock-create-account-instruction'),
    },
    Keypair: {
      generate: vi.fn(() => ({
        publicKey: 'mock-public-key',
        secretKey: new Uint8Array(64),
      })),
      fromSecretKey: vi.fn(() => ({
        publicKey: 'mock-public-key',
        secretKey: new Uint8Array(64),
      })),
    },
    LAMPORTS_PER_SOL: **********,
    clusterApiUrl: vi.fn().mockReturnValue('https://mock-cluster-url.com'),
  }
})

// Mock SPL Token library
vi.mock('@solana/spl-token', () => ({
  TOKEN_PROGRAM_ID: 'mock-token-program-id',
  ASSOCIATED_TOKEN_PROGRAM_ID: 'mock-associated-token-program-id',
  AccountLayout: {
    decode: vi.fn().mockReturnValue({
      mint: 'mock-mint',
      owner: 'mock-owner',
      amount: BigInt(1000000),
    }),
  },
  getAssociatedTokenAddress: vi.fn().mockResolvedValue('mock-associated-token-address'),
  createTransferInstruction: vi.fn().mockReturnValue('mock-transfer-instruction'),
  createAssociatedTokenAccountInstruction: vi.fn().mockReturnValue('mock-create-ata-instruction'),
  getAccount: vi.fn().mockResolvedValue({
    address: 'mock-token-account',
    mint: 'mock-mint',
    owner: 'mock-owner',
    amount: BigInt(1000000),
  }),
}))

// Mock Phantom Wallet Adapter
vi.mock('@solana/wallet-adapter-phantom', () => ({
  PhantomWalletAdapter: vi.fn().mockImplementation(() => ({
    name: 'Phantom',
    url: 'https://phantom.app',
    icon: 'mock-icon',
    readyState: 'Installed',
    publicKey: null,
    connected: false,
    connecting: false,
    supportedTransactionVersions: new Set(['legacy', 0]),
    connect: vi.fn().mockResolvedValue(undefined),
    disconnect: vi.fn().mockResolvedValue(undefined),
    sendTransaction: vi.fn().mockResolvedValue('mock-signature'),
    signTransaction: vi.fn().mockResolvedValue('mock-signed-transaction'),
    signAllTransactions: vi.fn().mockResolvedValue(['mock-signed-transaction']),
    signMessage: vi.fn().mockResolvedValue(new Uint8Array()),
    on: vi.fn(),
    off: vi.fn(),
    removeAllListeners: vi.fn(),
  })),
}))

// Mock Switchboard
vi.mock('@switchboard-xyz/on-demand', () => ({
  default: {},
  SwitchboardProgram: vi.fn(),
  AggregatorAccount: vi.fn(),
}))

// Mock Drift SDK completely
vi.mock('@drift-labs/sdk', () => ({
  DriftClient: vi.fn().mockImplementation(() => ({
    subscribe: vi.fn().mockResolvedValue(undefined),
    unsubscribe: vi.fn().mockResolvedValue(undefined),
    convertToSpotPrecision: vi.fn().mockReturnValue(1000000),
    getAssociatedTokenAccount: vi.fn().mockResolvedValue('mock-token-account'),
    getUserAccount: vi.fn().mockResolvedValue({
      authority: 'mock-authority',
      subAccountId: 0,
      positions: [],
    }),
    getUser: vi.fn().mockResolvedValue({
      getUserAccount: vi.fn().mockReturnValue({
        authority: 'mock-authority',
        subAccountId: 0,
        positions: [],
      }),
    }),
    deposit: vi.fn().mockResolvedValue('mock-deposit-signature'),
    withdraw: vi.fn().mockResolvedValue('mock-withdraw-signature'),
    placePerpOrder: vi.fn().mockResolvedValue('mock-order-signature'),
    cancelOrder: vi.fn().mockResolvedValue('mock-cancel-signature'),
    getSpotMarketAccount: vi.fn().mockResolvedValue({
      marketIndex: 0,
      mint: 'mock-mint',
      decimals: 6,
    }),
    getPerpMarketAccount: vi.fn().mockResolvedValue({
      marketIndex: 0,
      baseAssetSymbol: 'SOL',
      quoteAssetSymbol: 'USD',
    }),
  })),
  BN: vi.fn().mockImplementation((value) => ({
    toString: vi.fn(() => value.toString()),
    toNumber: vi.fn(() => Number(value)),
    add: vi.fn().mockReturnThis(),
    sub: vi.fn().mockReturnThis(),
    mul: vi.fn().mockReturnThis(),
    div: vi.fn().mockReturnThis(),
  })),
  BulkAccountLoader: vi.fn().mockImplementation(() => ({
    load: vi.fn().mockResolvedValue(undefined),
    unload: vi.fn().mockResolvedValue(undefined),
  })),
  getUserAccountPublicKey: vi.fn().mockReturnValue('mock-user-account-public-key'),
  USDC_MARKET_INDEX: 0,
  SOL_MARKET_INDEX: 1,
  Wallet: vi.fn(),
  PositionDirection: {
    LONG: 'long',
    SHORT: 'short',
  },
  OrderType: {
    MARKET: 'market',
    LIMIT: 'limit',
  },
  MarketType: {
    PERP: 'perp',
    SPOT: 'spot',
  },
}))

// Mock bs58 library
vi.mock('bs58', () => ({
  default: {
    encode: vi.fn().mockReturnValue('mock-encoded-string'),
    decode: vi.fn().mockReturnValue(new Uint8Array(32)),
  },
  encode: vi.fn().mockReturnValue('mock-encoded-string'),
  decode: vi.fn().mockReturnValue(new Uint8Array(32)),
}))

// Mock TradingView widget
vi.mock('@/components/TradingViewWidget', () => ({
  default: () => '<div data-testid="trading-view-widget">TradingView Widget</div>',
}))

// Mock any crypto/buffer operations that might be used
global.Buffer = global.Buffer || {
  from: vi.fn().mockReturnValue(new Uint8Array()),
  alloc: vi.fn().mockReturnValue(new Uint8Array()),
  isBuffer: vi.fn().mockReturnValue(false),
}

// Mock process.env for any direct access (though we prefer vi.stubEnv)
global.process = global.process || {
  env: {
    NODE_ENV: 'test',
    VITE_RPC_URL: 'https://mock-rpc-url.com',
    VITE_BASE_PYRON_URL: 'https://mock-api.test.com',
    VITE_WEBHOOK_URL: 'https://mock-webhook.test.com',
    VITE_ADMIN_KEY: 'mock-admin-key-for-testing',
    VITE_ENVIRONMENT: 'test',
  },
  browser: true,
}

// Mock WebSocket for any real-time connections
global.WebSocket = vi.fn().mockImplementation(() => ({
  send: vi.fn(),
  close: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  readyState: 1, // OPEN
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3,
}))

// Mock any potential crypto operations
Object.defineProperty(global, 'crypto', {
  value: {
    getRandomValues: vi.fn().mockImplementation((arr) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256)
      }
      return arr
    }),
    randomUUID: vi.fn(() => 'mock-uuid-1234-5678-9abc-def0'),
    subtle: {
      digest: vi.fn().mockResolvedValue(new ArrayBuffer(32)),
      encrypt: vi.fn().mockResolvedValue(new ArrayBuffer(16)),
      decrypt: vi.fn().mockResolvedValue(new ArrayBuffer(16)),
    },
  },
  writable: true,
})

// Ensure all console methods are available for testing
global.console = {
  ...console,
  log: vi.fn(),
  error: vi.fn(),
  warn: vi.fn(),
  info: vi.fn(),
  debug: vi.fn(),
}
