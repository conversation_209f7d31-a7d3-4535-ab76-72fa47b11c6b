import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { BrowserRouter } from 'react-router-dom'

// Import components to test
import WalletConnect from '@/components/WalletConnect'
import DepositDialog from '@/components/DepositDialog'
import AutoTradeDialog from '@/components/AutoTradeDialog'

// Mock stores
vi.mock('@/store/walletStore', () => ({
  useWalletStore: () => ({
    isConnected: true,
    walletAddress: '5Zzguz9NaP4nQtgHNwXjVfZmAYJZXeg6QZz4KPQiqdz4',
    connect: vi.fn().mockResolvedValue(true),
    disconnect: vi.fn()
  })
}))

// Mock Drift SDK
vi.mock('@/lib/drift', () => ({
  depositFunds: vi.fn().mockResolvedValue({ signature: 'mock-signature' }),
  setupDriftClient: vi.fn().mockResolvedValue(true)
}))

// Test wrapper with providers
const TestWrapper = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  )
}

describe('Trading Flow Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should complete a full trading flow: connect wallet → deposit → setup auto-trade', async () => {
    // Render wallet connection
    const { rerender } = render(
      <TestWrapper>
        <WalletConnect />
      </TestWrapper>
    )
    
    // Test wallet connection
    const connectButton = screen.getByText(/connect wallet/i)
    fireEvent.click(connectButton)
    
    // Test deposit flow
    rerender(
      <TestWrapper>
        <DepositDialog open={true} onOpenChange={vi.fn()} />
      </TestWrapper>
    )
    
    const amountInput = screen.getByLabelText(/amount/i)
    fireEvent.change(amountInput, { target: { value: '10' } })
    
    const depositButton = screen.getByText(/deposit/i)
    fireEvent.click(depositButton)
    
    await waitFor(() => {
      expect(screen.getByText(/deposit successful/i)).toBeInTheDocument()
    })
    
    // Test auto-trade setup
    rerender(
      <TestWrapper>
        <AutoTradeDialog open={true} onOpenChange={vi.fn()} />
      </TestWrapper>
    )
    
    const strategySelect = screen.getByLabelText(/strategy/i)
    fireEvent.change(strategySelect, { target: { value: 'momentum' } })
    
    const activateButton = screen.getByText(/activate/i)
    fireEvent.click(activateButton)
    
    await waitFor(() => {
      expect(screen.getByText(/auto-trading activated/i)).toBeInTheDocument()
    })
  })
})
