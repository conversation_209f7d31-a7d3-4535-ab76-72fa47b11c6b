# Integration Tests

This directory contains integration tests that test multiple components working together.

## Structure

- `TradingFlow.test.tsx` - Tests the complete trading flow from wallet connection to auto-trading
- `ChatTrading.test.tsx` - Tests the interaction between chat interface and trading components
- `WalletOperations.test.tsx` - Tests wallet operations across multiple components

## Running Tests

```bash
# Run all integration tests
npm test -- --run src/test/integration/

# Run a specific integration test
npm test -- --run src/test/integration/TradingFlow.test.tsx
```