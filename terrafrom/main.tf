
resource "null_resource" "deploy_docker_app" {
  connection {
    type        = "ssh"
    user        = "shuvo"
    private_key = file(var.pvt_key_path)
    host        = var.droplet_ip
    timeout     = "2m"
  }

  provisioner "file" {
    source      = "/home/<USER>/Documents/Codes/Pyron/pyron-meta-repo"
    destination = "/home/<USER>/pyron-project"
  }

  provisioner "remote-exec" {
    inline = [
      "cd /home/<USER>/pyron-project",
      "docker compose down || true",
      "docker compose up -d --build"
    ]
  }
}
