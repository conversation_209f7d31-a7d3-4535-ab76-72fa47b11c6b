
resource "null_resource" "deploy_docker_app" {
  connection {
    type        = "ssh"
    user        = "shuvo"
    private_key = file(var.pvt_key_path)
    host        = var.droplet_ip
    timeout     = "2m"
    port        = 22

    # Additional SSH options for better compatibility
    agent                = false
    host_key            = null
    bastion_host_key    = null
  }

  # Create remote directory first
  provisioner "remote-exec" {
    inline = [
      "mkdir -p /home/<USER>/pyron-project",
      "rm -rf /home/<USER>/pyron-project/*"
    ]
  }

  # Copy project files (excluding sensitive files)
  provisioner "file" {
    source      = "/home/<USER>/Documents/Codes/Pyron/pyron-meta-repo/"
    destination = "/home/<USER>/pyron-project"
  }

  # Deploy application
  provisioner "remote-exec" {
    inline = [
      "cd /home/<USER>/pyron-project",
      "echo 'Current directory: '$(pwd)",
      "echo 'Files in directory:'",
      "ls -la",
      "echo 'Stopping existing containers...'",
      "docker compose down || true",
      "echo 'Cleaning up old images...'",
      "docker image prune -f || true",
      "echo 'Building and starting containers...'",
      "docker compose up -d --build",
      "sleep 10",
      "echo 'Container status:'",
      "docker compose ps",
      "echo 'Deployment completed!'"
    ]
  }
}
