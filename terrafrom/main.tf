
resource "null_resource" "deploy_docker_app" {
  connection {
    type        = "ssh"
    user        = "shuvo"
    private_key = file(var.pvt_key_path)
    host        = var.droplet_ip
    timeout     = "2m"
    port        = 22

    # Additional SSH options for better compatibility
    agent                = false
    host_key            = null
    bastion_host_key    = null
  }

  # Create remote directories first
  provisioner "remote-exec" {
    inline = [
      "mkdir -p /home/<USER>/pyron-project",
      "rm -rf /home/<USER>/pyron-project/*",
      "mkdir -p /home/<USER>/pyron-project/pyron-mvp",
      "mkdir -p /home/<USER>/pyron-project/pyron-webhook",
      "mkdir -p /home/<USER>/pyron-project/PyRon-webApp"
    ]
  }

  # Use rsync to copy files (more reliable than file provisioner)
  provisioner "local-exec" {
    command = <<-EOT
      rsync -avz --delete \
        --exclude 'node_modules' \
        --exclude '.git' \
        --exclude '*.log' \
        --exclude '.env*' \
        --exclude 'terrafrom/.terraform' \
        --exclude 'terrafrom/*.tfstate*' \
        -e "ssh -i ${var.pvt_key_path} -o StrictHostKeyChecking=no" \
        /home/<USER>/Documents/Codes/Pyron/pyron-meta-repo/ \
        shuvo@${var.droplet_ip}:/home/<USER>/pyron-project/
    EOT
  }

  # Copy environment files first
  provisioner "file" {
    source      = "/home/<USER>/Documents/Codes/Pyron/pyron-meta-repo/pyron-mvp/.env.auth"
    destination = "/home/<USER>/pyron-project/pyron-mvp/.env.auth"
  }

  provisioner "file" {
    source      = "/home/<USER>/Documents/Codes/Pyron/pyron-meta-repo/pyron-mvp/.env.database"
    destination = "/home/<USER>/pyron-project/pyron-mvp/.env.database"
  }

  provisioner "file" {
    source      = "/home/<USER>/Documents/Codes/Pyron/pyron-meta-repo/pyron-mvp/.env.core"
    destination = "/home/<USER>/pyron-project/pyron-mvp/.env.core"
  }

  provisioner "file" {
    source      = "/home/<USER>/Documents/Codes/Pyron/pyron-meta-repo/pyron-mvp/.env.blockchain"
    destination = "/home/<USER>/pyron-project/pyron-mvp/.env.blockchain"
  }

  provisioner "file" {
    source      = "/home/<USER>/Documents/Codes/Pyron/pyron-meta-repo/pyron-mvp/.env.wallet"
    destination = "/home/<USER>/pyron-project/pyron-mvp/.env.wallet"
  }

  provisioner "file" {
    source      = "/home/<USER>/Documents/Codes/Pyron/pyron-meta-repo/pyron-webhook/.env.admin"
    destination = "/home/<USER>/pyron-project/pyron-webhook/.env.admin"
  }

  provisioner "file" {
    source      = "/home/<USER>/Documents/Codes/Pyron/pyron-meta-repo/pyron-webhook/.env.database"
    destination = "/home/<USER>/pyron-project/pyron-webhook/.env.database"
  }

  provisioner "file" {
    source      = "/home/<USER>/Documents/Codes/Pyron/pyron-meta-repo/pyron-webhook/.env.server"
    destination = "/home/<USER>/pyron-project/pyron-webhook/.env.server"
  }

  provisioner "file" {
    source      = "/home/<USER>/Documents/Codes/Pyron/pyron-meta-repo/pyron-webhook/.env.security"
    destination = "/home/<USER>/pyron-project/pyron-webhook/.env.security"
  }

  provisioner "file" {
    source      = "/home/<USER>/Documents/Codes/Pyron/pyron-meta-repo/PyRon-webApp/.env"
    destination = "/home/<USER>/pyron-project/PyRon-webApp/.env"
  }

  # Deploy application without sudo
  provisioner "remote-exec" {
    inline = [
      "cd /home/<USER>/pyron-project",
      "echo 'Current directory: '$(pwd)",
      "echo 'Files in directory:'",
      "ls -la",
      "echo 'Checking environment files...'",
      "ls -la pyron-mvp/.env* || echo 'No pyron-mvp env files'",
      "ls -la pyron-webhook/.env* || echo 'No pyron-webhook env files'",
      "ls -la PyRon-webApp/.env || echo 'No PyRon-webApp env file'",
      "echo 'Attempting Docker deployment...'",
      "# Try without sudo first",
      "docker compose down || echo 'No containers to stop'",
      "docker compose up -d --build || echo 'Docker command failed, may need permissions setup'",
      "sleep 15",
      "echo 'Container status:'",
      "docker compose ps || echo 'Cannot check container status'",
      "echo 'Checking service health...'",
      "curl -f http://localhost:3000 > /dev/null 2>&1 && echo '✓ Backend API is responding' || echo '✗ Backend API is not responding'",
      "curl -f http://localhost:3004 > /dev/null 2>&1 && echo '✓ Webhook service is responding' || echo '✗ Webhook service is not responding'",
      "curl -f http://localhost:8080 > /dev/null 2>&1 && echo '✓ Frontend is responding' || echo '✗ Frontend is not responding'",
      "echo 'Deployment completed!'"
    ]
  }
}
