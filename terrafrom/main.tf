
resource "null_resource" "deploy_docker_app" {
  connection {
    type        = "ssh"
    user        = var.remote_user
    private_key = file(var.pvt_key_path)
    host        = var.droplet_ip
    timeout     = "2m"
    port        = 22

    # Additional SSH options for better compatibility
    agent                = false
    host_key            = null
    bastion_host_key    = null
  }

  # Create remote directories first
  provisioner "remote-exec" {
    inline = [
      "mkdir -p ${var.remote_project_path}",
      "rm -rf ${var.remote_project_path}/*",
      "mkdir -p ${var.remote_project_path}/pyron-mvp",
      "mkdir -p ${var.remote_project_path}/pyron-webhook",
      "mkdir -p ${var.remote_project_path}/PyRon-webApp"
    ]
  }

  # Use rsync to copy files (more reliable than file provisioner)
  provisioner "local-exec" {
    command = <<-EOT
      rsync -avz --delete \
        --exclude 'node_modules' \
        --exclude '.git' \
        --exclude '*.log' \
        --exclude '.env*' \
        --exclude 'terrafrom/.terraform' \
        --exclude 'terrafrom/*.tfstate*' \
        -e "ssh -i ${var.pvt_key_path} -o StrictHostKeyChecking=no" \
        ${var.local_project_path}/ \
        ${var.remote_user}@${var.droplet_ip}:${var.remote_project_path}/
    EOT
  }

  # Copy environment files first
  provisioner "file" {
    source      = "${var.local_project_path}/pyron-mvp/.env.auth"
    destination = "${var.remote_project_path}/pyron-mvp/.env.auth"
  }

  provisioner "file" {
    source      = "${var.local_project_path}/pyron-mvp/.env.database"
    destination = "${var.remote_project_path}/pyron-mvp/.env.database"
  }

  provisioner "file" {
    source      = "${var.local_project_path}/pyron-mvp/.env.core"
    destination = "${var.remote_project_path}/pyron-mvp/.env.core"
  }

  provisioner "file" {
    source      = "${var.local_project_path}/pyron-mvp/.env.blockchain"
    destination = "${var.remote_project_path}/pyron-mvp/.env.blockchain"
  }

  provisioner "file" {
    source      = "${var.local_project_path}/pyron-mvp/.env.wallet"
    destination = "${var.remote_project_path}/pyron-mvp/.env.wallet"
  }

  provisioner "file" {
    source      = "${var.local_project_path}/pyron-webhook/.env.admin"
    destination = "${var.remote_project_path}/pyron-webhook/.env.admin"
  }

  provisioner "file" {
    source      = "${var.local_project_path}/pyron-webhook/.env.database"
    destination = "${var.remote_project_path}/pyron-webhook/.env.database"
  }

  provisioner "file" {
    source      = "${var.local_project_path}/pyron-webhook/.env.server"
    destination = "${var.remote_project_path}/pyron-webhook/.env.server"
  }

  provisioner "file" {
    source      = "${var.local_project_path}/pyron-webhook/.env.security"
    destination = "${var.remote_project_path}/pyron-webhook/.env.security"
  }

  provisioner "file" {
    source      = "${var.local_project_path}/PyRon-webApp/.env"
    destination = "${var.remote_project_path}/PyRon-webApp/.env"
  }

  # Deploy application without sudo
  provisioner "remote-exec" {
    inline = [
      "cd ${var.remote_project_path}",
      "echo 'Current directory: '$(pwd)",
      "echo 'Files in directory:'",
      "ls -la",
      "echo 'Checking environment files...'",
      "ls -la pyron-mvp/.env* || echo 'No pyron-mvp env files'",
      "ls -la pyron-webhook/.env* || echo 'No pyron-webhook env files'",
      "ls -la PyRon-webApp/.env || echo 'No PyRon-webApp env file'",
      "echo 'Attempting Docker deployment...'",
      "# Try without sudo first",
      "docker compose down || echo 'No containers to stop'",
      "docker compose up -d --build || echo 'Docker command failed, may need permissions setup'",
      "sleep 15",
      "echo 'Container status:'",
      "docker compose ps || echo 'Cannot check container status'",
      "echo 'Checking service health...'",
      "curl -f http://localhost:3000 > /dev/null 2>&1 && echo '✓ Backend API is responding' || echo '✗ Backend API is not responding'",
      "curl -f http://localhost:3004 > /dev/null 2>&1 && echo '✓ Webhook service is responding' || echo '✗ Webhook service is not responding'",
      "curl -f http://localhost:8080 > /dev/null 2>&1 && echo '✓ Frontend is responding' || echo '✗ Frontend is not responding'",
      "echo 'Deployment completed!'"
    ]
  }
}
