
resource "null_resource" "deploy_docker_app" {
  connection {
    type        = "ssh"
    user        = "Shuvo"
    private_key = file(var.pvt_key_path)
    host        = var.droplet_ip
  }

  provisioner "file" {
    source      = "../"           # path to your project root
    destination = "/root/pyron-project"
  }

  provisioner "remote-exec" {
    inline = [
      "apt update -y",
      "apt install -y docker.io docker-compose",
      "cd /root/pyron-project",
      "docker compose down || true",
      "docker compose up -d --build"
    ]
  }
}
