
resource "null_resource" "deploy_docker_app" {
  connection {
    type        = "ssh"
    user        = "shuvo"
    private_key = file(var.pvt_key_path)
    host        = var.droplet_ip
    timeout     = "2m"
    port        = 22

    # Additional SSH options for better compatibility
    agent                = false
    host_key            = null
    bastion_host_key    = null
  }

  # Create remote directory first
  provisioner "remote-exec" {
    inline = [
      "mkdir -p /home/<USER>/pyron-project",
      "rm -rf /home/<USER>/pyron-project/*"
    ]
  }

  # Copy docker-compose.yml
  provisioner "file" {
    source      = "/home/<USER>/Documents/Codes/Pyron/pyron-meta-repo/docker-compose.yml"
    destination = "/home/<USER>/pyron-project/docker-compose.yml"
  }

  # Copy service directories one by one for better reliability
  provisioner "file" {
    source      = "/home/<USER>/Documents/Codes/Pyron/pyron-meta-repo/pyron-mvp/"
    destination = "/home/<USER>/pyron-project/pyron-mvp"
  }

  provisioner "file" {
    source      = "/home/<USER>/Documents/Codes/Pyron/pyron-meta-repo/pyron-webhook/"
    destination = "/home/<USER>/pyron-project/pyron-webhook"
  }

  provisioner "file" {
    source      = "/home/<USER>/Documents/Codes/Pyron/pyron-meta-repo/PyRon-webApp/"
    destination = "/home/<USER>/pyron-project/PyRon-webApp"
  }

  # Setup Docker permissions and deploy application
  provisioner "remote-exec" {
    inline = [
      "cd /home/<USER>/pyron-project",
      "echo 'Current directory: '$(pwd)",
      "echo 'Files in directory:'",
      "ls -la",
      "echo 'Setting up Docker permissions...'",
      "sudo usermod -aG docker $USER || true",
      "sudo chown root:docker /var/run/docker.sock || true",
      "sudo chmod 664 /var/run/docker.sock || true",
      "echo 'Stopping existing containers...'",
      "sudo docker compose down || true",
      "echo 'Cleaning up old images...'",
      "sudo docker image prune -f || true",
      "echo 'Building and starting containers...'",
      "sudo docker compose up -d --build",
      "sleep 15",
      "echo 'Container status:'",
      "sudo docker compose ps",
      "echo 'Checking service health...'",
      "curl -f http://localhost:3000 > /dev/null 2>&1 && echo '✓ Backend API is responding' || echo '✗ Backend API is not responding'",
      "curl -f http://localhost:3004 > /dev/null 2>&1 && echo '✓ Webhook service is responding' || echo '✗ Webhook service is not responding'",
      "curl -f http://localhost:8080 > /dev/null 2>&1 && echo '✓ Frontend is responding' || echo '✗ Frontend is not responding'",
      "echo 'Deployment completed!'"
    ]
  }
}
