{"version": 4, "terraform_version": "1.1.3", "serial": 49, "lineage": "33287397-389a-d03c-e0d9-5da5f1c2b4dc", "outputs": {}, "resources": [{"mode": "data", "type": "digitalocean_ssh_key", "name": "terraform", "provider": "provider[\"registry.terraform.io/digitalocean/digitalocean\"]", "instances": [{"schema_version": 0, "attributes": {"fingerprint": "9a:28:0d:bc:8f:d3:28:11:a1:1a:61:9c:3a:60:a5:62", "id": 48776360, "name": "<PERSON><PERSON>", "public_key": "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIJABGwhk0Dcc1ECqo+MjnXN1Hc87yxQFZ1GVpXZsVXyu <EMAIL>"}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "null_resource", "name": "deploy_docker_app", "provider": "provider[\"registry.terraform.io/hashicorp/null\"]", "instances": [{"schema_version": 0, "attributes": {"id": "8888666332285705893", "triggers": null}, "sensitive_attributes": []}]}]}