variable "do_token" {
  type        = string
  description = "DigitalOcean API Token"
}

variable "pvt_key_path" {
  type        = string
  description = "Path to your private SSH key"
}

variable "droplet_ip" {
  type        = string
  description = "IP of existing droplet"
}

variable "local_project_path" {
  type        = string
  description = "Local path to the PyRon project directory"
  default     = "/home/<USER>/Documents/Codes/Pyron/pyron-meta-repo"
}

variable "remote_project_path" {
  type        = string
  description = "Remote path where the project will be deployed"
  default     = "/home/<USER>/opt/pyron"
}

variable "remote_user" {
  type        = string
  description = "Remote server username"
  default     = "pyron-service"
}
