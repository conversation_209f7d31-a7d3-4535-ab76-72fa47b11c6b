# Pyron Meta-Repo: Submodules Usage

## Cloning All Sub-Repositories

This project uses Git submodules to manage its sub-repositories. To clone the main repository along with all submodules, use the following command:

```sh
git clone --recurse-submodules <main-repo-url>
```

If you have already cloned the repository without submodules, you can initialize and update them with:

```sh
git submodule update --init --recursive
```

This will automatically clone the following sub-repositories into their respective folders:

- pyron-webhook
- pyron-mvp
- PyRon-webApp

Each sub-repository can be managed independently. For more information, see the `.gitmodules` file.
