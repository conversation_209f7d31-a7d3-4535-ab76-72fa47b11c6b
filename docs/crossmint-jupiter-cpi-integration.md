# Crossmint MPC with Jupiter CPI Integration

## Overview

Cross Program Invocation (CPI) with Jupiter allows you to call Jupiter's swap functionality directly from your own Solana program or through advanced transaction building. This guide shows how to use Crossmint MPC wallets to execute Jupiter CPI calls for optimal trading performance.

---

## Why Use Jupiter CPI with Crossmint MPC?

### Advantages of CPI Method
- ✅ **Direct program calls**: No API dependencies, pure on-chain execution
- ✅ **Atomic transactions**: Multiple operations in single transaction
- ✅ **Lower latency**: No external API calls during execution
- ✅ **Composability**: Combine with other DeFi protocols
- ✅ **MEV protection**: Built into Jupiter's on-chain routing

### Crossmint MPC Benefits
- ✅ **No key reconstruction**: Secure threshold signatures
- ✅ **Production ready**: Enterprise-grade infrastructure
- ✅ **Solana optimized**: Native support for complex transactions

---

## Implementation Methods

### Method 1: Using Jupiter Swap Instructions API with MPC

```typescript
import { CrossmintWallets, SolanaMPCWallet } from '@crossmint/client-sdk-wallets';
import { 
  Connection, 
  PublicKey, 
  Transaction, 
  TransactionInstruction,
  ComputeBudgetProgram 
} from '@solana/web3.js';

class JupiterCPIService {
  private connection: Connection;
  private jupiterApiUrl = 'https://quote-api.jup.ag/v6';
  
  constructor(rpcUrl: string) {
    this.connection = new Connection(rpcUrl, 'confirmed');
  }
  
  // Get Jupiter swap instructions for CPI
  async getSwapInstructions(params: {
    inputMint: string;
    outputMint: string;
    amount: number;
    userPublicKey: string;
    slippageBps?: number;
  }) {
    const { inputMint, outputMint, amount, userPublicKey, slippageBps = 50 } = params;
    
    // 1. Get quote from Jupiter
    const quoteUrl = new URL(`${this.jupiterApiUrl}/quote`);
    quoteUrl.searchParams.set('inputMint', inputMint);
    quoteUrl.searchParams.set('outputMint', outputMint);
    quoteUrl.searchParams.set('amount', amount.toString());
    quoteUrl.searchParams.set('slippageBps', slippageBps.toString());
    
    const quoteResponse = await fetch(quoteUrl.toString());
    const quote = await quoteResponse.json();
    
    // 2. Get swap instructions (not full transaction)
    const swapResponse = await fetch(`${this.jupiterApiUrl}/swap-instructions`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        quoteResponse: quote,
        userPublicKey,
        wrapAndUnwrapSol: true,
        computeUnitPriceMicroLamports: 'auto',
        dynamicComputeUnitLimit: true,
      }),
    });
    
    const swapInstructionsResponse = await swapResponse.json();
    
    return {
      quote,
      instructions: swapInstructionsResponse,
    };
  }
  
  // Build custom transaction with Jupiter CPI
  async buildJupiterCPITransaction(
    mpcWallet: SolanaMPCWallet,
    swapParams: {
      inputMint: string;
      outputMint: string;
      amount: number;
      slippageBps?: number;
    },
    additionalInstructions?: TransactionInstruction[]
  ): Promise<Transaction> {
    const userPublicKey = mpcWallet.getPublicKey();
    
    // 1. Get Jupiter swap instructions
    const { instructions: swapInstructions } = await this.getSwapInstructions({
      ...swapParams,
      userPublicKey: userPublicKey.toString(),
    });
    
    // 2. Create transaction
    const transaction = new Transaction();
    
    // 3. Add compute budget instructions (important for CPI)
    if (swapInstructions.computeBudgetInstructions) {
      swapInstructions.computeBudgetInstructions.forEach((ix: any) => {
        transaction.add(this.deserializeInstruction(ix));
      });
    }
    
    // 4. Add setup instructions (token accounts, etc.)
    if (swapInstructions.setupInstructions) {
      swapInstructions.setupInstructions.forEach((ix: any) => {
        transaction.add(this.deserializeInstruction(ix));
      });
    }
    
    // 5. Add any custom instructions before swap
    if (additionalInstructions) {
      additionalInstructions.forEach(ix => transaction.add(ix));
    }
    
    // 6. Add Jupiter swap instruction (the actual CPI)
    transaction.add(this.deserializeInstruction(swapInstructions.swapInstruction));
    
    // 7. Add cleanup instructions
    if (swapInstructions.cleanupInstructions) {
      swapInstructions.cleanupInstructions.forEach((ix: any) => {
        transaction.add(this.deserializeInstruction(ix));
      });
    }
    
    // 8. Set recent blockhash
    const { blockhash } = await this.connection.getLatestBlockhash();
    transaction.recentBlockhash = blockhash;
    transaction.feePayer = userPublicKey;
    
    return transaction;
  }
  
  // Execute Jupiter CPI with MPC wallet
  async executeJupiterCPI(
    mpcWallet: SolanaMPCWallet,
    swapParams: {
      inputMint: string;
      outputMint: string;
      amount: number;
      slippageBps?: number;
    },
    additionalInstructions?: TransactionInstruction[]
  ): Promise<string> {
    try {
      // 1. Build transaction with CPI instructions
      const transaction = await this.buildJupiterCPITransaction(
        mpcWallet,
        swapParams,
        additionalInstructions
      );
      
      // 2. Serialize transaction for MPC wallet
      const serializedTransaction = transaction.serialize({
        requireAllSignatures: false,
        verifySignatures: false,
      });
      
      // 3. Execute with MPC (no key reconstruction)
      const txHash = await mpcWallet.sendTransaction({
        transaction: serializedTransaction.toString('base64'),
        options: {
          skipPreflight: false,
          commitment: 'confirmed',
          maxRetries: 3,
        },
      });
      
      console.log('Jupiter CPI executed:', {
        txHash,
        inputToken: swapParams.inputMint,
        outputToken: swapParams.outputMint,
        amount: swapParams.amount,
      });
      
      return txHash;
    } catch (error) {
      console.error('Jupiter CPI execution failed:', error);
      throw error;
    }
  }
  
  // Helper to deserialize instruction from API response
  private deserializeInstruction(instructionData: any): TransactionInstruction {
    return new TransactionInstruction({
      programId: new PublicKey(instructionData.programId),
      keys: instructionData.accounts.map((account: any) => ({
        pubkey: new PublicKey(account.pubkey),
        isSigner: account.isSigner,
        isWritable: account.isWritable,
      })),
      data: Buffer.from(instructionData.data, 'base64'),
    });
  }
}
```

### Method 2: Advanced CPI with Custom Program Logic

```typescript
// For more advanced use cases with custom program logic
class AdvancedJupiterCPI {
  private jupiterCPI: JupiterCPIService;
  
  constructor(rpcUrl: string) {
    this.jupiterCPI = new JupiterCPIService(rpcUrl);
  }
  
  // Execute complex trading strategy with multiple swaps
  async executeMultiSwapStrategy(
    mpcWallet: SolanaMPCWallet,
    strategy: {
      swaps: Array<{
        inputMint: string;
        outputMint: string;
        amount: number;
        slippageBps?: number;
      }>;
      riskManagement?: {
        maxSlippage: number;
        stopLoss?: number;
        takeProfit?: number;
      };
    }
  ): Promise<string[]> {
    const results: string[] = [];
    
    for (const swap of strategy.swaps) {
      // Add risk management instructions
      const riskInstructions = this.buildRiskManagementInstructions(
        strategy.riskManagement
      );
      
      // Execute swap with risk management
      const txHash = await this.jupiterCPI.executeJupiterCPI(
        mpcWallet,
        swap,
        riskInstructions
      );
      
      results.push(txHash);
      
      // Wait for confirmation before next swap
      await this.waitForConfirmation(txHash);
    }
    
    return results;
  }
  
  // Build risk management instructions
  private buildRiskManagementInstructions(
    riskParams?: {
      maxSlippage: number;
      stopLoss?: number;
      takeProfit?: number;
    }
  ): TransactionInstruction[] {
    const instructions: TransactionInstruction[] = [];
    
    if (riskParams) {
      // Add compute budget for complex operations
      instructions.push(
        ComputeBudgetProgram.setComputeUnitLimit({
          units: 1_400_000, // Max compute units
        })
      );
      
      // Add priority fee for faster execution
      instructions.push(
        ComputeBudgetProgram.setComputeUnitPrice({
          microLamports: 1000, // Adjust based on network conditions
        })
      );
      
      // Additional risk management logic would go here
      // (custom program instructions for stop-loss, take-profit, etc.)
    }
    
    return instructions;
  }
  
  // Wait for transaction confirmation
  private async waitForConfirmation(txHash: string): Promise<void> {
    const connection = new Connection(process.env.SOLANA_RPC_URL!, 'confirmed');
    
    try {
      await connection.confirmTransaction(txHash, 'confirmed');
    } catch (error) {
      console.error(`Failed to confirm transaction ${txHash}:`, error);
      throw error;
    }
  }
}
```

### Method 3: PyRon Integration with Jupiter CPI

```typescript
// Integration with PyRon's trading system
class PyRonJupiterCPITrader {
  private jupiterCPI: JupiterCPIService;
  private crossmint: CrossmintWallets;
  
  constructor() {
    this.jupiterCPI = new JupiterCPIService(process.env.SOLANA_RPC_URL!);
    this.crossmint = new CrossmintWallets({
      apiKey: process.env.CROSSMINT_API_KEY!,
      environment: 'production',
    });
  }
  
  // Execute trading signal with Jupiter CPI
  async executeTradeSignal(
    userId: string,
    signal: {
      action: 'buy' | 'sell';
      fromToken: string;
      toToken: string;
      amount: number;
      strategy?: 'market' | 'limit' | 'dca';
    }
  ): Promise<{ txHash: string; executionDetails: any }> {
    try {
      // 1. Get user's MPC wallet
      const mpcWallet = await this.crossmint.solana.mpc.get({ userId });
      
      // 2. Validate trade signal
      await this.validateTradeSignal(userId, signal);
      
      // 3. Prepare swap parameters
      const swapParams = {
        inputMint: signal.fromToken,
        outputMint: signal.toToken,
        amount: signal.amount,
        slippageBps: await this.calculateOptimalSlippage(signal),
      };
      
      // 4. Add strategy-specific instructions
      const strategyInstructions = await this.buildStrategyInstructions(signal.strategy);
      
      // 5. Execute Jupiter CPI
      const txHash = await this.jupiterCPI.executeJupiterCPI(
        mpcWallet,
        swapParams,
        strategyInstructions
      );
      
      // 6. Log execution details
      const executionDetails = await this.logTradeExecution(userId, signal, txHash);
      
      return { txHash, executionDetails };
    } catch (error) {
      console.error('Trade signal execution failed:', error);
      throw error;
    }
  }
  
  // Webhook handler for automated CPI trading
  async handleWebhookSignal(agentId: string, webhookData: any): Promise<any> {
    try {
      const agent = await this.getAgent(agentId);
      
      const signal = {
        action: webhookData.action,
        fromToken: webhookData.fromToken,
        toToken: webhookData.toToken,
        amount: webhookData.amount,
        strategy: agent.strategy,
      };
      
      const result = await this.executeTradeSignal(agent.userId, signal);
      
      return {
        success: true,
        agentId,
        txHash: result.txHash,
        executionDetails: result.executionDetails,
      };
    } catch (error) {
      return {
        success: false,
        agentId,
        error: error.message,
      };
    }
  }
  
  // Calculate optimal slippage based on market conditions
  private async calculateOptimalSlippage(signal: any): Promise<number> {
    // Implement dynamic slippage calculation
    // Consider: token volatility, trade size, market conditions
    const baseSlippage = 50; // 0.5%
    const volatilityMultiplier = await this.getTokenVolatility(signal.fromToken);
    
    return Math.min(baseSlippage * volatilityMultiplier, 300); // Max 3%
  }
  
  // Build strategy-specific instructions
  private async buildStrategyInstructions(strategy?: string): Promise<TransactionInstruction[]> {
    const instructions: TransactionInstruction[] = [];
    
    switch (strategy) {
      case 'limit':
        // Add limit order logic
        break;
      case 'dca':
        // Add DCA (Dollar Cost Averaging) logic
        break;
      case 'market':
      default:
        // Standard market order - no additional instructions
        break;
    }
    
    return instructions;
  }
  
  // Validate trade signal against user limits
  private async validateTradeSignal(userId: string, signal: any): Promise<void> {
    const userLimits = await this.getUserTradingLimits(userId);
    
    if (signal.amount > userLimits.maxTradeSize) {
      throw new Error(`Trade size exceeds limit: ${signal.amount} > ${userLimits.maxTradeSize}`);
    }
    
    const dailyVolume = await this.getDailyTradingVolume(userId);
    if (dailyVolume + signal.amount > userLimits.dailyVolumeLimit) {
      throw new Error('Daily trading volume limit exceeded');
    }
  }
}
```

### Method 4: Frontend Integration

```typescript
// React hook for Jupiter CPI trading
import { usePrivy } from '@privy-io/react-auth';
import { useState } from 'react';

export function useJupiterCPITrading() {
  const { user } = usePrivy();
  const [isTrading, setIsTrading] = useState(false);
  const [jupiterCPI] = useState(() => new PyRonJupiterCPITrader());
  
  const executeCPITrade = async (signal: {
    action: 'buy' | 'sell';
    fromToken: string;
    toToken: string;
    amount: number;
    strategy?: string;
  }) => {
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    setIsTrading(true);
    try {
      const result = await jupiterCPI.executeTradeSignal(user.id, signal);
      return result;
    } finally {
      setIsTrading(false);
    }
  };
  
  const executeMultiSwapStrategy = async (swaps: any[]) => {
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    setIsTrading(true);
    try {
      const mpcWallet = await crossmint.solana.mpc.get({ userId: user.id });
      const advancedCPI = new AdvancedJupiterCPI(process.env.NEXT_PUBLIC_SOLANA_RPC_URL!);
      
      const results = await advancedCPI.executeMultiSwapStrategy(mpcWallet, {
        swaps,
        riskManagement: {
          maxSlippage: 1.0, // 1%
          stopLoss: 0.05,   // 5%
          takeProfit: 0.15, // 15%
        },
      });
      
      return results;
    } finally {
      setIsTrading(false);
    }
  };
  
  return {
    executeCPITrade,
    executeMultiSwapStrategy,
    isTrading,
  };
}

// Advanced trading interface component
function JupiterCPITradingInterface() {
  const { executeCPITrade, executeMultiSwapStrategy, isTrading } = useJupiterCPITrading();
  
  const handleAdvancedTrade = async () => {
    try {
      // Execute complex multi-swap strategy
      const results = await executeMultiSwapStrategy([
        {
          inputMint: 'So11111111111111111111111111111111111111112', // SOL
          outputMint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
          amount: 1000000, // 0.001 SOL
          slippageBps: 50,
        },
        {
          inputMint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
          outputMint: '4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R', // RAY
          amount: 1000000, // 1 USDC
          slippageBps: 75,
        },
      ]);
      
      console.log('Multi-swap strategy executed:', results);
    } catch (error) {
      console.error('Advanced trade failed:', error);
    }
  };
  
  return (
    <div className="jupiter-cpi-interface">
      <h3>Jupiter CPI Trading</h3>
      
      <button 
        onClick={handleAdvancedTrade}
        disabled={isTrading}
        className="advanced-trade-btn"
      >
        {isTrading ? 'Executing Strategy...' : 'Execute Multi-Swap Strategy'}
      </button>
    </div>
  );
}
```

---

## Key Benefits of Jupiter CPI with Crossmint MPC

### Performance Advantages
- ✅ **Lower latency**: Direct on-chain calls, no API dependencies
- ✅ **Atomic execution**: Multiple operations in single transaction
- ✅ **Better MEV protection**: Built into Jupiter's on-chain routing
- ✅ **Composability**: Combine with other DeFi protocols

### Security Benefits
- ✅ **No key reconstruction**: Crossmint MPC threshold signatures
- ✅ **On-chain execution**: No external API attack vectors
- ✅ **Atomic transactions**: All-or-nothing execution guarantees
- ✅ **Verifiable execution**: All operations recorded on-chain

### Cost Efficiency
- ✅ **Optimized compute usage**: Direct program calls
- ✅ **Batch operations**: Multiple swaps in single transaction
- ✅ **Dynamic compute limits**: Automatic optimization
- ✅ **Priority fee optimization**: Smart fee calculation

This CPI approach with Crossmint MPC provides the most advanced and efficient way to integrate Jupiter swaps into PyRon's trading system.
