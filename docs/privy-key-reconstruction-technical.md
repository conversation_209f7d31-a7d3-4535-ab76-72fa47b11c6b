# Privy Key Reconstruction: Technical Deep Dive

## Overview

Key reconstruction in Privy's system is **entirely off-chain** and happens within the user's browser in a secure, sandboxed environment. The private key is never stored complete anywhere and only exists temporarily during the transaction signing process.

---

## Key Reconstruction Process (Off-Chain)

### 1. Initial Key Generation and Sharding

When a user first creates an embedded wallet, here's what happens:

```typescript
// This happens ONCE during wallet creation (off-chain)
async function createEmbeddedWallet() {
  // 1. Generate a new private key in secure environment
  const privateKey = generateSolanaPrivateKey(); // 64 bytes
  
  // 2. Apply Shamir's Secret Sharing (2-of-3 threshold)
  const shares = shamirSecretSharing.split(privateKey, {
    shares: 3,        // Total shares created
    threshold: 2      // Minimum shares needed to reconstruct
  });
  
  // 3. Distribute shares
  const deviceShare = shares[0];    // Stays on user's device
  const authShare = shares[1];      // Goes to Privy backend (encrypted)
  const recoveryShare = shares[2];  // Optional recovery mechanism
  
  // 4. IMMEDIATELY destroy the original private key
  securelyWipeMemory(privateKey);
  
  // 5. Store shares in their respective locations
  await storeDeviceShare(deviceShare);        // Browser local storage (encrypted)
  await storeAuthShare(authShare);            // Privy backend (encrypted)
  await storeRecoveryShare(recoveryShare);    // Optional backup
  
  return {
    walletAddress: derivePublicKey(privateKey), // Public key derived before destruction
    success: true
  };
}
```

### 2. Transaction Signing Process (Off-Chain Reconstruction)

When a transaction needs to be signed, the key is temporarily reconstructed:

```typescript
// This happens EVERY TIME a transaction needs signing (off-chain)
async function signTransaction(transaction: Transaction) {
  // 1. Verify user authentication
  const authToken = await verifyUserAuthentication();
  if (!authToken.valid) {
    throw new Error('Authentication required');
  }
  
  // 2. Retrieve device share from local storage
  const deviceShare = await getDeviceShareFromLocalStorage();
  if (!deviceShare) {
    throw new Error('Device share not found');
  }
  
  // 3. Request auth share from Privy backend
  const authShare = await requestAuthShare(authToken);
  if (!authShare) {
    throw new Error('Auth share not accessible');
  }
  
  // 4. RECONSTRUCT private key in sandboxed environment
  const privateKey = shamirSecretSharing.combine([deviceShare, authShare]);
  
  // 5. Sign the transaction
  const signature = await signTransactionWithKey(transaction, privateKey);
  
  // 6. IMMEDIATELY destroy the reconstructed private key
  securelyWipeMemory(privateKey);
  
  return signature;
}
```

### 3. Secure Environment Implementation

The reconstruction happens in a sandboxed iFrame with strict security boundaries:

```typescript
// Sandboxed iFrame implementation
class SecureExecutionEnvironment {
  private iframe: HTMLIFrameElement;
  
  constructor() {
    // Create isolated iframe with strict CSP
    this.iframe = document.createElement('iframe');
    this.iframe.sandbox = 'allow-scripts'; // Minimal permissions
    this.iframe.src = 'https://secure.privy.io/signing-environment';
    
    // Content Security Policy prevents data exfiltration
    this.iframe.setAttribute('csp', 
      "default-src 'none'; script-src 'self'; connect-src 'none';"
    );
  }
  
  async reconstructAndSign(deviceShare: string, authShare: string, transaction: Transaction) {
    return new Promise((resolve, reject) => {
      // Send shares to secure environment
      this.iframe.contentWindow.postMessage({
        action: 'SIGN_TRANSACTION',
        deviceShare,
        authShare,
        transaction
      }, 'https://secure.privy.io');
      
      // Listen for signed transaction
      window.addEventListener('message', (event) => {
        if (event.origin !== 'https://secure.privy.io') return;
        
        if (event.data.action === 'TRANSACTION_SIGNED') {
          resolve(event.data.signature);
        } else if (event.data.action === 'SIGNING_ERROR') {
          reject(new Error(event.data.error));
        }
      });
    });
  }
}
```

---

## Technical Details: Shamir's Secret Sharing

### How It Works

Shamir's Secret Sharing is a cryptographic algorithm that splits a secret into multiple parts:

```typescript
// Mathematical foundation (simplified)
class ShamirSecretSharing {
  // Split a secret into n shares with k threshold
  static split(secret: Uint8Array, options: { shares: number, threshold: number }) {
    const { shares, threshold } = options;
    
    // 1. Convert secret to polynomial coefficients
    const coefficients = [secret, ...generateRandomCoefficients(threshold - 1)];
    
    // 2. Evaluate polynomial at different points to create shares
    const sharePoints = [];
    for (let i = 1; i <= shares; i++) {
      const y = evaluatePolynomial(coefficients, i);
      sharePoints.push({ x: i, y });
    }
    
    return sharePoints;
  }
  
  // Reconstruct secret from threshold number of shares
  static combine(shares: Array<{ x: number, y: Uint8Array }>) {
    // Use Lagrange interpolation to reconstruct the secret
    return lagrangeInterpolation(shares, 0); // Evaluate at x=0 to get original secret
  }
}
```

### Security Properties

1. **Threshold Security**: Need exactly 2 out of 3 shares to reconstruct
2. **Information Theoretic Security**: Individual shares reveal nothing about the key
3. **Perfect Secrecy**: Even with quantum computers, single shares are useless

---

## Off-Chain vs On-Chain Breakdown

### Off-Chain Operations (Private/Secure)

**Key Generation**:
- ✅ Private key generation
- ✅ Shamir's Secret Sharing
- ✅ Share distribution
- ✅ Original key destruction

**Key Reconstruction**:
- ✅ Share retrieval and validation
- ✅ Temporary key reconstruction
- ✅ Transaction signing
- ✅ Immediate key destruction

**Authentication**:
- ✅ User authentication (email/social/passkey)
- ✅ JWT token generation and validation
- ✅ Auth share access control

### On-Chain Operations (Public/Transparent)

**Transaction Broadcasting**:
- ✅ Signed transaction submission to Solana
- ✅ Transaction confirmation and finality
- ✅ Public ledger recording

**What's NOT on-chain**:
- ❌ Private keys (never touch the blockchain)
- ❌ Key shares (stored off-chain only)
- ❌ Authentication data (handled off-chain)
- ❌ Reconstruction process (happens in browser)

---

## Security Guarantees

### 1. No Single Point of Failure

```
Device Share (User's Browser) + Auth Share (Privy Backend) = Private Key
```

- **Privy alone cannot access funds**: They only have 1 of 2 required shares
- **Device alone cannot access funds**: Missing the auth share
- **Attacker needs both**: Must compromise both user device AND Privy backend

### 2. Temporal Security

```typescript
// Key lifecycle during signing
const keyLifecycle = {
  beforeSigning: 'Key does not exist (only shares exist)',
  duringSigning: 'Key exists for ~100ms in sandboxed environment',
  afterSigning: 'Key immediately destroyed, only signature remains'
};
```

### 3. Isolation Security

```typescript
// Sandboxed environment properties
const securityBoundaries = {
  networkAccess: 'Blocked - cannot send data externally',
  localStorage: 'Isolated - cannot access main app storage',
  parentAccess: 'Restricted - limited postMessage communication',
  memoryAccess: 'Isolated - cannot access main app memory'
};
```

---

## Comparison with Traditional Approaches

### Traditional Custodial (Centralized)
```
User → Platform holds private keys → Platform signs transactions
❌ Platform has full control of funds
❌ Single point of failure
❌ Regulatory and security risks
```

### Traditional Non-Custodial (Wallet Extensions)
```
User → Wallet extension holds keys → User signs each transaction
✅ User controls funds
❌ Complex UX for non-crypto users
❌ Requires wallet installation and seed phrase management
```

### Privy Non-Custodial (Embedded)
```
User → Shares distributed → Temporary reconstruction → Signing → Immediate destruction
✅ User controls funds (via device share)
✅ Simple UX (email/social login)
✅ No single point of failure
✅ Enterprise-grade security
```

---

## Implementation in PyRon

### Frontend Integration

```typescript
// PyRon transaction signing with Privy
async function executePyRonTrade(tradeParams: TradeParams) {
  try {
    // 1. Prepare Drift transaction
    const transaction = await prepareDriftTransaction(tradeParams);
    
    // 2. Request signature from Privy (off-chain reconstruction)
    const signature = await privy.signTransaction(transaction);
    
    // 3. Submit signed transaction to Solana (on-chain)
    const txHash = await connection.sendRawTransaction(signature);
    
    // 4. Confirm transaction
    await connection.confirmTransaction(txHash);
    
    return { success: true, txHash };
  } catch (error) {
    console.error('Trade execution failed:', error);
    throw error;
  }
}
```

### Security Monitoring

```typescript
// Security event logging (off-chain)
const securityEvents = {
  keyReconstructionAttempt: 'Log every reconstruction attempt',
  authShareAccess: 'Log all auth share requests',
  deviceShareAccess: 'Log device share usage',
  failedAuthentication: 'Log failed auth attempts',
  suspiciousActivity: 'Flag unusual patterns'
};
```

---

## Conclusion

**Key reconstruction in Privy is entirely off-chain** and provides the following guarantees:

1. **Private keys never exist on any server** - only temporarily in user's browser
2. **No single entity can access funds** - requires both device and auth shares
3. **Reconstruction is temporary** - keys exist only during signing (~100ms)
4. **Sandboxed execution** - isolated environment prevents data exfiltration
5. **Immediate destruction** - keys are wiped from memory after signing

This approach combines the security of non-custodial systems with the UX of traditional web applications, making it ideal for PyRon's goal of mainstream adoption while maintaining user fund control.
