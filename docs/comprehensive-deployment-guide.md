# PyRon Comprehensive Deployment Guide

## Table of Contents
1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Environment Setup](#environment-setup)
4. [Local Development Deployment](#local-development-deployment)
5. [Staging Deployment](#staging-deployment)
6. [Production Deployment](#production-deployment)
7. [Docker Deployment](#docker-deployment)
8. [Monitoring and Maintenance](#monitoring-and-maintenance)
9. [Troubleshooting](#troubleshooting)
10. [Security Checklist](#security-checklist)

## Overview

PyRon is a multi-service Solana trading application consisting of:
- **pyron-mvp**: Backend API service (Port 3000)
- **pyron-webhook**: Webhook processing service (Port 3004)
- **PyRon-webApp**: Frontend React application (Port 8080)

All services are containerized with Docker and orchestrated using Docker Compose.

## Prerequisites

### System Requirements
- **OS**: Linux (Ubuntu 20.04+ recommended)
- **RAM**: Minimum 4GB, Recommended 8GB+
- **Storage**: Minimum 20GB free space
- **CPU**: 2+ cores recommended

### Software Dependencies
```bash
# Install Docker and Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Install Node.js (for local development)
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Git
sudo apt-get update && sudo apt-get install -y git
```

### External Services
- **MongoDB**: Database for user data and trading history
- **Redis**: Queue system for webhook processing
- **Solana RPC**: Blockchain connectivity

## Environment Setup

### 1. Clone Repository
```bash
git clone <your-repository-url>
cd pyron-meta-repo
```

### 2. Environment Files Configuration

Create the following environment files based on the templates:

#### pyron-mvp Environment Files
```bash
# Create environment files
cp pyron-mvp/.env.auth.example pyron-mvp/.env.auth
cp pyron-mvp/.env.database.example pyron-mvp/.env.database
cp pyron-mvp/.env.core.example pyron-mvp/.env.core
cp pyron-mvp/.env.blockchain.example pyron-mvp/.env.blockchain
cp pyron-mvp/.env.wallet.example pyron-mvp/.env.wallet
```

#### pyron-webhook Environment Files
```bash
# Create environment files
cp pyron-webhook/.env.admin.example pyron-webhook/.env.admin
cp pyron-webhook/.env.database.example pyron-webhook/.env.database
cp pyron-webhook/.env.server.example pyron-webhook/.env.server
cp pyron-webhook/.env.security.example pyron-webhook/.env.security
```

#### PyRon-webApp Environment File
```bash
# Create environment file
cp PyRon-webApp/.env.example PyRon-webApp/.env
```

### 3. Configure Environment Variables

Edit each environment file with your specific values. See `docs/environment-variables.md` for detailed configuration.

## Local Development Deployment

### Option 1: Docker Compose (Recommended)
```bash
# Build and start all services
docker-compose up --build

# Run in background
docker-compose up -d --build

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Option 2: Individual Service Development
```bash
# Terminal 1: Start pyron-mvp
cd pyron-mvp
npm install
npm start

# Terminal 2: Start pyron-webhook
cd pyron-webhook
npm install
npm start

# Terminal 3: Start PyRon-webApp
cd PyRon-webApp
npm install
npm run dev
```

## Staging Deployment

### Using SSH to Staging Server
```bash
# Connect to staging server
ssh user@staging-pyron

# Navigate to application directory
cd /opt/pyron

# Pull latest changes
git pull origin main

# Update environment files if needed
# Edit .env files as necessary

# Rebuild and restart services
docker-compose down
docker-compose up --build -d

# Verify deployment
docker-compose ps
docker-compose logs
```

### Staging Verification
```bash
# Test API endpoints
curl http://staging-pyron:3000/health
curl http://staging-pyron:3004/health

# Test frontend
curl http://staging-pyron:8080
```

## Production Deployment

### 1. Server Setup
```bash
# Create dedicated user
sudo useradd -r -m -s /bin/bash pyron-service
sudo mkdir -p /opt/pyron
sudo chown pyron-service:pyron-service /opt/pyron

# Switch to service user
sudo -u pyron-service -i
cd /opt/pyron
```

### 2. Application Deployment
```bash
# Clone repository
git clone <your-repository-url> .

# Set up environment files
# Copy and configure all .env files

# Build and start services
docker-compose -f docker-compose.yml up --build -d
```

### 3. Production Configuration
```bash
# Enable production mode in environment files
# Set NODE_ENV=production in all .env files

# Configure reverse proxy (Nginx example)
sudo apt install nginx
sudo nano /etc/nginx/sites-available/pyron
```

#### Nginx Configuration Example
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location /api/ {
        proxy_pass http://localhost:3000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location /webhook/ {
        proxy_pass http://localhost:3004/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location / {
        proxy_pass http://localhost:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## Docker Deployment

### Individual Service Builds
```bash
# Build individual services
docker build -t pyron-mvp:latest ./pyron-mvp
docker build -t pyron-webhook:latest ./pyron-webhook
docker build -t pyron-webapp:latest ./PyRon-webApp
```

### Docker Compose Commands
```bash
# Start services
docker-compose up -d

# View running containers
docker-compose ps

# View logs
docker-compose logs -f [service-name]

# Restart specific service
docker-compose restart pyron-mvp

# Update and restart
docker-compose pull
docker-compose up -d

# Clean up
docker-compose down
docker system prune -f
```

### Container Health Checks
```bash
# Check container status
docker ps

# Check container logs
docker logs pyron-mvp
docker logs pyron-webhook
docker logs pyron-webapp

# Execute commands in container
docker exec -it pyron-mvp /bin/sh
```

## Monitoring and Maintenance

### Log Management
```bash
# View application logs
docker-compose logs -f --tail=100

# Service-specific logs
docker-compose logs pyron-webhook

# System logs
sudo journalctl -u docker
```

### Performance Monitoring
```bash
# Container resource usage
docker stats

# System resources
htop
df -h
free -m
```

### Database Maintenance
```bash
# MongoDB backup
mongodump --host localhost --port 27017 --db pyron --out /backup/

# Redis monitoring
redis-cli info
redis-cli monitor
```

### Updates and Patches
```bash
# Update application
git pull origin main
docker-compose up --build -d

# Update system packages
sudo apt update && sudo apt upgrade -y

# Update Docker images
docker-compose pull
docker-compose up -d
```

## Troubleshooting

### Common Issues

#### 1. Port Conflicts
```bash
# Check port usage
sudo netstat -tulpn | grep :3000
sudo netstat -tulpn | grep :3004
sudo netstat -tulpn | grep :8080

# Kill processes using ports
sudo kill -9 $(sudo lsof -t -i:3000)
```

#### 2. Docker Issues
```bash
# Restart Docker service
sudo systemctl restart docker

# Clean Docker system
docker system prune -a -f

# Remove all containers and rebuild
docker-compose down --volumes --remove-orphans
docker-compose up --build -d
```

#### 3. Environment Variable Issues
```bash
# Verify environment files exist
ls -la pyron-mvp/.env.*
ls -la pyron-webhook/.env.*
ls -la PyRon-webApp/.env

# Check environment loading in containers
docker exec pyron-mvp env | grep MONGO
docker exec pyron-webhook env | grep REDIS
```

#### 4. Database Connection Issues
```bash
# Test MongoDB connection
mongo --host localhost --port 27017

# Test Redis connection
redis-cli ping

# Check container networking
docker network ls
docker network inspect pyron-meta-repo_default
```

#### 5. Build Failures
```bash
# Clear npm cache
docker exec pyron-mvp npm cache clean --force

# Rebuild with no cache
docker-compose build --no-cache

# Check Dockerfile syntax
docker build --no-cache -t test ./pyron-mvp
```

### Log Analysis
```bash
# Application error logs
docker-compose logs pyron-mvp | grep ERROR
docker-compose logs pyron-webhook | grep ERROR

# System logs
sudo journalctl -u docker --since "1 hour ago"

# Container resource issues
docker stats --no-stream
```

### Performance Issues
```bash
# Check Redis queue status
docker exec pyron-webhook node monitor-redis.js

# Monitor database performance
docker exec pyron-mvp mongo --eval "db.stats()"

# Check disk space
df -h
docker system df
```

## Security Checklist

### Pre-Deployment Security
- [ ] All environment files configured with secure values
- [ ] No secrets committed to version control
- [ ] Strong passwords for database connections
- [ ] Firewall rules configured properly
- [ ] SSL/TLS certificates installed
- [ ] Security headers configured in web server

### Runtime Security
- [ ] Services running as non-root users
- [ ] Container security scanning completed
- [ ] Network segmentation implemented
- [ ] Regular security updates applied
- [ ] Monitoring and alerting configured
- [ ] Backup and recovery procedures tested

### Access Control
- [ ] SSH key-based authentication only
- [ ] VPN access for sensitive operations
- [ ] Role-based access control implemented
- [ ] Audit logging enabled
- [ ] Regular access reviews conducted

### Data Protection
- [ ] Database encryption at rest
- [ ] Encrypted communication channels
- [ ] Regular automated backups
- [ ] Backup encryption and testing
- [ ] Data retention policies implemented

## Quick Reference Commands

### Daily Operations
```bash
# Check service status
docker-compose ps

# View recent logs
docker-compose logs --tail=50 -f

# Restart all services
docker-compose restart

# Update and restart
git pull && docker-compose up -d --build
```

### Emergency Procedures
```bash
# Stop all services immediately
docker-compose down

# Emergency restart
docker-compose down && docker-compose up -d

# Rollback to previous version
git checkout HEAD~1 && docker-compose up -d --build

# Check system resources
free -m && df -h && docker stats --no-stream
```

### Backup Commands
```bash
# Backup environment files
tar -czf env-backup-$(date +%Y%m%d).tar.gz */.env*

# Backup application data
docker exec pyron-mvp mongodump --out /backup/$(date +%Y%m%d)

# Backup Redis data
docker exec pyron-webhook redis-cli BGSAVE
```

---

## Support and Maintenance

For additional support:
1. Check application logs first
2. Review this deployment guide
3. Consult the architecture documentation in `/docs`
4. Contact the development team with specific error messages and logs

Remember to always test deployments in staging before production!
