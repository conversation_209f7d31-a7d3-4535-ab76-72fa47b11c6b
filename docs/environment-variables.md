# Environment Variables Documentation for PyRon Project

This document describes the environment variables required for running the PyRon system, including the Webhook Service, MVP backend, and WebApp frontend. Each service may require its own set of environment files. **Never commit secrets or private keys to version control.**

---

## 1. pyron-webhook

Create the following files in the `pyron-webhook` root directory:

### `.env.admin`
```
ADMIN_KEY=your_solana_private_key_base58
```

### `.env.database`
```
MONGO_USER=your_mongodb_username
MONGO_PASSWORD=your_mongodb_password
MONGO_HOST=your_mongodb_host
MONGO_DB=your_database_name
MONGO_PORT=27017
```

### `.env.server`
```
PORT=3000
REDIS_URL=redis://127.0.0.1:6379
WORKER_CONCURRENCY=5
RPC_URL=https://your-solana-rpc-endpoint
```

### `.env.security`
```
ALLOWED_ORIGINS=https://yourdomain.com,http://localhost:3000
ALLOWED_IPS=*************,************,*************,***********
NODE_ENV=production
```

---

## 2. pyron-mvp (Backend)

Create the following files in the `pyron-mvp` root directory:

### `.env.auth`
```
JWT_SECRET=your-secret-key
```

### `.env.database`
```
MONGO_HOST=localhost
MONGO_USER=your-username
MONGO_PASSWORD=your-password
MONGO_DB=your-database
MONGO_PORT=27017
```

### `.env.core` (optional)
```
PORT=3000
NODE_ENV=development
```

### `.env.wallet` (optional)
```
ADMIN_KEY=your-solana-private-key
```

### `.env.blockchain` (optional)
```
RPC_URL=https://api.devnet.solana.com
```

---

## 3. PyRon-webApp (Frontend)

For local development, create a `.env` file in the `PyRon-webApp` root directory. Vite requires variables to be prefixed with `VITE_`:

```
VITE_RPC_URL=https://api.devnet.solana.com
VITE_BASE_PYRON_URL=https://api.pyron.net
VITE_WEBHOOK_URL=https://webhook.pyron.net
VITE_ADMIN_KEY=your-admin-key
VITE_ENVIRONMENT=development
```

- **VITE_RPC_URL**: Solana RPC endpoint for frontend operations
- **VITE_BASE_PYRON_URL**: Base URL for backend API
- **VITE_WEBHOOK_URL**: Webhook endpoint for trading signals
- **VITE_ADMIN_KEY**: (If needed for admin features)
- **VITE_ENVIRONMENT**: Set to `development`, `staging`, or `production`

---

## 4. General Notes
- **Order of loading**: The backend loads `.env.database`, `.env.server`, `.env.security`, `.env.admin`, and then `.env` (for overrides).
- **Testing**: Test environments mock all variables and do not require real secrets.
- **Secrets**: Use encrypted secrets and key management in production.

For more details, see `docs/deployment-guide.md` and `docs/security-report.md`.
