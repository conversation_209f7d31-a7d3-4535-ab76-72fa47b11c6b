# Jupiter Spot Trading with Crossmint MPC Wallet

## Overview

This guide shows how to execute spot trades on <PERSON> using Crossmint's MPC wallet, providing optimal price discovery and execution for PyRon's trading operations.

---

## Setup and Dependencies

### Install Required Packages
```bash
# Crossmint SDK
npm install @crossmint/client-sdk-wallets

# Jupiter SDK
npm install @jup-ag/api @jup-ag/react-hook

# Solana Web3
npm install @solana/web3.js @solana/spl-token
```

### Environment Configuration
```typescript
// Environment variables
const config = {
  CROSSMINT_API_KEY: process.env.CROSSMINT_API_KEY,
  SOLANA_RPC_URL: process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com',
  JUPITER_API_URL: 'https://quote-api.jup.ag/v6',
};
```

---

## Core Implementation

### 1. Jupiter Trading Service

```typescript
import { CrossmintWallets, SolanaMPCWallet } from '@crossmint/client-sdk-wallets';
import { Connection, PublicKey, Transaction, VersionedTransaction } from '@solana/web3.js';

interface JupiterQuoteParams {
  inputMint: string;
  outputMint: string;
  amount: number;
  slippageBps?: number;
}

interface JupiterSwapParams extends JupiterQuoteParams {
  userPublicKey: string;
}

class JupiterTradingService {
  private connection: Connection;
  private jupiterApiUrl: string;
  
  constructor() {
    this.connection = new Connection(config.SOLANA_RPC_URL, 'confirmed');
    this.jupiterApiUrl = config.JUPITER_API_URL;
  }
  
  // Get best route for token swap
  async getQuote(params: JupiterQuoteParams) {
    const { inputMint, outputMint, amount, slippageBps = 50 } = params;
    
    const quoteUrl = new URL(`${this.jupiterApiUrl}/quote`);
    quoteUrl.searchParams.set('inputMint', inputMint);
    quoteUrl.searchParams.set('outputMint', outputMint);
    quoteUrl.searchParams.set('amount', amount.toString());
    quoteUrl.searchParams.set('slippageBps', slippageBps.toString());
    
    const response = await fetch(quoteUrl.toString());
    
    if (!response.ok) {
      throw new Error(`Jupiter quote failed: ${response.statusText}`);
    }
    
    return await response.json();
  }
  
  // Get swap transaction from Jupiter
  async getSwapTransaction(params: JupiterSwapParams) {
    const quote = await this.getQuote(params);
    
    const swapResponse = await fetch(`${this.jupiterApiUrl}/swap`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        quoteResponse: quote,
        userPublicKey: params.userPublicKey,
        wrapAndUnwrapSol: true,
        dynamicComputeUnitLimit: true,
        prioritizationFeeLamports: 'auto',
      }),
    });
    
    if (!swapResponse.ok) {
      throw new Error(`Jupiter swap transaction failed: ${swapResponse.statusText}`);
    }
    
    const { swapTransaction } = await swapResponse.json();
    return {
      transaction: swapTransaction,
      quote,
    };
  }
  
  // Execute spot trade using Crossmint MPC wallet
  async executeSpotTrade(
    mpcWallet: SolanaMPCWallet,
    params: JupiterSwapParams
  ): Promise<string> {
    try {
      // 1. Get swap transaction from Jupiter
      const { transaction: swapTransactionBase64, quote } = await this.getSwapTransaction(params);
      
      // 2. Deserialize the transaction
      const swapTransactionBuf = Buffer.from(swapTransactionBase64, 'base64');
      const transaction = VersionedTransaction.deserialize(swapTransactionBuf);
      
      // 3. Execute with MPC wallet (no key reconstruction)
      const txHash = await mpcWallet.sendTransaction({
        transaction: swapTransactionBase64,
        options: {
          skipPreflight: false,
          commitment: 'confirmed',
        },
      });
      
      console.log('Spot trade executed:', {
        txHash,
        inputToken: params.inputMint,
        outputToken: params.outputMint,
        inputAmount: params.amount,
        expectedOutput: quote.outAmount,
        route: quote.routePlan,
      });
      
      return txHash;
    } catch (error) {
      console.error('Spot trade execution failed:', error);
      throw error;
    }
  }
}
```

### 2. PyRon Trading Integration

```typescript
// Integration with PyRon's trading system
class PyRonJupiterTrader {
  private jupiterService: JupiterTradingService;
  private crossmint: CrossmintWallets;
  
  constructor() {
    this.jupiterService = new JupiterTradingService();
    this.crossmint = new CrossmintWallets({
      apiKey: config.CROSSMINT_API_KEY,
      environment: 'production', // or 'staging'
    });
  }
  
  // Execute spot trade for a PyRon user
  async executeUserSpotTrade(
    userId: string,
    tradeParams: {
      fromToken: string;
      toToken: string;
      amount: number;
      slippageTolerance?: number;
    }
  ): Promise<{ txHash: string; tradeDetails: any }> {
    try {
      // 1. Get user's MPC wallet
      const mpcWallet = await this.crossmint.solana.mpc.get({ userId });
      
      // 2. Prepare Jupiter swap parameters
      const swapParams: JupiterSwapParams = {
        inputMint: tradeParams.fromToken,
        outputMint: tradeParams.toToken,
        amount: tradeParams.amount,
        slippageBps: (tradeParams.slippageTolerance || 0.5) * 100, // Convert % to bps
        userPublicKey: mpcWallet.getAddress(),
      };
      
      // 3. Get quote for price validation
      const quote = await this.jupiterService.getQuote(swapParams);
      
      // 4. Validate trade (add your risk management here)
      await this.validateTrade(userId, swapParams, quote);
      
      // 5. Execute the trade
      const txHash = await this.jupiterService.executeSpotTrade(mpcWallet, swapParams);
      
      // 6. Log trade for PyRon records
      const tradeDetails = await this.logTrade(userId, swapParams, quote, txHash);
      
      return { txHash, tradeDetails };
    } catch (error) {
      console.error(`Spot trade failed for user ${userId}:`, error);
      throw error;
    }
  }
  
  // Validate trade against user limits and risk parameters
  private async validateTrade(userId: string, params: JupiterSwapParams, quote: any) {
    // Get user's trading limits
    const userLimits = await this.getUserTradingLimits(userId);
    
    // Check position size limits
    if (params.amount > userLimits.maxTradeSize) {
      throw new Error(`Trade size ${params.amount} exceeds limit ${userLimits.maxTradeSize}`);
    }
    
    // Check slippage tolerance
    const expectedSlippage = this.calculateSlippage(quote);
    if (expectedSlippage > userLimits.maxSlippage) {
      throw new Error(`Expected slippage ${expectedSlippage}% exceeds limit ${userLimits.maxSlippage}%`);
    }
    
    // Check daily trading volume
    const dailyVolume = await this.getDailyTradingVolume(userId);
    if (dailyVolume + params.amount > userLimits.dailyVolumeLimit) {
      throw new Error('Daily trading volume limit exceeded');
    }
  }
  
  // Calculate expected slippage from Jupiter quote
  private calculateSlippage(quote: any): number {
    const inputAmount = parseInt(quote.inAmount);
    const outputAmount = parseInt(quote.outAmount);
    const expectedOutput = parseInt(quote.otherAmountThreshold);
    
    return ((expectedOutput - outputAmount) / expectedOutput) * 100;
  }
  
  // Log trade execution for PyRon records
  private async logTrade(
    userId: string,
    params: JupiterSwapParams,
    quote: any,
    txHash: string
  ) {
    const tradeRecord = {
      userId,
      txHash,
      timestamp: new Date(),
      type: 'spot_trade',
      platform: 'jupiter',
      inputToken: params.inputMint,
      outputToken: params.outputMint,
      inputAmount: params.amount,
      expectedOutput: quote.outAmount,
      route: quote.routePlan,
      slippageBps: params.slippageBps,
      fees: quote.platformFee,
    };
    
    // Store in PyRon database
    await this.storeTradeRecord(tradeRecord);
    
    return tradeRecord;
  }
}
```

### 3. Frontend Integration

```typescript
// React hook for Jupiter trading with Crossmint
import { usePrivy } from '@privy-io/react-auth';
import { useState } from 'react';

export function useJupiterTrading() {
  const { user } = usePrivy();
  const [isTrading, setIsTrading] = useState(false);
  const [jupiterTrader] = useState(() => new PyRonJupiterTrader());
  
  const executeSpotTrade = async (tradeParams: {
    fromToken: string;
    toToken: string;
    amount: number;
    slippageTolerance?: number;
  }) => {
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    setIsTrading(true);
    try {
      const result = await jupiterTrader.executeUserSpotTrade(user.id, tradeParams);
      return result;
    } finally {
      setIsTrading(false);
    }
  };
  
  const getQuote = async (params: {
    fromToken: string;
    toToken: string;
    amount: number;
  }) => {
    const jupiterService = new JupiterTradingService();
    return await jupiterService.getQuote({
      inputMint: params.fromToken,
      outputMint: params.toToken,
      amount: params.amount,
    });
  };
  
  return {
    executeSpotTrade,
    getQuote,
    isTrading,
  };
}

// Trading component
function JupiterTradingInterface() {
  const { executeSpotTrade, getQuote, isTrading } = useJupiterTrading();
  const [quote, setQuote] = useState(null);
  const [tradeParams, setTradeParams] = useState({
    fromToken: 'So11111111111111111111111111111111111111112', // SOL
    toToken: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
    amount: 1000000, // 0.001 SOL (in lamports)
    slippageTolerance: 0.5,
  });
  
  const handleGetQuote = async () => {
    try {
      const quoteResult = await getQuote(tradeParams);
      setQuote(quoteResult);
    } catch (error) {
      console.error('Failed to get quote:', error);
    }
  };
  
  const handleExecuteTrade = async () => {
    try {
      const result = await executeSpotTrade(tradeParams);
      console.log('Trade executed:', result);
      // Update UI with success
    } catch (error) {
      console.error('Trade failed:', error);
      // Show error to user
    }
  };
  
  return (
    <div className="jupiter-trading-interface">
      <h3>Jupiter Spot Trading</h3>
      
      {/* Trade parameters form */}
      <div className="trade-form">
        <input
          type="text"
          placeholder="From Token Address"
          value={tradeParams.fromToken}
          onChange={(e) => setTradeParams({...tradeParams, fromToken: e.target.value})}
        />
        <input
          type="text"
          placeholder="To Token Address"
          value={tradeParams.toToken}
          onChange={(e) => setTradeParams({...tradeParams, toToken: e.target.value})}
        />
        <input
          type="number"
          placeholder="Amount"
          value={tradeParams.amount}
          onChange={(e) => setTradeParams({...tradeParams, amount: parseInt(e.target.value)})}
        />
        <input
          type="number"
          placeholder="Slippage %"
          value={tradeParams.slippageTolerance}
          onChange={(e) => setTradeParams({...tradeParams, slippageTolerance: parseFloat(e.target.value)})}
        />
      </div>
      
      {/* Quote section */}
      <div className="quote-section">
        <button onClick={handleGetQuote}>Get Quote</button>
        {quote && (
          <div className="quote-display">
            <p>Input: {quote.inAmount}</p>
            <p>Output: {quote.outAmount}</p>
            <p>Price Impact: {quote.priceImpactPct}%</p>
            <p>Route: {quote.routePlan?.length} hops</p>
          </div>
        )}
      </div>
      
      {/* Execute trade */}
      <button 
        onClick={handleExecuteTrade}
        disabled={isTrading || !quote}
        className="execute-trade-btn"
      >
        {isTrading ? 'Executing...' : 'Execute Trade'}
      </button>
    </div>
  );
}
```

### 4. Webhook Integration for Automated Trading

```typescript
// Webhook handler for automated Jupiter trades
app.post('/webhook/jupiter-trade/:agentId', async (req, res) => {
  try {
    const { agentId } = req.params;
    const { signal, fromToken, toToken, amount } = req.body;
    
    // Get trading agent
    const agent = await getAgent(agentId);
    
    // Validate signal
    if (!validateTradingSignal(signal, agent)) {
      return res.status(400).json({ error: 'Invalid trading signal' });
    }
    
    // Execute Jupiter spot trade
    const jupiterTrader = new PyRonJupiterTrader();
    const result = await jupiterTrader.executeUserSpotTrade(agent.userId, {
      fromToken,
      toToken,
      amount,
      slippageTolerance: agent.slippageTolerance,
    });
    
    res.json({
      success: true,
      txHash: result.txHash,
      tradeDetails: result.tradeDetails,
    });
  } catch (error) {
    console.error('Webhook Jupiter trade failed:', error);
    res.status(500).json({ error: error.message });
  }
});
```

---

## Common Token Addresses

```typescript
// Solana token mint addresses for Jupiter trading
export const COMMON_TOKENS = {
  SOL: 'So11111111111111111111111111111111111111112',
  USDC: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
  USDT: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
  RAY: '4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R',
  SRM: 'SRMuApVNdxXokk5GT7XD5cUUgXMBCoAz2LHeuAoKWRt',
  ORCA: 'orcaEKTdK7LKz57vaAYr9QeNsVEPfiu6QeMU1kektZE',
  MNGO: 'MangoCzJ36AjZyKwVj3VnYU4GTonjfVEnJmvvWaxLac',
  BTC: '9n4nbM75f5Ui33ZbPYXn59EwSgE8CGsHtAeTH5YFeJ9E', // Wrapped Bitcoin
  ETH: '2FPyTwcZLUg1MDrwsyoP4D6s1tM7hAkHYRjkNb5w6Pxk', // Wrapped Ethereum
};
```

---

## Error Handling and Best Practices

```typescript
// Comprehensive error handling
class JupiterTradingError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'JupiterTradingError';
  }
}

// Error handling wrapper
async function safeExecuteJupiterTrade<T>(
  operation: () => Promise<T>,
  context: string
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    if (error.message.includes('insufficient funds')) {
      throw new JupiterTradingError(
        'Insufficient balance for trade',
        'INSUFFICIENT_FUNDS',
        { context, originalError: error.message }
      );
    }
    
    if (error.message.includes('slippage')) {
      throw new JupiterTradingError(
        'Slippage tolerance exceeded',
        'SLIPPAGE_EXCEEDED',
        { context, originalError: error.message }
      );
    }
    
    if (error.message.includes('timeout')) {
      throw new JupiterTradingError(
        'Transaction timeout',
        'TRANSACTION_TIMEOUT',
        { context, originalError: error.message }
      );
    }
    
    // Re-throw unknown errors
    throw error;
  }
}
```

This implementation provides a complete solution for executing Jupiter spot trades using Crossmint's MPC wallet, with proper error handling, risk management, and integration with PyRon's trading system.
