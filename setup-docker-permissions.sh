#!/bin/bash

# Script to setup Docker permissions on the server
# This script will add the user to the docker group and restart the Docker service

SERVER_IP="************"
SERVER_USER="shuvo"
SSH_KEY_PATH="/home/<USER>/.ssh/id_rsa"

echo "Setting up Docker permissions on server..."

# Connect to server and setup Docker permissions
ssh -i "$SSH_KEY_PATH" "$SERVER_USER@$SERVER_IP" << 'EOF'
    echo "Current user: $(whoami)"
    echo "Current groups: $(groups)"
    
    # Check if docker group exists
    if getent group docker > /dev/null 2>&1; then
        echo "Docker group exists"
    else
        echo "Docker group does not exist, creating it..."
        sudo groupadd docker
    fi
    
    # Add user to docker group
    echo "Adding user to docker group..."
    sudo usermod -aG docker $USER
    
    # Change group ownership of docker socket
    echo "Setting docker socket permissions..."
    sudo chown root:docker /var/run/docker.sock
    sudo chmod 664 /var/run/docker.sock
    
    # Restart docker service
    echo "Restarting Docker service..."
    sudo systemctl restart docker
    
    echo "Docker setup completed. Please log out and log back in for changes to take effect."
EOF

echo "Docker permissions setup completed!"
echo "Now trying to test Docker access..."

# Test Docker access
ssh -i "$SSH_KEY_PATH" "$SERVER_USER@$SERVER_IP" "newgrp docker << 'DOCKERTEST'
echo 'Testing Docker access...'
docker --version
docker ps
echo 'Docker test completed'
DOCKERTEST"
